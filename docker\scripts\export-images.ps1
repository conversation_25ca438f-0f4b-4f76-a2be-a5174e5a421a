# 导出Docker镜像脚本

param(
    [switch]$NoCompress,
    [switch]$BackendOnly,
    [switch]$FrontendOnly
)

Write-Host "=== Export Docker Images ===" -ForegroundColor Green
if ($NoCompress) {
    Write-Host "Mode: No compression (faster export)" -ForegroundColor Yellow
}
Write-Host ""

# 创建导出目录 - 存放到docker路径下
# $scriptPath = $PSScriptRoot # Original line

# Robust way to get script's directory
$currentScriptFile = $MyInvocation.MyCommand.Path
if (-not $currentScriptFile) {
    Write-Error "Could not determine the current script file path via \$MyInvocation.MyCommand.Path."
    exit 1
}
$scriptPath = Split-Path $currentScriptFile -Parent 

if (-not $scriptPath) {
    Write-Error "Could not determine the script path from '$currentScriptFile'."
    exit 1
}

$dockerPath = Split-Path -Parent $scriptPath # This should go up one level from 'scripts' to 'docker'
if (-not $dockerPath) {
    Write-Error "Could not determine the parent path of the script directory '$scriptPath'."
    exit 1
}

$exportDir = Join-Path $dockerPath "docker-images" # This should be D:\DeskTop\web_ai_vision_app\docker\docker-images

# 确保 $dockerPath 存在，因为 Join-Path 即使在基础路径不存在时也会构造路径字符串
if (-not (Test-Path $dockerPath -PathType Container)) {
    Write-Error "Calculated parent docker path '$dockerPath' does not exist or is not a directory."
    Write-Host "This script expects to be in a 'scripts' subdirectory, and the 'docker-images' folder will be created alongside 'scripts'."
    Write-Host "Current script file: $currentScriptFile"
    Write-Host "Script directory (scripts): $scriptPath"
    exit 1
}

Write-Host "Script located at: $currentScriptFile" -ForegroundColor DarkGray
Write-Host "Script directory (where export-images.ps1 is): $scriptPath" -ForegroundColor DarkGray
Write-Host "Parent directory (expected 'docker' dir): $dockerPath" -ForegroundColor DarkGray
Write-Host "Target export directory: $exportDir" -ForegroundColor DarkGray
Write-Host ""


New-Item -ItemType Directory -Force -Path $exportDir | Out-Null

# 检查7zip是否可用
$use7zip = $false
$7zipPath = ""
$possiblePaths = @(
    "C:\Program Files\7-Zip\7z.exe",
    "C:\Program Files (x86)\7-Zip\7z.exe",
    "${env:ProgramFiles}\7-Zip\7z.exe",
    "${env:ProgramFiles(x86)}\7-Zip\7z.exe"
)

foreach ($path in $possiblePaths) {
    if (Test-Path $path -PathType Leaf) { # Ensure it's a file
        $7zipPath = $path
        $use7zip = $true
        Write-Host "Found 7-Zip at: $7zipPath" -ForegroundColor Gray
        break
    }
}

if (-not $use7zip) {
    Write-Host "7-Zip not found, keeping files uncompressed" -ForegroundColor Yellow
}

if ($NoCompress) {
    $use7zip = $false # Override if NoCompress is set
    Write-Host "Compression disabled by user (-NoCompress specified)" -ForegroundColor Yellow
}

# 导出前端镜像
if (-not $BackendOnly) {
    Write-Host ""
    Write-Host "Exporting frontend image (web_ai_vision_app-frontend:latest)..." -ForegroundColor Yellow
    $frontendTarPath = Join-Path $exportDir "frontend.tar"
    docker save web_ai_vision_app-frontend:latest -o $frontendTarPath
    
    if ($LASTEXITCODE -eq 0) {
        $frontendTarSize = (Get-Item $frontendTarPath).Length / 1MB
        Write-Host "Frontend tar size: $([math]::Round($frontendTarSize, 1)) MB" -ForegroundColor Gray

        if ($use7zip) {
            Write-Host "Compressing frontend image with 7-Zip..." -ForegroundColor Gray
            $frontendGzPath = "$frontendTarPath.gz"
            try {
                & $7zipPath a -tgzip $frontendGzPath $frontendTarPath -y | Out-Null # Added -y for overwrite
                if ($LASTEXITCODE -eq 0) {
                    Remove-Item $frontendTarPath -ErrorAction SilentlyContinue
                    $frontendSize = (Get-Item $frontendGzPath).Length / 1MB
                    Write-Host "✓ Frontend exported: $([math]::Round($frontendSize, 1)) MB (compressed to $frontendGzPath)" -ForegroundColor Green
                } else {
                    $frontendSize = $frontendTarSize
                    Write-Host "✓ Frontend exported: $([math]::Round($frontendSize, 1)) MB (7-Zip compression failed with code $LASTEXITCODE, kept uncompressed at $frontendTarPath)" -ForegroundColor Yellow
                }
            } catch {
                $frontendSize = $frontendTarSize
                Write-Host "✓ Frontend exported: $([math]::Round($frontendSize, 1)) MB (7-Zip compression threw an exception, kept uncompressed at $frontendTarPath)" -ForegroundColor Yellow
                Write-Warning "7-Zip Error: $($_.Exception.Message)"
            }
        } else {
            $frontendSize = $frontendTarSize
            Write-Host "✓ Frontend exported: $([math]::Round($frontendSize, 1)) MB (uncompressed at $frontendTarPath)" -ForegroundColor Green
        }
    } else {
        Write-Host "✗ Failed to export frontend image (docker save returned $LASTEXITCODE)" -ForegroundColor Red
    }
}

# 导出后端镜像
if (-not $FrontendOnly) {
    Write-Host ""
    Write-Host "Exporting backend image (web_ai_vision_app-backend:latest)..." -ForegroundColor Yellow
    $backendTarPath = Join-Path $exportDir "backend.tar"
    docker save web_ai_vision_app-backend:latest -o $backendTarPath

    if ($LASTEXITCODE -eq 0) {
        $backendTarSize = (Get-Item $backendTarPath).Length / 1MB
        Write-Host "Backend tar size: $([math]::Round($backendTarSize, 1)) MB" -ForegroundColor Gray

        if ($use7zip) {
            Write-Host "Compressing backend image with 7-Zip..." -ForegroundColor Gray
            $backendGzPath = "$backendTarPath.gz"
            try {
                & $7zipPath a -tgzip $backendGzPath $backendTarPath -y | Out-Null # Added -y for overwrite
                if ($LASTEXITCODE -eq 0) {
                    Remove-Item $backendTarPath -ErrorAction SilentlyContinue
                    $backendSize = (Get-Item $backendGzPath).Length / 1MB
                    Write-Host "✓ Backend exported: $([math]::Round($backendSize, 1)) MB (compressed to $backendGzPath)" -ForegroundColor Green
                } else {
                    $backendSize = $backendTarSize
                    Write-Host "✓ Backend exported: $([math]::Round($backendSize, 1)) MB (7-Zip compression failed with code $LASTEXITCODE, kept uncompressed at $backendTarPath)" -ForegroundColor Yellow
                }
            } catch {
                $backendSize = $backendTarSize
                Write-Host "✓ Backend exported: $([math]::Round($backendSize, 1)) MB (7-Zip compression threw an exception, kept uncompressed at $backendTarPath)" -ForegroundColor Yellow
                Write-Warning "7-Zip Error: $($_.Exception.Message)"
            }
        } else {
            $backendSize = $backendTarSize
            Write-Host "✓ Backend exported: $([math]::Round($backendSize, 1)) MB (uncompressed at $backendTarPath)" -ForegroundColor Green
        }
    } else {
        Write-Host "✗ Failed to export backend image (docker save returned $LASTEXITCODE)" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "=== Export Complete ===" -ForegroundColor Cyan
if (Test-Path $exportDir) {
    Write-Host "Files saved to: $exportDir" -ForegroundColor White
    Write-Host ""

    # 显示导出的文件
    Write-Host "Exported files:" -ForegroundColor Cyan
    Get-ChildItem $exportDir | ForEach-Object {
        $fileSize = [math]::Round($_.Length / 1MB, 1)
        Write-Host "  $($_.Name) - $fileSize MB" -ForegroundColor White
    }

    Write-Host ""
    Write-Host "Transfer to server and run:" -ForegroundColor Yellow

    # 检查文件格式并给出相应的说明
    $frontendFile = Get-ChildItem $exportDir -Filter "frontend.*" | Select-Object -First 1
    $backendFile = Get-ChildItem $exportDir -Filter "backend.*" | Select-Object -First 1
    
    $frontendIsGz = $frontendFile -and $frontendFile.Name -like "*.tar.gz"
    $backendIsGz = $backendFile -and $backendFile.Name -like "*.tar.gz"

    if ($frontendIsGz -or $backendIsGz) {
        Write-Host "  # Extract compressed files first (if they exist and are .gz):" -ForegroundColor Gray
        if ($frontendIsGz) {
            Write-Host "  tar -xzf $($frontendFile.Name)" -ForegroundColor Gray
        }
        if ($backendIsGz) {
            Write-Host "  tar -xzf $($backendFile.Name)" -ForegroundColor Gray
        }
    } else {
        Write-Host "  # Files appear to be uncompressed .tar archives, ready to load." -ForegroundColor Gray
    }

    Write-Host "  # Load images:" -ForegroundColor Gray
    if ($frontendFile) { Write-Host "  docker load -i frontend.tar" -ForegroundColor Gray }
    if ($backendFile) { Write-Host "  docker load -i backend.tar" -ForegroundColor Gray }
    Write-Host "  # Start services (example, adjust to your docker-compose.yml):" -ForegroundColor Gray
    Write-Host "  docker-compose up -d" -ForegroundColor Gray

} else {
    Write-Warning "Export directory '$exportDir' not found. No files were exported or listed."
}


Write-Host ""
if (-not $NoCompress -and -not $use7zip) { # Only show 7zip suggestion if not disabled and not found
    Write-Host "Note: Install 7-Zip for automatic compression to save space." -ForegroundColor Yellow
    Write-Host "Download from: https://www.7-zip.org/" -ForegroundColor Gray
}

Write-Host ""
Write-Host "Speed up options:" -ForegroundColor Yellow
Write-Host "  .\export-images.ps1 -NoCompress     # Skip compression (faster export)" -ForegroundColor Gray
Write-Host "  .\export-images.ps1 -BackendOnly    # Export backend only" -ForegroundColor Gray
Write-Host "  .\export-images.ps1 -FrontendOnly   # Export frontend only" -ForegroundColor Gray

if ($NoCompress -and $use7zip) { # If -NoCompress was used but 7zip IS available
     Write-Host ""
     Write-Host "Compression was skipped due to -NoCompress flag." -ForegroundColor Yellow
     Write-Host "File sizes are larger but export was faster." -ForegroundColor Gray
}