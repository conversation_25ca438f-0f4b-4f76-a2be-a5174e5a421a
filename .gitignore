# ================================
# AI Vision App - .gitignore
# ================================
#
# 本文件为 AI 视觉应用项目的完整 .gitignore 配置
# 涵盖：前端(React/TypeScript)、后端(Django/Python)、Docker容器化部署
#
# 项目结构：
# - Frontend/          React + TypeScript + Vite 前端
# - Backend_Django/    Django + AI模型 后端
# - docker/            Docker 容器化部署配置
# - docs/              项目文档
#
# 最后更新：2025-05-27
# ================================

# ================================
# Node.js / Frontend 相关
# ================================

# 依赖目录
node_modules/
Frontend/node_modules/

# 包管理器日志
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# 构建输出
dist/
Frontend/dist-ssr/

# Vite 相关
.vite/
.vite-cache/
*.local

# TypeScript 编译输出
*.tsbuildinfo

# Electron 构建输出
Frontend/release/
Frontend/dist-electron/

# ================================
# Python / Django 后端相关
# ================================

# Python 缓存文件
__pycache__/
*.py[cod]
*$py.class
*.pyc
*.pyo
*.pyd

# Django 特定文件
Backend_Django/db.sqlite3-journal
Backend_Django/**/__pycache__/
Backend_Django/**/*.pyc

# Python 虚拟环境
Backend_Django/env/
Backend_Django/.venv/
# Backend_Django/.env/
venv/
env/
.venv/
# .env/

# Python 分发/打包
Backend_Django/build/
Backend_Django/dist/
Backend_Django/*.egg-info/
Backend_Django/.eggs/

# 测试和覆盖率
Backend_Django/.pytest_cache/
Backend_Django/.coverage
Backend_Django/htmlcov/
Backend_Django/.tox/
Backend_Django/.mypy_cache/

Backend_Django/vision_app/__pycache__/

# ================================
# AI 模型文件
# ================================

# 忽略模型文件但保留目录结构（已在下方重新定义）

# 各种AI模型格式
*.pt
*.pth
*.onnx
*.pb
*.h5
*.weights
*.engine
*.mlmodel
*.mlpackage
*.torchscript
*.tflite
*.pdmodel
*.pdiparams

# ================================
# 媒体和数据文件
# ================================

# Django 媒体文件
Backend_Django/media/
Backend_Django/static/
Backend_Django/staticfiles/

# 数据持久化目录
data/
docker/data/

# 日志文件
*.log
logs/
Backend_Django/logs/

# ================================
# Docker 相关
# ================================

# Docker 镜像导出文件
docker/docker-images/
*.tar
*.tar.gz
*.tar.bz2
*.tar.xz

# Docker 数据持久化目录
docker/data/
data/

# Docker 热重载代码同步目录
docker/code-sync/

# Docker 运行时文件
docker/volumes/
docker/logs/

# Docker Compose 覆盖文件
docker-compose.override.yml
docker-compose.local.yml

# Docker 构建缓存
.docker/

# 容器运行时生成的文件
*.pid
*.sock

# ================================
# 开发工具和IDE配置
# ================================

# VSCode
.vscode/
!.vscode/extensions.json

# JetBrains IDEs
.idea/

# Visual Studio
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Sublime Text
*.sublime-project
*.sublime-workspace

# ================================
# 操作系统文件
# ================================

# macOS
.DS_Store
.AppleDouble
.LSOverride

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini

# Linux
*~

# ================================
# 环境变量和配置
# ================================

# 环境变量文件
# .env
# .env.*

# ================================
# 临时文件和缓存
# ================================

# 通用临时文件
*.tmp
*.temp
.tmp/
temp/

# 缓存目录
.cache/
.npm/
.yarn/

# ================================
# 文档和工具
# ================================

# draw.io 备份文件
./docs/draw.io/*.drawio.bkp

# Cursor AI 编辑器
.cursor/
.cursorignore

# 内存库
memory-bank/

# 旧的后端目录（已重命名）
Backend/

# ================================
# 项目特定忽略
# ================================

# 测试图片和数据
test_images/
test_data/
inference/
inference_results/
output/
train_data/

# 运行时生成的文件
runs/
wandb/
mlruns/

# 脚本运行产生的文件
*.out
*.err
*.result
*.report

# 备份文件
*.backup
*.bak
*.orig
*.save
*~

# 部署包和压缩文件
deployment-package/
server-deploy-package/
*.zip
*.rar
*.7z
!docker/scripts/*.ps1
!docker/scripts/*.bat
!docker/scripts/*.sh

# PowerShell 和批处理脚本日志
*.ps1.log
*.bat.log

# 同步和传输临时文件
sync-temp/
transfer-temp/
.sync/
rsync-temp/

# 性能和监控文件
*.prof
*.trace
*.perf

# 数据库备份
*.sql
*.dump
*.backup.db

# ================================
# 项目特定文件（新增）
# ================================

# 前端构建产物（保留源码，忽略构建结果）
Frontend/dist/
Frontend/build/

# 后端数据库文件（开发环境）
Backend_Django/db/
Backend_Django/db.sqlite3

# AI模型文件（大文件，通过LFS或其他方式管理）
Backend_Django/models/custom_models/*
!Backend_Django/models/custom_models/.gitkeep
Backend_Django/models/system_models/*
!Backend_Django/models/system_models/.gitkeep

# 示例图片库
Backend_Django/models/example_images/ai_restored/*
Backend_Django/models/example_images/barcode/*
Backend_Django/models/example_images/ocr/*

# 后端虚拟环境（巨大，不应提交）
Backend_Django/venv/

# 临时测试文件
Frontend/temp_test.js
Backend_Django/scripts/test_*.py

# 版本文档（保留，这是重要的项目文档）
# version.md

# 文档目录（保留，这是重要的项目文档）
# docs/

# issues
issues/

# ================================
# 开发和调试文件
# ================================

# 编辑器临时文件（已在上方定义）
# .cursor/
# .cursorignore

# 开发环境配置
# .env.local
# .env.development
# .env.production

# 调试和分析文件（已在上方定义）
*.coverage

# 测试覆盖率报告
coverage/
.nyc_output/

# ================================
# 系统和网络文件
# ================================

# Windows 系统文件（已在上方定义，这里补充）
$RECYCLE.BIN/

# Linux 系统文件（已在上方定义）
.nfs*