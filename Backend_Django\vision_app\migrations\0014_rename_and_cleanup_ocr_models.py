# Generated manually for OCR model renaming and cleanup

from django.db import migrations
from django.utils import timezone

def rename_and_cleanup_ocr_models(apps, schema_editor):
    """
    重命名OCR模型并删除general_ocr_ch模型
    """
    AIModel = apps.get_model('vision_app', 'AIModel')
    db_alias = schema_editor.connection.alias
    
    print("开始OCR模型重命名和清理...")
    
    # 1. 删除 general_ocr_ch 模型
    print("1. 删除 general_ocr_ch 模型...")
    deleted_count = AIModel.objects.using(db_alias).filter(
        name='general_ocr_ch',
        model_type='ocr',
        is_system_model=True
    ).delete()
    print(f"   删除了 {deleted_count[0]} 个 general_ocr_ch 模型记录")
    
    # 2. 重命名 vehicle_license_plate_cn -> license_plate_cn
    print("2. 重命名车牌识别模型: vehicle_license_plate_cn -> license_plate_cn")
    vehicle_models = AIModel.objects.using(db_alias).filter(
        name='vehicle_license_plate_cn',
        model_type='ocr',
        is_system_model=True
    )
    
    for model in vehicle_models:
        model.name = 'license_plate_cn'
        if model.ocr_role == 'detection':
            model.description = '中文车牌检测模型 (PaddleOCR)'
        elif model.ocr_role == 'recognition':
            model.description = '中文车牌识别模型 (PaddleOCR)'
        model.version = '1.0.0'
        model.save()
        print(f"   更新模型 ID {model.id}: {model.ocr_role} 角色")
    
    # 3. 重命名 identity_card_number_cn -> id_card_en
    print("3. 重命名证件号码识别模型: identity_card_number_cn -> id_card_en")
    id_card_models = AIModel.objects.using(db_alias).filter(
        name='identity_card_number_cn',
        model_type='ocr',
        is_system_model=True
    )

    for model in id_card_models:
        model.name = 'id_card_en'
        if model.ocr_role == 'detection':
            model.description = '证件号码检测模型 - 英文 (PaddleOCR)'
        elif model.ocr_role == 'recognition':
            model.description = '证件号码识别模型 - 英文 (PaddleOCR)'
        model.version = '1.0.0'
        model.save()
        print(f"   更新模型 ID {model.id}: {model.ocr_role} 角色")
    
    # 4. 重命名 general_ocr_mobile -> general_text_mobile_ch_en
    print("4. 重命名通用轻量级模型: general_ocr_mobile -> general_text_mobile_ch_en")
    mobile_models = AIModel.objects.using(db_alias).filter(
        name='general_ocr_mobile',
        model_type='ocr',
        is_system_model=True
    )

    for model in mobile_models:
        model.name = 'general_text_mobile_ch_en'
        if model.ocr_role == 'detection':
            model.description = 'PP-OCRv4 mobile 通用文字检测模型 - 轻量级（中英文）'
        elif model.ocr_role == 'recognition':
            model.description = 'PP-OCRv4 mobile 通用文字识别模型 - 轻量级（中英文）'
        model.version = '4.0.0'
        model.save()
        print(f"   更新模型 ID {model.id}: {model.ocr_role} 角色")
    
    print("OCR模型重命名和清理完成！")

def reverse_rename_and_cleanup_ocr_models(apps, schema_editor):
    """
    回滚操作：恢复原始命名
    """
    AIModel = apps.get_model('vision_app', 'AIModel')
    db_alias = schema_editor.connection.alias
    
    print("开始回滚OCR模型重命名...")
    
    # 1. 恢复 license_plate_cn -> vehicle_license_plate_cn
    vehicle_models = AIModel.objects.using(db_alias).filter(
        name='license_plate_cn',
        model_type='ocr',
        is_system_model=True
    )
    
    for model in vehicle_models:
        model.name = 'vehicle_license_plate_cn'
        if model.ocr_role == 'detection':
            model.description = 'Chinese vehicle license plate detection model (PaddleOCR)'
        elif model.ocr_role == 'recognition':
            model.description = 'Chinese vehicle license plate recognition model (PaddleOCR)'
        model.version = '1.0'
        model.save()
    
    # 2. 恢复 id_card_en -> identity_card_number_cn
    id_card_models = AIModel.objects.using(db_alias).filter(
        name='id_card_en',
        model_type='ocr',
        is_system_model=True
    )

    for model in id_card_models:
        model.name = 'identity_card_number_cn'
        if model.ocr_role == 'detection':
            model.description = 'Chinese identity card detection model (PaddleOCR)'
        elif model.ocr_role == 'recognition':
            model.description = 'Chinese identity card recognition model (PaddleOCR)'
        model.version = '1.0'
        model.save()
    
    # 3. 恢复 general_text_mobile_ch_en -> general_ocr_mobile
    mobile_models = AIModel.objects.using(db_alias).filter(
        name='general_text_mobile_ch_en',
        model_type='ocr',
        is_system_model=True
    )

    for model in mobile_models:
        model.name = 'general_ocr_mobile'
        if model.ocr_role == 'detection':
            model.description = 'PP-OCRv4 mobile detection model - 轻量级通用文字检测模型'
        elif model.ocr_role == 'recognition':
            model.description = 'PP-OCRv4 mobile recognition model - 轻量级通用文字识别模型'
        model.version = '4.0.0'
        model.save()
    
    # 注意：我们不恢复已删除的 general_ocr_ch 模型，因为这是有意的清理操作
    print("OCR模型重命名回滚完成！")

class Migration(migrations.Migration):

    dependencies = [
        ('vision_app', '0013_update_ai_restorer_model_v1017'),
    ]

    operations = [
        migrations.RunPython(
            rename_and_cleanup_ocr_models,
            reverse_rename_and_cleanup_ocr_models
        ),
    ]
