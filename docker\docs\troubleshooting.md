# 故障排除指南

本文档提供常见问题的诊断和解决方案。

---

## 🔍 **快速诊断工具**

### 自动化诊断脚本
```powershell
# Docker 环境全面检查
.\scripts\check\check-docker-env.ps1

# 网络配置检查
.\scripts\check\check-network.ps1

# 局域网访问检查
.\scripts\check\check-lan-access.ps1
```

### 手动状态检查
```powershell
# 查看容器状态
docker ps -a

# 查看镜像列表
docker images

# 查看网络配置
docker network ls

# 查看系统资源
docker system df
```

---

## ❌ **常见问题及解决方案**

### 🐳 **Docker 相关问题**

#### 问题：Docker Desktop 未启动
**症状**: 
```
error during connect: This error may indicate that the docker daemon is not running
```

**解决方案**:
```powershell
# 1. 启动 Docker Desktop
# 2. 等待 Docker 完全启动
# 3. 验证状态
docker version
docker info
```

#### 问题：镜像构建失败
**症状**: 
```
ERROR: failed to solve: process "/bin/sh -c npm run build" did not complete successfully
```

**解决方案**:
```powershell
# 1. 清理 Docker 缓存
docker system prune -a

# 2. 无缓存重新构建
.\scripts\build-images.ps1 -NoCache

# 3. 检查 Dockerfile 语法
docker build --no-cache -f Dockerfile.frontend .
```

#### 问题：容器启动失败
**症状**: 容器状态显示 `Exited (1)`

**解决方案**:
```powershell
# 1. 查看容器日志
docker logs ai-vision-backend
docker logs ai-vision-frontend

# 2. 检查端口占用
netstat -ano | findstr :8080
netstat -ano | findstr :8000

# 3. 重新启动容器
docker-compose restart
```

### 🌐 **网络访问问题**

#### 问题：无法访问 localhost:8080
**症状**: 浏览器显示"无法访问此网站"

**解决方案**:
```powershell
# 1. 检查容器状态
docker ps | findstr frontend

# 2. 检查端口映射
docker port ai-vision-frontend

# 3. 检查防火墙
.\scripts\setup-firewall.bat

# 4. 尝试其他地址
# http://127.0.0.1:8080
# http://你的IP地址:8080
```

#### 问题：局域网无法访问
**症状**: 其他设备无法通过 IP 地址访问

**解决方案**:
```powershell
# 1. 检查本机 IP
ipconfig | findstr IPv4

# 2. 配置防火墙
.\scripts\setup-firewall.bat

# 3. 检查网络配置
.\scripts\check\check-lan-access.ps1

# 4. 验证端口监听
netstat -ano | findstr :8080
```

#### 问题：API 请求失败 (404/502)
**症状**: 前端无法调用后端 API

**解决方案**:
```powershell
# 1. 检查后端容器状态
docker logs ai-vision-backend

# 2. 测试后端直接访问
curl http://localhost:8000/api/vision/models/

# 3. 检查 Nginx 配置
docker exec ai-vision-frontend cat /etc/nginx/nginx.conf

# 4. 重启服务
docker-compose restart
```

### 🔄 **热重载问题**

#### 问题：代码修改不生效
**症状**: 修改代码后页面没有更新

**解决方案**:
```powershell
# 1. 检查挂载目录
docker inspect ai-vision-backend | findstr Mounts

# 2. 验证文件同步
# 检查 code-sync 目录是否包含最新代码

# 3. 手动重启后端
docker-compose -f docker-compose.hotreload.yml restart backend

# 4. 前端重新构建
.\scripts\frontend-dev.ps1 -Mode build
.\scripts\frontend-dev.ps1 -Mode sync
```

#### 问题：前端热重载不工作
**症状**: 前端代码修改后需要手动刷新

**解决方案**:
```powershell
# 1. 检查前端开发服务器
.\scripts\frontend-dev.ps1 -Mode status

# 2. 重启前端开发服务器
.\scripts\frontend-dev.ps1 -Mode stop
.\scripts\frontend-dev.ps1 -Mode start

# 3. 检查端口冲突
netstat -ano | findstr :5173
```

### 💾 **数据和模型问题**

#### 问题：数据库连接失败
**症状**: 
```
django.db.utils.OperationalError: unable to open database file
```

**解决方案**:
```powershell
# 1. 检查数据目录权限
# 确保 ./data/db 目录存在且可写

# 2. 重新初始化数据库
docker exec ai-vision-backend python manage.py migrate

# 3. 检查挂载路径
docker inspect ai-vision-backend | findstr "/app/db"
```

#### 问题：AI 模型加载失败
**症状**: 
```
FileNotFoundError: [Errno 2] No such file or directory: '/app/models/...'
```

**解决方案**:
```powershell
# 1. 检查模型目录
ls ./data/models/

# 2. 确保模型文件存在
# 将模型文件复制到 ./data/models/system_models/

# 3. 重启后端容器
docker-compose restart backend
```

### 🔗 **跨电脑同步问题**

#### 问题：网络共享连接失败
**症状**: 
```
The network path was not found
```

**解决方案**:
```powershell
# A电脑（开发机）
# 1. 检查共享状态
.\scripts\setup-code-sharing.ps1 status

# 2. 重新设置共享
.\scripts\setup-code-sharing.ps1 remove
.\scripts\setup-code-sharing.ps1 setup

# B电脑（部署机）
# 3. 测试网络连接
ping A电脑IP
telnet A电脑IP 445
```

#### 问题：代码同步不及时
**症状**: A电脑修改代码，B电脑没有及时更新

**解决方案**:
```powershell
# 1. 检查同步脚本状态
# 确保 sync-from-dev.ps1 在运行

# 2. 手动触发同步
.\scripts\sync-from-dev.ps1 -DevMachineIP "A电脑IP" -Mode once

# 3. 调整同步间隔
.\scripts\sync-from-dev.ps1 -DevMachineIP "A电脑IP" -Mode watch -WatchInterval 3
```

---

## 🛠️ **高级故障排除**

### 完全重置环境
```powershell
# 1. 停止所有容器
docker-compose down
.\scripts\dev-workflow.ps1 -Mode stop-all

# 2. 清理 Docker 系统
docker system prune -a
docker volume prune

# 3. 重新构建和启动
.\scripts\build-images.ps1 -NoCache
.\scripts\deploy-hotreload.ps1 -Mode start
```

### 容器内部调试
```powershell
# 进入后端容器调试
docker exec -it ai-vision-backend bash

# 在容器内执行命令
python manage.py shell
python manage.py check
python manage.py migrate --plan

# 查看容器内文件
ls -la /app/
cat /app/logs/django.log
```

### 网络连通性测试
```powershell
# 测试容器间网络
docker exec ai-vision-frontend ping backend
docker exec ai-vision-backend ping frontend

# 测试外部网络
docker exec ai-vision-backend ping google.com
docker exec ai-vision-backend curl http://backend:8000/api/vision/models/
```

---

## 📊 **性能问题诊断**

### 资源使用监控
```powershell
# 查看容器资源使用
docker stats

# 查看系统资源
Get-Process | Sort-Object CPU -Descending | Select-Object -First 10
Get-Process | Sort-Object WorkingSet -Descending | Select-Object -First 10
```

### 日志分析
```powershell
# 查看详细日志
docker-compose logs --tail=100 backend
docker-compose logs --tail=100 frontend

# 查看错误日志
docker-compose logs backend | findstr ERROR
docker-compose logs backend | findstr CRITICAL
```

---

## 🔧 **预防性维护**

### 定期清理
```powershell
# 每周清理未使用的镜像
docker image prune

# 每月清理系统
docker system prune

# 清理构建缓存
docker builder prune
```

### 健康检查
```powershell
# 定期运行诊断脚本
.\scripts\check\check-docker-env.ps1
.\scripts\check\check-network.ps1

# 验证服务可用性
curl http://localhost:8080/health
curl http://localhost:8000/api/vision/models/
```

### 备份重要数据
```powershell
# 备份数据库
copy .\data\db\db.sqlite3 .\backup\

# 备份配置文件
copy .env .\backup\
copy docker-compose.yml .\backup\
```

---

## 📞 **获取更多帮助**

### 日志收集
当需要技术支持时，请收集以下信息：
```powershell
# 1. 系统信息
docker version
docker info
Get-ComputerInfo

# 2. 容器状态
docker ps -a
docker images

# 3. 服务日志
docker-compose logs > logs.txt

# 4. 网络配置
ipconfig /all
netstat -ano | findstr :8080
```

### 常用调试命令
```powershell
# 查看容器详细信息
docker inspect ai-vision-backend
docker inspect ai-vision-frontend

# 查看网络详细信息
docker network inspect ai-vision-network

# 查看挂载点信息
docker volume ls
docker volume inspect ai-vision_data
```
