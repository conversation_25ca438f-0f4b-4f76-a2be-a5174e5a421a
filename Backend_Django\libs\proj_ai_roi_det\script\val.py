from ultralytics.models.yolo.model import YOLO
from pathlib import Path
from ultralytics import settings

# 修改设置
root = Path(__file__).parent
settings.update({'datasets_dir': f"{Path(root).as_posix()}"})
settings.update({'weights_dir': f"{Path(root, 'weights').as_posix()}"})
settings.update({'runs_dir': f"{Path(root, 'runs').as_posix()}"})
print(settings)

# 主函数
if __name__ == "__main__":
    model = YOLO("../runs/AI_ROI_Det/V1.1.0.2/weights/best.pt")

     # 执行验证
    results = model.val(
        data="datasets/roi.yaml",                   # 数据集配置文件
        project="../runs/AI_ROI_Det/V1.1.0.2",      # 保存路径为指定的训练项目目录
        split="test",                               # 使用测试集
        imgsz=320,                                  # 输入图像大小
        save_json=True,                             # 保存为JSON格式
        conf=0.35                                   # 置信度阈值
    )