# 网络访问和配置指南

本文档详细介绍网络配置、局域网访问设置和跨设备访问方案。

---

## 🌐 **网络访问概览**

### 默认访问地址
```
本地访问:
- 前端应用: http://localhost:8080
- 后端API: http://localhost:8000
- 前端开发: http://localhost:5173 (开发模式)

局域网访问:
- 前端应用: http://你的IP:8080
- 后端API: http://你的IP:8000
- 示例: http://*************:8080
```

### 端口说明
| 端口 | 服务 | 用途 | 访问方式 |
|------|------|------|----------|
| 8080 | 前端 | 主要应用入口 | 浏览器访问 |
| 8000 | 后端 | API服务 | API调用 |
| 5173 | 前端开发 | Vite开发服务器 | 开发时访问 |

---

## 🔧 **网络配置**

### Docker 网络配置
```yaml
# docker-compose.yml 网络配置
services:
  backend:
    ports:
      - "8000:8000"  # 主机端口:容器端口
    networks:
      - ai-vision-network

  frontend:
    ports:
      - "8080:80"
    networks:
      - ai-vision-network

networks:
  ai-vision-network:
    driver: bridge
```

### 容器间通信
```yaml
# 容器内部网络
backend:
  hostname: backend
  # 其他容器可通过 http://backend:8000 访问

frontend:
  hostname: frontend
  # 其他容器可通过 http://frontend:80 访问
```

---

## 🏠 **局域网访问配置**

### 自动IP检测
脚本会自动检测并显示可用的访问地址：
```powershell
# 启动时自动显示
.\scripts\deploy-hotreload.ps1 -Mode start

# 输出示例:
# ✅ 服务启动成功！
# 📱 本地访问: http://localhost:8080
# 🌐 局域网访问: http://*************:8080
```

### 手动IP检测
```powershell
# 查看本机IP地址
ipconfig | findstr IPv4

# 或使用诊断脚本
.\scripts\check\check-lan-access.ps1
```

### 防火墙配置
```powershell
# 自动配置防火墙（需要管理员权限）
.\scripts\setup-firewall.bat

# 手动配置防火墙规则
netsh advfirewall firewall add rule name="Docker Frontend" dir=in action=allow protocol=TCP localport=8080
netsh advfirewall firewall add rule name="Docker Backend" dir=in action=allow protocol=TCP localport=8000
netsh advfirewall firewall add rule name="Frontend Dev" dir=in action=allow protocol=TCP localport=5173
```

---

## 📱 **多设备访问**

### 手机/平板访问
```
1. 确保设备连接到同一WiFi网络
2. 在浏览器中输入: http://电脑IP:8080
3. 示例: http://*************:8080
```

### 其他电脑访问
```
1. 确保在同一局域网内
2. 配置防火墙允许访问
3. 使用: http://服务器IP:8080
```

### 移动设备优化
前端应用已针对移动设备进行优化：
- 响应式设计
- 触摸友好的界面
- 移动端适配的文件上传

---

## 🔗 **跨电脑开发协作**

### A电脑（开发机）网络共享设置

#### 设置网络共享
```powershell
# 自动设置（推荐）
.\scripts\setup-code-sharing-simple.ps1 setup

# 手动设置步骤：
# 1. 创建共享用户
net user shareuser docker /add
net localgroup "Users" shareuser /add

# 2. 设置文件夹共享
# 右键项目文件夹 → 属性 → 共享 → 高级共享
# 共享名: web_ai_vision_app
# 权限: shareuser 完全控制
```

#### 查看共享状态
```powershell
# 检查共享状态
.\scripts\setup-code-sharing-simple.ps1 status

# 手动检查
net share
net user shareuser
```

### B电脑（部署机）连接设置

#### 连接到A电脑共享
```powershell
# 测试网络连接
ping A电脑IP
telnet A电脑IP 445

# 连接共享文件夹
net use Z: \\A电脑IP\web_ai_vision_app /user:shareuser docker

# 启动同步
.\scripts\sync-from-dev.ps1 -DevMachineIP "A电脑IP" -Mode watch
```

#### 同步模式说明
```powershell
# 测试连接
.\scripts\sync-from-dev.ps1 -DevMachineIP "*************" -Mode test

# 单次同步
.\scripts\sync-from-dev.ps1 -DevMachineIP "*************" -Mode once

# 实时监控同步（推荐）
.\scripts\sync-from-dev.ps1 -DevMachineIP "*************" -Mode watch

# 自定义同步间隔（秒）
.\scripts\sync-from-dev.ps1 -DevMachineIP "*************" -Mode watch -WatchInterval 5
```

---

## 🛡️ **安全配置**

### 防火墙规则
```powershell
# 查看当前防火墙规则
netsh advfirewall firewall show rule name="Docker Frontend"
netsh advfirewall firewall show rule name="Docker Backend"

# 删除规则（如需要）
netsh advfirewall firewall delete rule name="Docker Frontend"
netsh advfirewall firewall delete rule name="Docker Backend"
```

### 网络安全建议
1. **仅在可信网络中开启局域网访问**
2. **定期更改共享用户密码**
3. **使用VPN进行远程访问**
4. **监控网络访问日志**

---

## 🔍 **网络诊断**

### 连通性测试
```powershell
# 测试本地服务
curl http://localhost:8080
curl http://localhost:8000/api/vision/models/

# 测试局域网访问
curl http://你的IP:8080
curl http://你的IP:8000/api/vision/models/

# 测试容器间网络
docker exec ai-vision-frontend ping backend
docker exec ai-vision-backend ping frontend
```

### 端口检查
```powershell
# 检查端口占用
netstat -ano | findstr :8080
netstat -ano | findstr :8000
netstat -ano | findstr :5173

# 检查Docker端口映射
docker port ai-vision-frontend
docker port ai-vision-backend
```

### 网络配置检查
```powershell
# 运行网络诊断脚本
.\scripts\check\check-network.ps1
.\scripts\check\check-lan-access.ps1

# 查看网络接口
ipconfig /all

# 查看路由表
route print
```

---

## ⚡ **性能优化**

### 网络性能优化
```yaml
# docker-compose.yml 网络优化
services:
  backend:
    networks:
      ai-vision-network:
        aliases:
          - api-server

networks:
  ai-vision-network:
    driver: bridge
    driver_opts:
      com.docker.network.bridge.name: ai-vision-br0
      com.docker.network.driver.mtu: 1500
```

### 缓存配置
```nginx
# nginx.conf 缓存优化
location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}

location /api/ {
    proxy_cache_bypass $http_pragma;
    proxy_cache_revalidate on;
}
```

---

## 🌍 **外网访问配置**

### 端口转发设置
如需从外网访问，需要在路由器中配置端口转发：
```
内网IP: *************
内网端口: 8080
外网端口: 8080
协议: TCP
```

### 动态DNS配置
```powershell
# 使用动态DNS服务（如花生壳、DDNS等）
# 配置域名指向你的公网IP
# 示例: yourdomain.ddns.net:8080
```

### 安全注意事项
⚠️ **外网访问安全提醒**：
1. 更改默认端口
2. 配置HTTPS证书
3. 启用访问认证
4. 定期更新系统
5. 监控访问日志

---

## 📊 **网络监控**

### 实时监控
```powershell
# 监控网络连接
netstat -an | findstr :8080
netstat -an | findstr :8000

# 监控Docker网络
docker network ls
docker network inspect ai-vision-network
```

### 日志分析
```powershell
# 查看访问日志
docker-compose logs nginx | findstr "GET\|POST"

# 查看错误日志
docker-compose logs | findstr "ERROR\|WARN"
```

---

## 🆘 **常见网络问题**

### 无法访问localhost:8080
```powershell
# 1. 检查容器状态
docker ps | findstr frontend

# 2. 检查端口映射
docker port ai-vision-frontend

# 3. 检查防火墙
.\scripts\setup-firewall.bat

# 4. 重启服务
docker-compose restart frontend
```

### 局域网无法访问
```powershell
# 1. 检查IP地址
ipconfig | findstr IPv4

# 2. 测试网络连通性
ping 你的IP

# 3. 检查防火墙规则
netsh advfirewall firewall show rule name="Docker Frontend"

# 4. 运行诊断脚本
.\scripts\check\check-lan-access.ps1
```

### 跨电脑同步失败
```powershell
# 1. 测试网络连接
ping A电脑IP
telnet A电脑IP 445

# 2. 检查共享状态
.\scripts\setup-code-sharing.ps1 status

# 3. 重新建立连接
net use Z: /delete
net use Z: \\A电脑IP\web_ai_vision_app /user:shareuser docker
```
