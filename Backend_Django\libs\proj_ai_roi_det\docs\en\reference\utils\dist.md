---
description: Explore Ultralytics' utilities for distributed training including DDP file generation, command setup, and cleanup. Improve multi-node training efficiency.
keywords: Ultralytics, distributed training, DDP, multi-node training, network port, DDP file generation, DDP command, training utilities
---

# Reference for `ultralytics/utils/dist.py`

!!! note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/utils/dist.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/utils/dist.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/utils/dist.py) 🛠️. Thank you 🙏!

<br>

## ::: ultralytics.utils.dist.find_free_network_port

<br><br><hr><br>

## ::: ultralytics.utils.dist.generate_ddp_file

<br><br><hr><br>

## ::: ultralytics.utils.dist.generate_ddp_command

<br><br><hr><br>

## ::: ultralytics.utils.dist.ddp_cleanup

<br><br>
