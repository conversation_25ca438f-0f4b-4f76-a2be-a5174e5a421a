import React from 'react';
import { Allotment } from 'allotment';
import { Typography, Row, Col } from 'antd'; // 添加 Row, Col
import PanelInfo from './PanelInfo'; // 导入 PanelInfo 组件
import { useImageWorkspace } from '../contexts/ImageWorkspaceContext'; // Import context hook
import BaseInfoPanel from './BaseInfoPanel'; // 导入 BaseInfoPanel 组件
// import "allotment/dist/style.css"; // App.tsx 已导入，这里无需重复导入

const { Text } = Typography; // 获取 Typography 的子组件

const ImageInfoPanel: React.FC = () => {
  const { imageList, currentImageIndex, currentImageInfo } = useImageWorkspace(); // Get state from context

  const totalImages = imageList.length;
  const displayIndex = currentImageIndex >= 0 ? currentImageIndex + 1 : '-'; // 1-based index for display

  return (
    <Allotment defaultSizes={[40, 60]}>
      <Allotment.Pane>
        <BaseInfoPanel title="图像基本信息">
          {!currentImageInfo && <Text>请先加载一张图片。</Text>}
          {currentImageInfo && (
            <div style={{ display: 'flex', flexDirection: 'column', gap: '4px' }}>
              <Row justify="space-between" align="middle">
                <Col span={8}><Text strong>名称:</Text></Col>
                <Col span={16}><Text>{currentImageInfo.name ?? '-'}</Text></Col>
              </Row>
              <Row justify="space-between" align="middle">
                <Col span={8}><Text strong>宽度:</Text></Col>
                <Col span={16}><Text>{currentImageInfo.width ? `${currentImageInfo.width} px` : '-'}</Text></Col>
              </Row>
              <Row justify="space-between" align="middle">
                <Col span={8}><Text strong>高度:</Text></Col>
                <Col span={16}><Text>{currentImageInfo.height ? `${currentImageInfo.height} px` : '-'}</Text></Col>
              </Row>
              <Row justify="space-between" align="middle">
                <Col span={8}><Text strong>格式:</Text></Col>
                <Col span={16}><Text>{currentImageInfo.type ?? '-'}</Text></Col>
              </Row>
              <Row justify="space-between" align="middle">
                <Col span={8}><Text strong>索引:</Text></Col>
                <Col span={16}><Text>{displayIndex}/{totalImages > 0 ? totalImages : '-'}</Text></Col>
              </Row>
            </div>
          )}
        </BaseInfoPanel>
      </Allotment.Pane>
      <Allotment.Pane>
        <PanelInfo />
      </Allotment.Pane>
    </Allotment>
  );
};

export default ImageInfoPanel;