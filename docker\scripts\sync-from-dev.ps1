# Code Synchronization Script for Deployment Machine (B Computer)
# B Computer Code Sync Script

param(
    [Parameter(Mandatory=$true)]
    [string]$DevMachineIP,
    
    [Parameter(Mandatory=$false)]
    [string]$ShareUser = "",
    
    [Parameter(Mandatory=$false)]
    [string]$SharePassword = "",
    
    [Parameter(Mandatory=$false)]
    [string]$ShareName = "web_ai_vision_app",
    
    [Parameter(Mandatory=$false)]
    [ValidateSet("once", "watch", "test", "disconnect")]
    [string]$Mode = "once",
    
    [Parameter(Mandatory=$false)]
    [int]$WatchInterval = 15
)

$ErrorActionPreference = "Continue"

function Write-ColorOutput {
    param([string]$Message, [string]$Color = "White")
    Write-Host $Message -ForegroundColor $Color
}

function Connect-NetworkShare {
    Write-ColorOutput "=== Connecting to A Computer Network Share ===" "Green"
    
    $NetworkPath = "\\$DevMachineIP\$ShareName"
    
    Write-ColorOutput "Network path: $NetworkPath" "Yellow"
    
    try {
        # First try direct access with current user credentials
        Write-ColorOutput "Trying direct access with current user credentials..." "Gray"
        if (Test-Path $NetworkPath) {
            Write-ColorOutput "Network share connection successful! (using current user credentials)" "Green"
            return $true
        }
        
        # If direct access fails, try mapping drive
        Write-ColorOutput "Direct access failed, trying drive mapping..." "Yellow"
        $DriveLetter = "Z:"
        
        # Disconnect existing connections
        try {
            net use $DriveLetter /delete /y 2>$null | Out-Null
        }
        catch {
            # Ignore errors when disconnecting
        }
        
        # Establish new connection
        if ([string]::IsNullOrEmpty($ShareUser)) {
            # Use current user credentials
            $connectCmd = "net use $DriveLetter $NetworkPath"
            Write-ColorOutput "Connecting with current Windows user credentials..." "Gray"
        } else {
            # Use specified user credentials
            $connectCmd = "net use $DriveLetter $NetworkPath /user:$ShareUser $SharePassword"
            Write-ColorOutput "Connecting with specified user credentials: $ShareUser" "Gray"
        }
        
        $result = Invoke-Expression $connectCmd 2>&1
        
        if ($LASTEXITCODE -eq 0) {
            Write-ColorOutput "Network share connection successful!" "Green"
            Write-ColorOutput "Mapped drive: $DriveLetter" "Cyan"
            return $true
        } else {
            Write-ColorOutput "Network share connection failed" "Red"
            Write-ColorOutput "Error message: $result" "Red"
            return $false
        }
    }
    catch {
        Write-ColorOutput "Connection error: $($_.Exception.Message)" "Red"
        return $false
    }
}

function Test-NetworkConnection {
    Write-ColorOutput "=== Testing Network Connection ===" "Green"
    
    # Test IP connectivity
    Write-ColorOutput "Testing IP connectivity: $DevMachineIP" "Yellow"
    try {
        $pingResult = Test-Connection -ComputerName $DevMachineIP -Count 2 -Quiet
        
        if ($pingResult) {
            Write-ColorOutput "✅ IP connectivity OK" "Green"
        } else {
            Write-ColorOutput "❌ IP connectivity failed" "Red"
            return $false
        }
    }
    catch {
        Write-ColorOutput "❌ Ping test failed: $($_.Exception.Message)" "Red"
        return $false
    }
    
    # Test share access
    $NetworkPath = "\\$DevMachineIP\$ShareName"
    Write-ColorOutput "Testing share access: $NetworkPath" "Yellow"
    
    try {
        if (Test-Path $NetworkPath) {
            Write-ColorOutput "✅ Network share access OK" "Green"
            
            # List share contents
            try {
                $items = Get-ChildItem $NetworkPath | Select-Object -First 5
                Write-ColorOutput "Share contents preview:" "Cyan"
                foreach ($item in $items) {
                    Write-ColorOutput "  - $($item.Name)" "Gray"
                }
            }
            catch {
                Write-ColorOutput "Could not list share contents" "Yellow"
            }
            
            return $true
        } else {
            Write-ColorOutput "❌ Cannot access network share" "Red"
            return $false
        }
    }
    catch {
        Write-ColorOutput "❌ Share access error: $($_.Exception.Message)" "Red"
        return $false
    }
}

function Sync-CodeOnce {
    Write-ColorOutput "=== Executing Single Code Sync ===" "Green"
    
    $NetworkPath = "\\$DevMachineIP\$ShareName"
    $LocalCodeSync = Join-Path (Split-Path $PSScriptRoot) "code-sync"
    
    # Ensure local sync directory exists
    if (!(Test-Path $LocalCodeSync)) {
        New-Item -ItemType Directory -Path $LocalCodeSync -Force | Out-Null
    }
    
    Write-ColorOutput "Source path: $NetworkPath" "Yellow"
    Write-ColorOutput "Target path: $LocalCodeSync" "Yellow"
    
    try {
        # Sync backend code
        $SourceBackend = Join-Path $NetworkPath "Backend_Django"
        $TargetBackend = Join-Path $LocalCodeSync "Backend_Django"
        
        if (Test-Path $SourceBackend) {
            Write-ColorOutput "Syncing backend code..." "Yellow"
            
            # Clear target directory
            if (Test-Path $TargetBackend) {
                Remove-Item $TargetBackend -Recurse -Force
            }
            
            # Copy files
            Copy-Item $SourceBackend -Destination $TargetBackend -Recurse -Force
            Write-ColorOutput "✅ Backend code sync completed" "Green"
        } else {
            Write-ColorOutput "⚠️ Source backend directory not found: $SourceBackend" "Yellow"
        }
        
        # Sync frontend code
        $SourceFrontend = Join-Path $NetworkPath "Frontend"
        $TargetFrontend = Join-Path $LocalCodeSync "Frontend"
        
        if (Test-Path $SourceFrontend) {
            Write-ColorOutput "Syncing frontend code..." "Yellow"
            
            # Check for dist directory
            $SourceDist = Join-Path $SourceFrontend "dist"
            $TargetDist = Join-Path $TargetFrontend "dist"
            
            if (Test-Path $SourceDist) {
                # Ensure target directory exists
                if (!(Test-Path $TargetFrontend)) {
                    New-Item -ItemType Directory -Path $TargetFrontend -Force | Out-Null
                }
                
                # Clear target dist directory
                if (Test-Path $TargetDist) {
                    Remove-Item $TargetDist -Recurse -Force
                }
                
                # Copy dist files
                Copy-Item $SourceDist -Destination $TargetDist -Recurse -Force
                Write-ColorOutput "✅ Frontend build files sync completed" "Green"
            } else {
                Write-ColorOutput "⚠️ Frontend build directory not found, please build frontend on A computer first" "Yellow"
            }
        } else {
            Write-ColorOutput "⚠️ Source frontend directory not found: $SourceFrontend" "Yellow"
        }
        
        # Show sync statistics
        $BackendFiles = 0
        $FrontendFiles = 0
        
        try {
            if (Test-Path $TargetBackend) {
                $BackendFiles = (Get-ChildItem $TargetBackend -Recurse -File).Count
            }
        }
        catch {
            $BackendFiles = 0
        }
        
        try {
            if (Test-Path $TargetDist) {
                $FrontendFiles = (Get-ChildItem $TargetDist -Recurse -File).Count
            }
        }
        catch {
            $FrontendFiles = 0
        }
        
        Write-ColorOutput "" "White"
        Write-ColorOutput "📊 Sync Statistics:" "Cyan"
        Write-ColorOutput "Backend files: $BackendFiles" "Gray"
        Write-ColorOutput "Frontend files: $FrontendFiles" "Gray"
        Write-ColorOutput "Sync time: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" "Gray"
        
        return $true
    }
    catch {
        Write-ColorOutput "❌ Sync failed: $($_.Exception.Message)" "Red"
        return $false
    }
}

function Start-WatchMode {
    Write-ColorOutput "=== Starting Watch Sync Mode ===" "Green"
    Write-ColorOutput "Watch interval: $WatchInterval seconds" "Yellow"
    Write-ColorOutput "Press Ctrl+C to stop watching" "Cyan"
    Write-ColorOutput "" "White"
    
    $lastSyncTime = Get-Date
    $syncCount = 0
    
    while ($true) {
        try {
            Write-ColorOutput "[$(Get-Date -Format 'HH:mm:ss')] Checking for code updates..." "Gray"
            
            # Check network connection
            try {
                $pingResult = Test-Connection -ComputerName $DevMachineIP -Count 1 -Quiet
                if (!$pingResult) {
                    Write-ColorOutput "⚠️ Network connection interrupted, waiting for reconnection..." "Yellow"
                    Start-Sleep -Seconds $WatchInterval
                    continue
                }
            }
            catch {
                Write-ColorOutput "⚠️ Network check failed, waiting..." "Yellow"
                Start-Sleep -Seconds $WatchInterval
                continue
            }
            
            # Check if source files have updates
            $NetworkPath = "\\$DevMachineIP\$ShareName"
            $hasUpdates = $false
            
            # Check backend updates
            try {
                $SourceBackend = Join-Path $NetworkPath "Backend_Django"
                if (Test-Path $SourceBackend) {
                    $latestBackend = Get-ChildItem $SourceBackend -Recurse -File | 
                                    Sort-Object LastWriteTime -Descending | 
                                    Select-Object -First 1
                    
                    if ($latestBackend -and $latestBackend.LastWriteTime -gt $lastSyncTime) {
                        $hasUpdates = $true
                        Write-ColorOutput "🔄 Backend code update detected: $($latestBackend.Name)" "Yellow"
                    }
                }
            }
            catch {
                Write-ColorOutput "Could not check backend updates" "Gray"
            }
            
            # Check frontend updates
            try {
                $SourceFrontend = Join-Path $NetworkPath "Frontend\dist"
                if (Test-Path $SourceFrontend) {
                    $latestFrontend = Get-ChildItem $SourceFrontend -Recurse -File | 
                                     Sort-Object LastWriteTime -Descending | 
                                     Select-Object -First 1
                    
                    if ($latestFrontend -and $latestFrontend.LastWriteTime -gt $lastSyncTime) {
                        $hasUpdates = $true
                        Write-ColorOutput "🔄 Frontend code update detected: $($latestFrontend.Name)" "Yellow"
                    }
                }
            }
            catch {
                Write-ColorOutput "Could not check frontend updates" "Gray"
            }
            
            # Execute sync
            if ($hasUpdates) {
                Write-ColorOutput "📥 Starting code sync..." "Cyan"
                
                if (Sync-CodeOnce) {
                    $syncCount++
                    $lastSyncTime = Get-Date
                    Write-ColorOutput "✅ Code sync completed (sync #$syncCount)" "Green"
                    
                    # Check Docker service status
                    try {
                        Write-ColorOutput "🔄 Checking Docker service status..." "Yellow"
                        $containerStatus = docker ps --format "{{.Names}}" 2>$null | Where-Object { $_ -like "*ai-vision*" }
                        
                        if ($containerStatus) {
                            Write-ColorOutput "Docker services are running, code will auto-reload" "Cyan"
                        } else {
                            Write-ColorOutput "⚠️ Docker services not running, please start manually" "Yellow"
                        }
                    }
                    catch {
                        Write-ColorOutput "Could not check Docker status" "Gray"
                    }
                } else {
                    Write-ColorOutput "❌ Code sync failed" "Red"
                }
            } else {
                Write-ColorOutput "✓ No code updates" "Gray"
            }
            
            Start-Sleep -Seconds $WatchInterval
        }
        catch {
            Write-ColorOutput "❌ Watch process error: $($_.Exception.Message)" "Red"
            Write-ColorOutput "Waiting $WatchInterval seconds before retry..." "Yellow"
            Start-Sleep -Seconds $WatchInterval
        }
    }
}

function Disconnect-NetworkShare {
    Write-ColorOutput "=== Disconnecting Network Share ===" "Yellow"
    
    try {
        net use Z: /delete /y 2>$null | Out-Null
        Write-ColorOutput "Network share connection disconnected" "Green"
    }
    catch {
        Write-ColorOutput "Disconnect failed: $($_.Exception.Message)" "Red"
    }
}

# Main Logic
Write-ColorOutput "=== B Computer Code Sync Tool ===" "Green"
Write-ColorOutput "Development machine IP: $DevMachineIP" "Cyan"
Write-ColorOutput "Sync mode: $Mode" "Cyan"
Write-ColorOutput "" "White"

# Connect to network share
if ($Mode -ne "disconnect") {
    if (!(Connect-NetworkShare)) {
        Write-ColorOutput "Cannot connect to A computer, please check:" "Red"
        Write-ColorOutput "1. A computer has network sharing set up" "Yellow"
        Write-ColorOutput "2. Network connection is normal" "Yellow"
        Write-ColorOutput "3. Username and password are correct" "Yellow"
        exit 1
    }
}

# Execute corresponding mode
switch ($Mode) {
    "test" {
        Test-NetworkConnection
    }
    "once" {
        if (Sync-CodeOnce) {
            Write-ColorOutput "" "White"
            Write-ColorOutput "🎉 Single sync completed!" "Green"
        }
    }
    "watch" {
        Start-WatchMode
    }
    "disconnect" {
        Disconnect-NetworkShare
    }
    default {
        Write-ColorOutput "Invalid mode: $Mode" "Red"
        Write-ColorOutput "Available modes: once, watch, test, disconnect" "Yellow"
    }
}
