services:
  # Backend service - simple mode
  backend:
    image: web_ai_vision_app-backend:latest
    container_name: ai-vision-backend-simple
    restart: unless-stopped
    environment:
      - DJANGO_SETTINGS_MODULE=backend_project.settings_docker
      - PYTHONPATH=/app
      - PIP_INDEX_URL=https://pypi.tuna.tsinghua.edu.cn/simple
      - PIP_TRUSTED_HOST=pypi.tuna.tsinghua.edu.cn
      - DJANGO_ALLOWED_HOSTS=*
      - CORS_ALLOW_ALL_ORIGINS=True
      - PYTHONUNBUFFERED=1
    volumes:
      - ./data/db:/app/db
      - ./data/models:/app/models
      - ./data/media:/app/media
      - ./data/logs:/app/logs
    ports:
      - "8000:8000"
    networks:
      - ai-vision-network
    command: >
      sh -c "
        echo 'Starting Django server...' &&
        python manage.py collectstatic --noinput --clear &&
        python manage.py migrate &&
        python manage.py runserver 0.0.0.0:8000
      "
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/vision/models/"]
      interval: 30s
      timeout: 30s
      retries: 5
      start_period: 120s

  # Frontend service - simple mode
  frontend:
    image: web_ai_vision_app-frontend:latest
    container_name: ai-vision-frontend-simple
    restart: unless-stopped
    ports:
      - "8080:80"
    depends_on:
      - backend
    networks:
      - ai-vision-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s

networks:
  ai-vision-network:
    driver: bridge
