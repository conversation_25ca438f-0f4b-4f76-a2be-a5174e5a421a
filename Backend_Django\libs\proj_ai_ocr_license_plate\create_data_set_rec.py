from user_utils.proj_offline_data_augmentation.OCR import ocrRec, LabelPaddleOCR

if __name__ == "__main__":
    rec = ocrRec(suffix=".png")

    rec.Distribute_Data_Set({
        "mindeo/data/common/CCPD2019/ccpd_base" : 1.0,
        "mindeo/data/common/CCPD2019/ccpd_blur" : 1.0,
        "mindeo/data/common/CCPD2019/ccpd_challenge" : 1.0,
        "mindeo/data/common/CCPD2019/ccpd_db" : 1.0,
        "mindeo/data/common/CCPD2019/ccpd_fn" : 1.0,
        "mindeo/data/common/CCPD2019/ccpd_rotate" : 1.0,
        "mindeo/data/common/CCPD2019/ccpd_tilt" : 1.0,
        "mindeo/data/common/CCPD2019/ccpd_weather" : 1.0,
        "mindeo/data/common/CCPD2020/ccpd_green/test" : 1.0,
        "mindeo/data/common/CCPD2020/ccpd_green/train" : 1.0,
        "mindeo/data/common/CCPD2020/ccpd_green/val" : 1.0,
        }, 0.75, 0.2, "mindeo/data_processed/Rec_Tmp/DataSet", seed=20241030, only_label=True)

    # 减少特定数据的比例
    classify_func = lambda x: x[0:1]
    for data_type in ["train", "val", "test"]:
        classified_labels_dict = rec.Classify_Data([
            f"mindeo/data_processed/Rec_Tmp/DataSet/{data_type}",
            ], classify_func)

        # print(data_type)
        # for id in classified_labels_dict:
        #     print(id, len(classified_labels_dict[id]))

        rec.Write_Classified_Data({
            "皖": 0.01,
        }, classified_labels_dict, f"mindeo/data_processed/Rec_Tmp/Classify/{data_type}", only_label=True)

    # 添加花纹
    for data_type in ["train", "val", "test"]:
        rec.Add_Pattern([
            f"mindeo/data_processed/Rec_Tmp/Classify/{data_type}",
            ], 1, f"mindeo/data_processed/Rec_Tmp/Pattern/{data_type}")

    # 对图片做仿射变换
    for data_type in ["train", "val", "test"]:
        rec.Affine_Transform([
            f"mindeo/data_processed/Rec_Tmp/Classify/{data_type}",
            f"mindeo/data_processed/Rec_Tmp/Pattern/{data_type}",
            ], 1, f"mindeo/data_processed/Rec_Tmp/Affine/{data_type}")

    # 添加阴影
    for data_type in ["train", "val", "test"]:
        rec.Add_Shadow([
            f"mindeo/data_processed/Rec_Tmp/Affine/{data_type}",
            ], 1, f"mindeo/data_processed/Rec_Tmp/Add_Shadow/{data_type}", num_circles=10, max_radius=120)

    # 旋转和裁剪图像
    for data_type in ["train", "val", "test"]:
        rec.Rotate_And_Crop([
            f"mindeo/data_processed/Rec_Tmp/Add_Shadow/{data_type}",
            f"mindeo/data_processed/Rec_Tmp/Classify/{data_type}",
            ], (64, 320), f"mindeo/data_processed/Rec/Train/{data_type}")