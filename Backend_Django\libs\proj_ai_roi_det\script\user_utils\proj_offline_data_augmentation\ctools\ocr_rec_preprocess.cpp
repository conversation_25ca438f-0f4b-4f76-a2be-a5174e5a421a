//-----------------------------------------------------------------------------
//  Includes

#include <cstdio>
#include <cmath>
#include <cstdlib>
#include <iostream>
#include <memory>
#include <sstream>
#include <string>
#include <stack>
#include <vector>
#include <algorithm>
#include <sys/stat.h>

#include "AIEngineCommon.h"
#include "test/Parser.hpp"
#include "opencv2/core/hal/interface.h"

#include <opencv2/core.hpp>
#include <opencv2/imgproc.hpp>
#include <opencv2/highgui/highgui.hpp>
//-----------------------------------------------------------------------------
//  Definitions

using namespace std;
typedef int8_t s8;
typedef uint8_t u8;
typedef uint16_t u16;
typedef int32_t s32;
typedef uint32_t u32;
//-----------------------------------------------------------------------------
//  Function declarations


//-----------------------------------------------------------------------------
//  Global variables


//-----------------------------------------------------------------------------
//  Functions

/**
 * @brief    
 *           计算两点间的距离
 *           
 * @param    x0, y0:    第一个点的x轴、y轴坐标
 * @param    x1, y1:    第二个点的x轴、y轴坐标
 *           
 * @retval   两点间的距离
 *           
 * @date     2024-07-16 Created by HuangJP
 */
inline double _calculate_distance(int x0, int y0, int x1, int y1)
{
	return sqrt(pow(x0-x1, 2) + pow(y0-y1, 2));
}

/**
 * @brief    
 *           检查ROI坐标是否合法
 *           
 * @param    img_height:    原图高度
 * @param    img_height:    原图宽度
 * @param    rco:           ROI坐标
 * @param    limit_h:       ROI高度限制
 * @param    limit_w:       ROI宽度限制
 *           
 * @retval   true:          坐标合法
 * @retval   false:         坐标不合法
 *           
 * @date     2024-07-16 Created by HuangJP
 */
static inline bool check_roi_coordinate(u32 img_height, u32 img_width, CoordinateVOC *rco, u32 limit_h, u32 limit_w)
{
    // 确保图片拷贝范围不超过图片边界
    if ((rco->xmin < 0)
        || (rco->ymin < 0)
        || (rco->xmax > (img_width - 1))
        || (rco->ymax > (img_height - 1)))
    {
        LOGE("ROI is out of image!\n");
        return false;
    }

    // 计算需要拷贝的区域，确保检测区域能装下ROI
    int cpy_h = rco->ymax - rco->ymin + 1;
    int cpy_w = rco->xmax - rco->xmin + 1;
    if ((cpy_h > limit_h)
        || (cpy_h < 0)
        || (cpy_w > limit_w)
        || (cpy_w < 0))
    {
        return false;
    }

    return true;
}

/**
 * @brief    获取积分图像
 *           
 *           
 * @param    gray_img:  灰度图像数组首地址
 * @param    sum:       保存积分图像数组的首地址
 * @param    width:     灰度图像宽度
 * @param    height:    灰度图像高度
 *           
 * @date     2024-07-16 Created by HuangJP
 */
template <typename gray_t>
void integral(gray_t *gray_img, long *sum, int width, int height)
{
    for (int y = 0; y < height; y++)
    {
        // 获取行指针
        gray_t *input_row_ptr = &gray_img[y * width];
        long *output_row_ptr = &sum[(y + 1) * width + 1]; // 积分图像比原始图像行和列都要多1

        // 计算积分图像
        for (int x = 0; x < width; x++)
        {
            output_row_ptr[x] = 0;															// 清零
            output_row_ptr[x] = output_row_ptr[x - 1] + input_row_ptr[x];					// 0 + s_{y,x-1} + a_{x,y}
            output_row_ptr[x] += output_row_ptr[x - width] - output_row_ptr[x - width - 1]; // 当前列和
        }
    }
}

/**
 * @brief    
 *           自适应阈值二值化
 *           
 * @param    gray_img:  灰度图像数组首地址
 * @param    width:     灰度图像宽度
 * @param    height:    灰度图像高度
 *           
 * @date     2024-07-16 Created by HuangJP
 */
template <typename gray_t>
void adaptive_threshold_binaryzation(gray_t *gray_img, int width, int height, gray_t *binary_img)
{
    // 自适应阈值取n*n范围的像素计算局部最优阈值，这里计算合适的n的取值
    int S = std::max(10, (width > height ? height : width)/2);
    // T是一个可调参数，影响阈值（进而影响二值化效果）
    float T = 0.1f;

    // 定义变量保存n*n框选的图像范围及像素数量
    int s2 = S/2;
    int x1, y1, x2, y2, count;

    // 申请内存空间用于保存积分图像，积分图像比原始图像多一行一列
    long *sum = NULL;
    sum = (long *)calloc((width + 1) * (height + 1), sizeof(long)); // 申请内存空间，并对申请到的空间做零初始化

    // 确认是否申请到了内存空间
    if(sum != NULL)
    {
        integral<gray_t>(gray_img, sum, width, height); // 计算和获取积分图像

        // 外层循环用于遍历图像的每一行
        for (int y = 0; y < height; y++)
        {
            // 计算y轴上的框选范围
            y1 = y - s2;
            y2 = y + s2;

            // 避免框选到图像外的像素点
            y1 = y1 < 0 ? 0 : y1;
            y2 = y2 > (height - 1) ? (height - 1) : y2;

            // 获取灰度图像和积分图像的行指针（可以理解为单行图像的首个像素的地址）
            gray_t *src = &gray_img[y * width];
            gray_t *dst =  &binary_img[y * width];
            long *y1_ptr = &sum[y1 * width];
            long *y2_ptr = &sum[y2 * width];

            // 内层循环用于遍历图像的每一列
            for (int x = 0; x < width; x++)
            {
                // 计算x轴上的框选范围
                x1 = x - s2;
                x2 = x + s2;

                // 避免框选到图像外的像素点
                x1 = x1 < 0 ? 0 : x1;
                x2 = x2 > (width - 1) ? (width - 1) : x2;

                // 计算框选的像素点的个数
                count = (x2 - x1) * (y2 - y1);

                // 计算局部阈值
                int summation = y2_ptr[x2] + y1_ptr[x1] - y1_ptr[x2] - y2_ptr[x1]; // 利用积分图快速求区域和

                // 二值化
                dst[x] = ((int)(src[x] * count) > (int)(summation * (1.f - T))) ? ~((gray_t)0) : 0;
            }
        }

        free(sum); // 释放申请的内存空间
    }
    else
    {
        // do nothing
    }
}

/**
 * @brief    
 *           逆时针旋转图像
 *           
 * @param    img_ptr:       指向图片存储地址的指针
 * @param    rows:          原图高度
 * @param    cols:          原图宽度
 * @param    degrees:       旋转角度（注意：目前仅支持旋转90度）
 * @param    output:        指向输出图片存储地址的指针
 *           
 * @date     2024-07-16 Created by HuangJP
 */
template <typename input_t, typename output_t>
void _rotate_img_anticlockwise(input_t *img_ptr, u32 rows, u32 cols, int degrees, output_t *output)
{
    switch (degrees)
    {
        case 90:
        {
            const int r_rows = cols;
            const int r_cols = rows;
            for (int i = 0; i < r_rows; i++)
            {
                output_t *dst = output + i * r_cols;
                input_t *src = img_ptr + (cols - 1) - i;
                for (int j = 0; j < r_cols; j++)
                {
                    dst[j] = *src;
                    src += cols;
                }
            }
            break;
        }
        default:
            LOGE("Rotation angle not supported");
            break;
    }
}

/**
 * @brief    
 *           裁剪图片
 *           
 * @param    img_ptr:       指向图片存储地址的指针
 * @param    rows:          原图高度
 * @param    cols:          原图宽度
 * @param    rsn:           右移位数（Right Shift Number），用于提取子位深度
 * @param    cut_x:         裁剪区域起始x轴坐标
 * @param    cut_y:         裁剪区域起始y轴坐标
 * @param    cut_h:         裁剪区域高度
 * @param    cut_w:         裁剪区域宽度
 * @param    output:        指向输出图片存储地址的指针
 * @param    binaryzation:  是否做二值化
 *           
 * @date     2024-07-16 Created by HuangJP
 */
template <typename input_t, typename output_t>
void _crop_img(input_t *img_ptr, u32 rows, u32 cols, s8 rsn, u32 cut_x, u32 cut_y, u32 cut_h, u32 cut_w, output_t *output, bool binaryzation = false)
{
    for (int i = 0; i < cut_h; i++)
    {
        input_t *src = img_ptr + (i + cut_y) * cols + cut_x;
        output_t *dst = output + i * cut_w;

        for (int j = 0; j < cut_w; j++)
        {
            dst[j] = src[j] >> rsn;
        }
    }

    if (binaryzation == true)
    {
        adaptive_threshold_binaryzation<output_t>(output, cut_w, cut_h, output);
    }
}

/**
 * @brief    
 *           单字符分割函数（基于深度优先搜索）
 *           
 * @param    binary:    指向二值化图像的指针
 * @param    rows:      图像高度
 * @param    cols:      图像宽度
 * @param    start_x:   起始x轴坐标
 * @param    start_y:   起始y轴坐标
 * @param    bbox:      单个字符的最小包围框
 *           
 * @retval   字符像素点个数
 *           
 * @date     2024-07-16 Created by HuangJP
 */
template <typename binary_t>
int _single_character_segmentation_dfs(binary_t *binary, int rows, int cols, int start_x, int start_y, CoordinateVOC &bbox)
{
    struct Point{
        int x;
        int y;
    };

    // 方向数组
    const int dx[] = {-1, 0, 1, 0};
    const int dy[] = {0, 1, 0, -1};

    // 计数值
    int count = 0;

    bbox = (CoordinateVOC){
        .xmin = (float)start_x,
        .ymin = (float)start_y,
        .xmax = (float)start_x,
        .ymax = (float)start_y,
    };

    // 初始化栈
    stack<Point> stk;
    stk.push((Point){.x = start_x, .y = start_y});

    // 当栈非空时继续搜索
    while (!stk.empty())
    {
        Point cur = stk.top(); stk.pop();
        binary_t *ptr = binary + cur.y * cols + cur.x;

        if ((cur.x < 0)
            || (cur.x >= cols)
            || (cur.y < 0)
            || (cur.y >= rows)
            || (*ptr != 0))
        {
            continue;
        }

        // 将当前点的邻居加入到栈中
        const int num = std::min(sizeof(dy) / sizeof(dy[0]), sizeof(dx) / sizeof(dx[0]));
        for (int i = 0; i < num; i++)
        {
            stk.push((Point){.x = cur.x + dx[i], .y = cur.y + dy[i]});
        }

        bbox.xmin = std::min(bbox.xmin, (float)cur.x);
        bbox.ymin = std::min(bbox.ymin, (float)cur.y);
        bbox.xmax = std::max(bbox.xmax, (float)cur.x);
        bbox.ymax = std::max(bbox.ymax, (float)cur.y);

        // 标记当前点已遍历
        count++;
        *ptr = ~((u8)0);
    }

    return count;
}

/**
 * @brief    
 *           字符分割函数（分割直线上的字符）
 *           
 * @param    binary:        指向二值化图像的指针
 * @param    rows:          图像高度
 * @param    cols:          图像宽度
 * @param    line:          分割线上所有点的坐标，
 * @param    bboxes:        每个字符的最小包围框信息
 * @param    max_height:    最高字符高度
 * @param    bias_x:        x轴偏移坐标，返回最小包围框坐标时会加上此值
 * @param    bias_y:        y轴偏移坐标，返回最小包围框坐标时会加上此值
 * @param    max_count:     最大字符像素数，字符像素个数超过此值时，不返回该字符的坐标信息
 *           
 * @retval   字符像素点个数
 *           
 * @date     2024-07-16 Created by HuangJP
 */
template <typename binary_t>
void character_segmentation_in_line(binary_t *binary, int rows, int cols, vector<pair<int, int>> line, vector<CoordinateVOC> &bboxes, int max_height, int bias_x = 0, int bias_y = 0, int max_count = 1000)
{
    int char_center_y = 0; // 字符中心点y轴坐标
    for (auto point: line)
    {
        int point_x = point.first;
        int point_y = bboxes.size() == 0 ? point.second : char_center_y;
        binary_t *src = binary + point_y * cols + point_x;
        if (*src == 0)
        {
            CoordinateVOC bbox;
            int size = _single_character_segmentation_dfs<binary_t>(binary, rows, cols, point_x, point_y, bbox);
            if ((size < 50)
                || ((bbox.ymax - bbox.ymin + 1) > max_height)
                || (size > max_count))
            {
                continue;
            }

            char_center_y = (bbox.ymin + bbox.ymax) / 2; // 计算字符中心点y轴坐标

            bbox.xmin += bias_x;
            bbox.xmax += bias_x;
            bbox.ymin += bias_y;
            bbox.ymax += bias_y;

            bboxes.emplace_back(bbox);
        }
    }

    std::sort(bboxes.begin(), bboxes.end(), [](const CoordinateVOC& a, const CoordinateVOC& b) {
        return a.xmin < b.xmin;
    });
}

/**
 * @brief    
 *           根据四边形坐标旋转和裁剪图片
 *           
 * @param    quad_points:    四边形坐标
 * @param    rows:           原图高度
 * @param    cols:           原图宽度
 * @param    img_ptr:        指向原图数组的指针
 * @param    rsn:            灰度图位深度范围
 * @param    resizeH:        输入张量的高度
 * @param    resizeW:        输入张量的宽度（值为-1时表示不限制宽度）
 * @param    resizeC:        输入张量的通道数
 * @param    ignore_width:   忽略输出图片宽度
 *           
 * @retval   指向旋转和裁剪后的图片的指针
 *           
 * @date     2024-07-17 Created by HuangJP
 */
template<typename input_t, typename output_t>
shared_ptr<output_t> prep_rotate_and_crop_img(QuadPoints quad_points, int rows, int cols, input_t *img_ptr, s8 rsn, int resizeH, int resizeW, int resizeC, int &img_width)
{
    // 设置旋转中心
    int centroid_x = (quad_points.x0 + quad_points.x1 + quad_points.x2 + quad_points.x3) / 4;
    int centroid_y = (quad_points.y0 + quad_points.y1 + quad_points.y2 + quad_points.y3) / 4;

    // 确定矩形高度和宽度
    float width1 = _calculate_distance(quad_points.x0, quad_points.y0, quad_points.x1, quad_points.y1);
    float width2 = _calculate_distance(quad_points.x2, quad_points.y2, quad_points.x3, quad_points.y3);
    float height1 = _calculate_distance(quad_points.x3, quad_points.y3, quad_points.x0, quad_points.y0);
    float height2 = _calculate_distance(quad_points.x1, quad_points.y1, quad_points.x2, quad_points.y2);

    // 留下一定宽度和高度，模拟下位机的情况
    width1  += 10;
    width2  += 10;
    height1 += 5;
    height2 += 5;

    float width = std::max(width1, width2);
    float height = std::max(height1, height2);

    // 高度或宽度不能为0
    if ((width <= 0.0f) || (height <= 0.0f))
    {
        return nullptr;
    }

    float slope = 1.0f;             // 包围框斜率
    float angle_radians = 0.0f;     // 旋转角度（单位: 弧度）
    float scale_w = 1.0f;           // 宽度方向缩放系数
    float scale_h_left = 1.0f;      // 高度方向缩放系数（左）
    float scale_h_right = 1.0f;     // 高度方向缩放系数（右）
    float bbox_width = 0;           // 包围框宽度
    int cpy_w = 0;                  // 宽度方向拷贝的像素数量
    int cpy_h = resizeH;            // 高度方向拷贝的像素数量

    // 计算包围框高度和宽度以及斜率
    // 水平方向
    if (width > height)
    {
        // 计算斜率
        float delta_y = ((float)(quad_points.y2 - quad_points.y3) + (float)(quad_points.y1 - quad_points.y0)) / 2.0f;
        float delta_x = ((float)(quad_points.x2 - quad_points.x3) + (float)(quad_points.x1 - quad_points.x0)) / 2.0f;
        slope = (delta_x == 0) ? 1.0f : delta_y / delta_x;
        angle_radians = atan(slope); // 计算旋转角度

        // 计算左右两边的高度缩放系数
        scale_h_left = height1 / resizeH;
        scale_h_right = height2 / resizeH;

        // 赋值包围框宽度
        bbox_width = width;
    }
    // 竖直方向
    else
    {
        // 计算斜率
        float delta_y = ((float)(quad_points.y1 - quad_points.y2) + (float)(quad_points.y0 - quad_points.y3)) / 2.0f;
        float delta_x = ((float)(quad_points.x1 - quad_points.x2) + (float)(quad_points.x0 - quad_points.x3)) / 2.0f;
        slope = (delta_x == 0) ? 1.0f : delta_y / delta_x;
        slope = -1 / slope; // 求法线斜率
        angle_radians = atan(slope) + M_PI / 2; // 计算旋转角度

        // 计算左右两边的高度缩放系数
        scale_h_left = width1 / resizeH;
        scale_h_right = width2 / resizeH;

        // 赋值包围框宽度
        bbox_width = height;
    }

    // 获取拷贝宽度、计算宽度缩放系数
    scale_w = std::max(scale_h_left, scale_h_right);
    int estimate_width = bbox_width / scale_w;
    cpy_w = estimate_width;

    // 判断是否要限制宽度
    if (resizeW != -1)
    {
        scale_w = (estimate_width <= resizeW) ? (scale_w) : (bbox_width / resizeW);
        cpy_w = std::min(resizeW, estimate_width);
    }
    img_width = cpy_w; // 返回输出图像宽度

    // 申请图片存储空间
    auto dest_mem = std::shared_ptr<output_t>(new output_t[cpy_h * cpy_w], [](output_t* p) { delete[] p; });
    output_t *dest = dest_mem.get();
    memset(dest, 0x00, cpy_w * cpy_h * sizeof(output_t));

    // 提前计算好每一个像素点高度方向的缩放系数
    vector<float> scale_h(cpy_w, 1.0f);
    for (int i = 0; i < cpy_w; i++)
    {
        float step = (float)i / (float)cpy_w;
        scale_h[i] = scale_h_left * (1 - step) + scale_h_right * step;
    }

    int start_x = -cpy_w / 2; // 起始x坐标
    int start_y = -cpy_h / 2; // 起始y坐标
    float cos_val = cos(angle_radians); // 旋转矩阵cos值
    float sin_val = sin(angle_radians); // 旋转矩阵sin值

    // 遍历和填充输入张量
    for (int i = 0; i < cpy_h; i++)
    {
        output_t *dst = dest + i * cpy_w * resizeC;
        for (int j = 0; j < cpy_w; j++)
        {
            // 计算缩放后的坐标点
            float s_x = (start_x + j) * scale_w;
            float s_y = (start_y + i) * scale_h[j];
            // 旋转得到像素点在原图上的坐标
            float src_x = s_x * cos_val - s_y * sin_val + (float)centroid_x;
            float src_y = s_x * sin_val + s_y * cos_val + (float)centroid_y;

            // 双线性插值
            int x0 = (int)src_x;
            int y0 = (int)src_y;
            int x1 = x0 + 1;
            int y1 = y0 + 1;

            // 计算权重
            float dx = src_x - (float)x0;
            float dy = src_y - (float)y0;

            float w00 = (1.0f - dx) * (1.0f - dy);
            float w01 = dx * (1.0f - dy);
            float w10 = (1.0f - dx) * dy;
            float w11 = dx * dy;

            // 防止越界
            x0 = std::min(std::max(x0, 0), cols - 2);
            y0 = std::min(std::max(y0, 0), rows - 2);
            x1 = std::min(std::max(x1, 0), cols - 1);
            y1 = std::min(std::max(y1, 0), rows - 1);

            // 计算像素值
            input_t *src00 = img_ptr + x0 + y0 * cols; 
            input_t *src01 = src00 + (x1 - x0);
            input_t *src10 = src00 + (y1 - y0) * cols;
            input_t *src11 = src10 + (x1 - x0);
            output_t pix_val = (int)(((float)*src00 * w00) + ((float)*src01 * w01) + ((float)*src10 * w10) + ((float)*src11 * w11)) >> rsn;

            // 单通道填充数据
            if (resizeC == 1)
            {
                *dst = pix_val;
                dst++;
            }
            // 三通道填充数据
            else if (resizeC == 3)
            {
                dst[0] = pix_val;
                dst[1] = pix_val;
                dst[2] = pix_val;
                dst += 3;
            }
        }
    }

    return dest_mem;
}

/**
 * @brief    
 *           字符内容长度校验
 *           
 * @param    img:       校验图片
 * @param    content:   校验内容
 * @param    bboxes:    字符位置信息
 *           
 * @retval   true:      校验通过
 * @retval   false:     校验不通过
 *           
 * @date     2024-07-18 Created by HuangJP
 */
bool content_length_verify(cv::Mat img, string content, vector<CoordinateVOC> *bboxes = nullptr)
{
    // 判断是否要返回字符位置信息
    vector<CoordinateVOC> _bboxes;
    bboxes = bboxes == nullptr ? &_bboxes : bboxes; // 指针为空时，使用本地变量存储字符位置信息

    // 截取区域图像
    std::shared_ptr<u8> bin_img_mem;

    // 申请内存空间用于存储截取的图像
    int bin_img_w = img.cols;
    int bin_img_h = img.rows;
    bin_img_mem = std::shared_ptr<u8>(new u8[bin_img_w * bin_img_h], [](u8* p) { delete[] p; });
    u8 *bin_img = bin_img_mem.get();

    // 二值化
    adaptive_threshold_binaryzation(img.data, bin_img_w, bin_img_h, bin_img);

    // 计算斜率
    vector<pair<int, int>> line;

    // 画出字符中垂线的每一个点
    for (int i = 0; i < bin_img_w-1; i++)
    {
        line.emplace_back(pair<int, int>{i, bin_img_h/2});
    }

    // 基于中垂线分割字符
    character_segmentation_in_line(bin_img, bin_img_h, bin_img_w, line, *bboxes, bin_img_h, 0, 0, bin_img_h * bin_img_h);

    // 判断分割数量是否相符
    int word_count = bboxes->size();
    if (word_count != content.size())
    {
        // 图片提亮
        double alpha = 2.5; // 对比度因子
        double beta = 50;  // 亮度偏移量，负值降低亮度
        cv::Mat img_adjust_light = img.clone();
        img_adjust_light.convertTo(img_adjust_light, -1, alpha, beta);

        adaptive_threshold_binaryzation(img_adjust_light.data, bin_img_w, bin_img_h, bin_img);

        // // 可视化二值化图片
        // {
        //     cv::Mat output(bin_img_h, bin_img_w, CV_8UC1, bin_img); // 创建OpenCV Mat
        //     auto pos = output_file.find_last_of('.');
        //     string file_name = output_file.substr(0, pos);
        //     string suffix = output_file.substr(pos+1);
        //     stringstream ss;
        //     ss << file_name << "_binary" << "." << suffix;
        //     cv::imwrite(ss.str(), output); // 保存图片
        // }

        // 基于中垂线分割字符
        bboxes->clear();
        character_segmentation_in_line(bin_img, bin_img_h, bin_img_w, line, *bboxes, bin_img_h, 0, 0, bin_img_h * bin_img_h);
        word_count = bboxes->size();

        // 判断分割数量是否相符
        if (word_count != content.size())
        {
            return false;
        }
    }

    // 校验通过
    return true;
}

/**
 * @brief    
 *           滑窗分割图像
 *           
 * @param    img:           原始图像
 * @param    out_w:         输出图片宽度
 * @param    out_h:         输出图片高度
 * @param    content:       图片内容
 * @param    output_file:   输出文件路径
 *           
 * @retval   
 *           
 * @date     2024-07-24 Created by HuangJP
 */
int OCR_Preprocess_Sliding_Window_Segment(cv::Mat img, int out_w, int out_h, string content, string output_file)
{
    // 尝试分割字符
    vector<CoordinateVOC> bboxes;
    if (content_length_verify(img, content, &bboxes) == false)
    {
        // 分割失败，无法执行图像滑窗分割
        return -1;
    }

    // 初始化变量
    int bin_img_w = img.cols;
    int bin_img_h = img.rows;

    // 计算每一个分隔点
    vector<int> seg_points({(int)(bboxes.front().xmin / 2)});
    for (int i = 1; i < bboxes.size(); i++)
    {
        // 计算两个字符的中间值
        int center_x = (bboxes[i-1].xmax + bboxes[i].xmin) / 2;
        seg_points.emplace_back(center_x);
    }

    seg_points.emplace_back((bin_img_w-1 + bboxes.back().xmax) / 2);
    int left = 0, right = 0;
    int count = 0;
    int height = img.rows;

    // 滑动窗口截取图片
    while (right < seg_points.size()-1)
    {
        int next_length = seg_points[right+1] - seg_points[left] + 1;
        // 窗口增长
        if (next_length <= out_w)
        {
            right++;

            if (right != (seg_points.size()-1))
            {
                // 在使用最后一个分隔点前尽可能扩大窗口
                continue;
            }
        }

        // 窗口形成
        int left_x = seg_points[left];
        int right_x = seg_points[right];

        // 申请图片缓存
        int width = right_x - left_x;
        auto crop_img = std::shared_ptr<u8>(new u8[bin_img_w * height], [](u8* p) { delete[] p; });

        // 截取图片
        _crop_img<u8, u8>(img.data, img.rows, img.cols, 0, left_x, 0, height, width, crop_img.get());

        // 命名图片
        auto pos = output_file.find_last_of('.'); // 获取分隔符位置
        if (pos == string::npos)
        {
            return -1; // 无法获取后缀的情况无法保存图片，直接退出
        }
        string file_name = output_file.substr(0, pos); // 获取文件名
        string suffix = output_file.substr(pos+1); // 获取后缀
        stringstream ss;
        ss << file_name << "_" << count << "." << suffix;
        count++;

        // 保存图片
        cv::Mat output(height, width, CV_8UC1, crop_img.get()); // 创建OpenCV Mat
        cv::imwrite(ss.str(), output); // 保存图片
        cout << ss.str() << "\t" << content.substr(left, right - left) << endl; // 输出内容

        // 窗口缩短
        left++;
    }

    return 0;
}

/**
 * @brief    
 *           旋转和裁剪图像
 *           
 * @param    img:           原始图像
 * @param    quad_points:   四边形区域坐标
 * @param    out_w:         输出图片宽度
 * @param    out_h:         输出图片高度
 * @param    content:       图片内容
 * @param    output_file:   输出文件路径
 *           
 * @retval   0:             处理成功
 * @retval   -1:            处理失败
 *           
 * @date     2024-07-16 Created by HuangJP
 */
int OCR_Preprocess_Rotate_And_Crop(cv::Mat img, QuadPoints quad_points, int out_w, int out_h, string content, string output_file)
{
    // 计算四边形四条边的长度
    float width1 = _calculate_distance(quad_points.x0, quad_points.y0, quad_points.x1, quad_points.y1);
    float width2 = _calculate_distance(quad_points.x2, quad_points.y2, quad_points.x3, quad_points.y3);
    float height1 = _calculate_distance(quad_points.x3, quad_points.y3, quad_points.x0, quad_points.y0);
    float height2 = _calculate_distance(quad_points.x1, quad_points.y1, quad_points.x2, quad_points.y2);

    // 旋转和裁剪图像
    int cpy_w = 0;
    shared_ptr<u8> out_img_mem = prep_rotate_and_crop_img<u8, u8>(quad_points, img.rows, img.cols, img.data, 0, out_h, out_w, 1, cpy_w);
    u8 *out_img = out_img_mem.get();

    // 判断图像处理是否成功
    if ((out_img_mem == nullptr)
        || (cpy_w == 0))
    {
        printf("Output image no create\n");
        return -1;
    }

    // // 校验字符长度
    // cv::Mat verify_img(out_h, cpy_w, CV_8UC1, out_img);
    // if (content_length_verify(verify_img, content) == false)
    // {
    //     printf("Failed to verify content length\n");
    //     return -1;
    // }

    cv::Mat output(out_h, cpy_w, CV_8UC1, out_img); // 创建OpenCV Mat
    cv::imwrite(output_file, output); // 保存图片

    cout << content << endl; // 输出内容

    return 0;
}

/**
 * @brief    
 *           主函数
 *           
 * @param    argc:      传入参数个数
 * @param    argv:      传入参数数组
 *           
 * @retval   0:         执行成功
 * @retval   -1:        执行失败
 *           
 * @date     2024-07-16 Created by HuangJP
 */
int main(int argc, char *argv[])
{
    // 添加需要解析参数
    Parser parse;
    parse.Add_Boolean_Argument("--help"); // 帮助信息
    parse.Add_Argument("--area", ""); // 指定处理区域
    parse.Add_Argument("--image", ""); // 指定处理图片
    parse.Add_Argument("--content", ""); // 指定文本内容
    parse.Add_Argument("--output", "./output.jpg"); // 指定测试模型
    parse.Add_Argument("--output-size", ""); // 指定输出形状

    // 解析传入参数
    parse.Parse_Args(argc, argv);

    // 打印帮助信息
    bool help_flag = parse.Get_Args_Value<bool>("--help", false);
    if (help_flag == true)
    {
        cout << "=============================================================================" << endl;
        cout << "参数说明" << endl;
        cout << "=============================================================================" << endl;
        cout << "使用相关" << endl;
        cout << "  --area:                  指定前处理区域，示例：--area x0,y0,x1,y1,x2,y2,x3,y3" << endl;
        cout << "                               其中0, 1, 2, 3表示处理区域左上、右上、右下、左下角点的坐标。" << endl;
        cout << "  --image:                 指定需要处理的图片。" << endl;
        cout << "  --content:               指定字符内容。" << endl;
        cout << "  --output:                指定输出图片，默认为\"./output.jpg\"。" << endl;
        cout << "  --output-size:           指定输出图片的尺寸，示例：--output-size 960x32，其中960表示宽度，32表示高度" << endl;
        cout << "-----------------------------------------------------------------------------" << endl;
        cout << "帮助相关" << endl;
        cout << "  --help:                  打印帮助信息。" << endl;
        
        return 0;
    }

    // 获取检测区域
    string area = parse.Get_Args_Value<string>("--area", "");
    QuadPoints quad_points;
    int num_parms = sscanf(area.c_str(), "%d,%d,%d,%d,%d,%d,%d,%d",
        &quad_points.x0, &quad_points.y0,
        &quad_points.x1, &quad_points.y1,
        &quad_points.x2, &quad_points.y2,
        &quad_points.x3, &quad_points.y3
    );
    if (num_parms != 8)
    {
        cout << "Error: An unexpected preprocessing area was encountered. Please review the usage of the '--area' option by checking the help information available with '--help' for detailed guidance." << endl;
        return -1;
    }

    // 获取输入图片
    string image_file = parse.Get_Args_Value<string>("--image", "");
    if (image_file.size() == 0)
    {
        cout << "Error: Please specify the image file through '--image' option, checking the help information with '--help' for detailed guidance." << endl;
        return -1;
    }

    // 获取字符内容
    string content = parse.Get_Args_Value<string>("--content", "");
    if (content.size() == 0)
    {
        cout << "Error: Please specify the content through '--content' option, checking the help information with '--help' for detailed guidance." << endl;
        return -1;
    }

    // 获取输出
    string output = parse.Get_Args_Value<string>("--output", "");

    // 获取输出图片尺寸
    string output_size = parse.Get_Args_Value<string>("--output-size", "");
    if (output_size.size() == 0)
    {
        cout << "Error: Please specify the output size through '--output-size' option, checking the help information with '--help' for detailed guidance." << endl;
        return -1;
    }
    int out_w = 0;
    int out_h = 0;
    num_parms = sscanf(output_size.c_str(), "%dx%d", &out_w, &out_h);
    if (num_parms != 2)
    {
        cout << "Error: Invalid output_size." << endl;
        return -1;
    }

    // 读取图片
    cv::Mat img;
    img = cv::imread(image_file.c_str(), cv::IMREAD_GRAYSCALE);

    int rev = -1;
#ifdef PREP_TYPE_ROTATE_AND_CROP
    rev = OCR_Preprocess_Rotate_And_Crop(img, quad_points, out_w, out_h, content, output);
#endif
#ifdef PREP_TYPE_SLIDING_WINDOW_SEGMENT
    rev = OCR_Preprocess_Sliding_Window_Segment(img, out_w, out_h, content, output);
#endif

    return rev;
}
//-----------------------------------------------------------------------------
//  End of file