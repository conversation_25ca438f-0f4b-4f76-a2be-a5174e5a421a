---
comments: true
hide:
  - navigation
---

- Web online experience
    - PP-OCRv4 online experience：<https://aistudio.baidu.com/aistudio/projectdetail/6611435>
    - SLANet online experience：<https://aistudio.baidu.com/community/app/91661>
    - PP-ChatOCRv3-doc online experience：<https://aistudio.baidu.com/community/app/182491>
    - PP-ChatOCRv2-common online experience：<https://aistudio.baidu.com/community/app/91662>
    - PP-ChatOCRv2-doc online experience：<https://aistudio.baidu.com/community/app/70303>

- [One-Click Call for 17 Core PaddleOCR Models](https://paddlepaddle.github.io/PaddleOCR/latest/en/paddlex/quick_start.html)
- One line of code quick use: [Text Detection and Recognition (Chinese/English/Multilingual)](https://paddlepaddle.github.io/PaddleOCR/latest/en/ppocr/overview.html)
- One line of code quick use: [Document Analysis](https://paddlepaddle.github.io/PaddleOCR/latest/en/ppstructure/overview.html)
