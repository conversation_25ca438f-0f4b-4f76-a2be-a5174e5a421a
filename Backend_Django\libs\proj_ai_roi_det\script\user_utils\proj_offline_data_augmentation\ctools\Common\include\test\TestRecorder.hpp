#ifndef _TEST_RECORDER_H
#define _TEST_RECORDER_H

//-----------------------------------------------------------------------------
//  Includes

#include <iomanip>
#include <ios>
#include <string>
#include <vector>
#include <fstream>
#include <unordered_set>
#include <unordered_map>

#include "AIEngineCommon.h"
#include "ModelLoader.hpp"
#include "Log.hpp"
//-----------------------------------------------------------------------------
//  Definitions


//-----------------------------------------------------------------------------
//  Declarations

class TestRecorder {
public:
    typedef struct {
        std::string test_img; // 测试图片
        float elapsed_time; // 测试耗时
        std::vector<DetectionBBoxInfo> results; // 测试结果
    }DetectionTestResult; // 目标检测测试结果结构体

    /**
     * @brief    
     *           TestRecorder构造函数
     *           
     * @param    model_data:    测试模型数据
     * @param    test_set:      测试集名称
     *           
     * @date     2024-04-18 Created by HuangJP
     */
    TestRecorder(BaseModelLoader::ModelData model_data, const char *test_set) : _model_data(model_data), _test_set(test_set)
    {
        _number_of_tests = 0;
        _number_of_test_samples = 0;
    }

    /**
     * @brief    
     *           TestRecorder析构函数
     *           
     * @date     2024-04-18 Created by HuangJP
     */
    ~TestRecorder() {} // 析构函数

    /**
     * @brief    
     *           添加目标检测测试结果
     *           
     * @param    test_result:   目标检测测试结果
     *           
     * @retval   错误码
     *           
     * @date     2024-04-18 Created by HuangJP
     */
    int Add_Detection_Test_Result(DetectionTestResult test_result)
    {
        static std::unordered_set<std::string> samples;

        // 检查模型是否适用于目标检测任务
        if (_model_data.task_type != TASK_TYPE_DETECTION)
        {
            return AIENGINE_TASK_TYPE_ERROR;
        }

        _number_of_tests++; // 记录测试次数

        // 添加新的测试样本
        if (samples.find(test_result.test_img) == samples.end())
        {
            samples.emplace(test_result.test_img);
            _number_of_test_samples++; // 记录测试样本数
        }

        _detection_test_results.push_back(test_result);
        return AIENGINE_NO_ERROR;
    }

    /**
     * @brief    
     *           输出测试信息和测试结果到文件中
     *           
     * @param    file_path:     输出文件路径
     *           
     * @retval   错误码
     *           
     * @date     2024-04-18 Created by HuangJP
     */
    int Write(const char *file_path)
    {
        std::ofstream fs;

        fs.open(file_path, std::ios_base::out | std::ios_base::trunc); // 以截断写入模式打开文件，若文件存在，则会清空文件原有内容
        
        // 判断文件是否打开成功
        if (fs.is_open() == false)
        {
            LOGE("Failed to open file");
            LOGD("Try to open file: %s", file_path);
            return AIENGINE_OPEN_FILE_ERROR;
        }

        // 定义用于枚举转字符串的映射表
        std::unordered_map<task_type_t, std::string> task_type2string = {
            {TASK_TYPE_DETECTION, "TASK_TYPE_DETECTION"},
        }; // 模型任务类型

        std::unordered_map<BaseModelLoader::backend_t, std::string> backend2string = {
            {BaseModelLoader::AIENGINE_BACKEND_CPU, "AIENGINE_BACKEND_CPU"},
            {BaseModelLoader::AIENGINE_BACKEND_GPU, "AIENGINE_BACKEND_GPU"},
            {BaseModelLoader::AIENGINE_BACKEND_IPU, "AIENGINE_BACKEND_IPU"},
            {BaseModelLoader::AIENGINE_BACKEND_NN, "AIENGINE_BACKEND_NN"},
        }; // 模型推理后端

        std::unordered_map<BaseModelLoader::precision_t, std::string> precision2string = {
            {BaseModelLoader::AIENGINE_PRECISION_LOW, "AIENGINE_PRECISION_LOW"},
            {BaseModelLoader::AIENGINE_PRECISION_LOW_BF16, "AIENGINE_PRECISION_LOW_BF16"},
            {BaseModelLoader::AIENGINE_PRECISION_NORMAL, "AIENGINE_PRECISION_NORMAL"},
            {BaseModelLoader::AIENGINE_PRECISION_HIGH, "AIENGINE_PRECISION_HIGH"},
        }; // 模型推理精度

        std::unordered_map<BaseModelLoader::memory_format_t, std::string> memory_format2string = {
            {BaseModelLoader::AIENGINE_MEMORY_FORMAT_NCHW, "AIENGINE_MEMORY_FORMAT_NCHW"},
            {BaseModelLoader::AIENGINE_MEMORY_FORMAT_NC4HW4, "AIENGINE_MEMORY_FORMAT_NC4HW4"},
            {BaseModelLoader::AIENGINE_MEMORY_FORMAT_NHWC, "AIENGINE_MEMORY_FORMAT_NHWC"},
        }; // 模型张量内存存储格式

        // 按照JSON格式写文件
        fs << "{" << std::endl;
        {
            // 写"information"，包括测试信息和模型应用信息
            fs << std::setw(4) << "" << "\"information\" : {" << std::endl;
            {
                // 写测试信息
                fs << std::setw(8) << "" << "\"test_info\" : {" << std::endl;
                {
                    fs << std::setw(12) << "" << "\"number_of_tests\" : " << _number_of_tests;
                    fs << "," << std::endl << std::setw(12) << "" << "\"test_set\" : " << "\"" << _test_set << "\"";
                    fs << "," << std::endl << std::setw(12) << "" << "\"number_of_test_samples\" : " << _number_of_test_samples;
                }
                fs << std::endl << std::setw(8) << "" << "}";

                // 写模型应用信息
                fs << "," << std::endl;
                fs << std::setw(8) << "" << "\"model_application_info\" : {" << std::endl;
                {
                    fs << std::setw(12) << "" << "\"name\" : " << "\"" << _model_data.name << "\"";
                    fs << "," << std::endl << std::setw(12) << "" << "\"version\" : " << "\"" << _model_data.version << "\"";
                    fs << "," << std::endl << std::setw(12) << "" << "\"data_size\" : " << _model_data.data_size;
                    fs << "," << std::endl << std::setw(12) << "" << "\"task_type\" : " << "\"" << task_type2string[_model_data.task_type] << "\"";
                    fs << "," << std::endl << std::setw(12) << "" << "\"backend\" : " << "\"" << backend2string[_model_data.backend] << "\"";
                    fs << "," << std::endl << std::setw(12) << "" << "\"precision\" : " << "\"" << precision2string[_model_data.precision] << "\"";
                    fs << "," << std::endl << std::setw(12) << "" << "\"memory_format\" : " << "\"" << memory_format2string[_model_data.memory_format] << "\"";
                    fs << "," << std::endl << std::setw(12) << "" << "\"num_threads\" : " << _model_data.num_threads;
                }
                fs << std::endl << std::setw(8) << "" << "}";
            }
            fs << std::endl << std::setw(4) << "" << "}";

            // 写"detection_test_results"，用于记录目标检测任务的输出结果
            if (_detection_test_results.size() != 0)
            {
                auto iter = _detection_test_results.begin();
                fs << "," << std::endl;
                fs << std::setw(4) << "" << "\"detection_test_results\" : [" << std::endl;

                // 逐个写测试数据
                while (1)
                {
                    fs << std::setw(8) << "" << "{" << std::endl;
                    {
                        fs << std::setw(12) << "" << "\"test_img\" : " << "\"" << iter->test_img << "\"";
                        fs << "," << std::endl << std::setw(12) << "" << "\"elapsed_time\" : " << iter->elapsed_time;
                        fs << "," << std::endl << std::setw(12) << "" << "\"results\" : [";

                        // 判断是否有检测结果
                        if (iter->results.size() != 0)
                        {
                            fs << std::endl;
                            auto det_iter = iter->results.begin();

                            // 逐个写检测结果
                            while (1)
                            {
                                fs << std::setw(16) << "";
                                fs << "{";
                                fs << "\"class_id\" : " << det_iter->classID;
                                fs << ", " << "\"score\" : " << det_iter->score;
                                fs << ", " << "\"xmin\" : " << det_iter->xmin;
                                fs << ", " << "\"ymin\" : " << det_iter->ymin;
                                fs << ", " << "\"xmax\" : " << det_iter->xmax;
                                fs << ", " << "\"ymax\" : " << det_iter->ymax;
                                fs << "}";

                                det_iter++;
                                if (det_iter == iter->results.end())
                                {
                                    break;
                                }
                                fs << "," << std::endl;
                            }
                        }
                        fs << std::endl << std::setw(12) << "" << "]";
                    }
                    fs << std::endl << std::setw(8) << "" << "}";

                    // 判断是否所有结果均保存完毕
                    iter++;
                    if (iter == _detection_test_results.end())
                    {
                        break;
                    }
                    fs << "," << std::endl;
                }
                fs << std::endl << std::setw(4) << "" << "]";
            }
        }
        fs << std::endl << "}" << std::endl;

        return AIENGINE_NO_ERROR;
    }

protected:
    const char *_test_set; // 测试集
    const BaseModelLoader::ModelData _model_data; // 模型数据
    int _number_of_tests; // 测试次数
    int _number_of_test_samples; // 测试样本数
    std::vector<DetectionTestResult> _detection_test_results; // 目标检测测试结果列表
};
#endif
//-----------------------------------------------------------------------------
//  End of file