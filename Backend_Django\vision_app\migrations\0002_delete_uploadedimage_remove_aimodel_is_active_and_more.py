# Generated by Django 5.2.1 on 2025-05-08 11:39

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("vision_app", "0001_initial"),
    ]

    operations = [
        migrations.DeleteModel(
            name="UploadedImage",
        ),
        migrations.RemoveField(
            model_name="aimodel",
            name="is_active",
        ),
        migrations.RemoveField(
            model_name="aimodel",
            name="uploaded_at",
        ),
        migrations.AlterField(
            model_name="aimodel",
            name="model_file",
            field=models.Char<PERSON>ield(
                help_text="Path relative to system_models/ directory", max_length=255
            ),
        ),
        migrations.AlterField(
            model_name="aimodel",
            name="model_type",
            field=models.Char<PERSON>ield(
                help_text="e.g., barcode, ocr, ai_restored", max_length=50
            ),
        ),
        migrations.AlterField(
            model_name="aimodel",
            name="name",
            field=models.Char<PERSON><PERSON>(max_length=255, unique=True),
        ),
    ]
