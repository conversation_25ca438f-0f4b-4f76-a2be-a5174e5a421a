---
description: Explore the MaskDecoder and MLP modules in Ultralytics for efficient mask prediction using transformer architecture. Detailed attributes, functionalities, and implementation.
keywords: Ultralytics, MaskDecoder, MLP, machine learning, transformer architecture, mask prediction, neural networks, PyTorch modules
---

# Reference for `ultralytics/models/sam/modules/decoders.py`

!!! note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/sam/modules/decoders.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/sam/modules/decoders.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/models/sam/modules/decoders.py) 🛠️. Thank you 🙏!

<br>

## ::: ultralytics.models.sam.modules.decoders.MaskDecoder

<br><br><hr><br>

## ::: ultralytics.models.sam.modules.decoders.SAM2MaskDecoder

<br><br>
