import React, { createContext, useState, useContext, ReactNode, useEffect } from 'react';
import { adminLogin, adminLogout, adminCheckStatus, AdminLoginParams, AdminApiResponse, AdminStatusResponse } from '../services/api';
import { App } from 'antd';

// --- Types ---

export interface AdminContextType {
  isAdmin: boolean;
  isLoading: boolean;
  loginTime?: string;
  login: (password: string) => Promise<boolean>;
  logout: () => Promise<void>;
  checkStatus: () => Promise<void>;
}

// --- Context ---

const AdminContext = createContext<AdminContextType | undefined>(undefined);

// --- Provider ---

interface AdminProviderProps {
  children: ReactNode;
}

export const AdminProvider: React.FC<AdminProviderProps> = ({ children }) => {
  const [isAdmin, setIsAdmin] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [loginTime, setLoginTime] = useState<string | undefined>(undefined);

  // 使用App组件的message API
  const { message } = App.useApp();

  // 检查管理员状态
  const checkStatus = async (): Promise<void> => {
    try {
      setIsLoading(true);
      const response: AdminStatusResponse = await adminCheckStatus();

      if (response.success) {
        setIsAdmin(response.is_admin);
        setLoginTime(response.login_time);
      } else {
        setIsAdmin(false);
        setLoginTime(undefined);
      }
    } catch (error) {
      console.error('检查管理员状态失败:', error);
      setIsAdmin(false);
      setLoginTime(undefined);
    } finally {
      setIsLoading(false);
    }
  };

  // 管理员登录
  const login = async (password: string): Promise<boolean> => {
    try {
      setIsLoading(true);
      const params: AdminLoginParams = { password };
      const response: AdminApiResponse = await adminLogin(params);

      if (response.success) {
        setIsAdmin(true);
        setLoginTime(new Date().toISOString());
        // 移除 await checkStatus() 调用，避免重复设置状态导致组件重复渲染
        // await checkStatus();
        return true;
      } else {
        message.error(response.message || '登录失败');
        return false;
      }
    } catch (error: any) {
      console.error('管理员登录失败:', error);
      message.error(error.message || '登录失败，请稍后重试');
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  // 管理员登出
  const logout = async (): Promise<void> => {
    try {
      setIsLoading(true);
      const response: AdminApiResponse = await adminLogout();

      if (response.success) {
        setIsAdmin(false);
        setLoginTime(undefined);
        message.success(response.message || '管理员登出成功');
        await checkStatus();
      } else {
        message.error(response.message || '登出失败');
      }
    } catch (error: any) {
      console.error('管理员登出失败:', error);
      message.error(error.message || '登出失败，请稍后重试');
    } finally {
      setIsLoading(false);
    }
  };

  // 组件挂载时检查状态
  useEffect(() => {
    checkStatus();
  }, []);

  const contextValue: AdminContextType = {
    isAdmin,
    isLoading,
    loginTime,
    login,
    logout,
    checkStatus,
  };

  return (
    <AdminContext.Provider value={contextValue}>
      {children}
    </AdminContext.Provider>
  );
};

// --- Hook ---

export const useAdmin = (): AdminContextType => {
  const context = useContext(AdminContext);
  if (context === undefined) {
    throw new Error('useAdmin must be used within an AdminProvider');
  }
  return context;
};

export default AdminContext;
