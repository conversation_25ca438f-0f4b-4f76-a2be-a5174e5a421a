import React, { createContext, useState, useContext, ReactNode } from 'react';

// --- Types ---

// 定义API返回的匹配结果结构
export interface MatchResult {
    box_corners: [number, number][];
    center_point: [number, number];
    angle: number;
}

// 定义API返回的完整数据结构
export interface FeatureMatchingResult {
    status: 'success' | 'error';
    message: string;
    algorithm_used: 'SIFT' | 'ORB';
    keypoints_info: {
        template_keypoints: number;
        target_keypoints: number;
        good_matches: number;
    };
    match_result?: MatchResult;
    image_base64?: string; // 可选的可视化结果图
    processing_time: number; // 添加处理耗时字段（单位：秒）
}


interface FeatureMatchingTraditionalContextType {
    // 模板图像
    templateImage: File | null;
    setTemplateImage: (file: File | null) => void;
    templatePreview: string | null;
    setTemplatePreview: (url: string | null) => void;

    // 目标图像
    targetImage: File | null;
    setTargetImage: (file: File | null) => void;
    targetPreview: string | null;
    setTargetPreview: (url: string | null) => void;

    // ROI信息 (当模板来源于ROI时)
    roi: { x: number; y: number; width: number; height: number; } | null;
    setRoi: (roi: { x: number; y: number; width: number; height: number; } | null) => void;

    // 参数
    algorithm: 'SIFT' | 'ORB';
    setAlgorithm: (algorithm: 'SIFT' | 'ORB') => void;
    matchRatioThreshold: number;
    setMatchRatioThreshold: (threshold: number) => void;
    minMatchCount: number;
    setMinMatchCount: (count: number) => void;

    // 运行状态
    isMatching: boolean;
    setIsMatching: (isMatching: boolean) => void;

    // 结果
    matchingResult: FeatureMatchingResult | null;
    setMatchingResult: (result: FeatureMatchingResult | null) => void;
}

// --- Context Creation ---
const FeatureMatchingTraditionalContext = createContext<FeatureMatchingTraditionalContextType | undefined>(undefined);

// --- Provider Component ---
interface FeatureMatchingTraditionalProviderProps {
    children: ReactNode;
}

export const FeatureMatchingTraditionalProvider: React.FC<FeatureMatchingTraditionalProviderProps> = ({ children }) => {
    const [templateImage, setTemplateImage] = useState<File | null>(null);
    const [templatePreview, setTemplatePreview] = useState<string | null>(null);
    const [targetImage, setTargetImage] = useState<File | null>(null);
    const [targetPreview, setTargetPreview] = useState<string | null>(null);
    const [roi, setRoi] = useState<{ x: number; y: number; width: number; height: number; } | null>(null);

    const [algorithm, setAlgorithm] = useState<'SIFT' | 'ORB'>('SIFT');
    const [matchRatioThreshold, setMatchRatioThreshold] = useState<number>(0.7);
    const [minMatchCount, setMinMatchCount] = useState<number>(10);
    const [isMatching, setIsMatching] = useState<boolean>(false);
    const [matchingResult, setMatchingResult] = useState<FeatureMatchingResult | null>(null);

    const contextValue = React.useMemo(() => ({
        templateImage, setTemplateImage,
        templatePreview, setTemplatePreview,
        targetImage, setTargetImage,
        targetPreview, setTargetPreview,
        roi, setRoi,
        algorithm, setAlgorithm,
        matchRatioThreshold, setMatchRatioThreshold,
        minMatchCount, setMinMatchCount,
        isMatching, setIsMatching,
        matchingResult, setMatchingResult,
    }), [
        templateImage,
        templatePreview,
        targetImage,
        targetPreview,
        roi,
        algorithm,
        matchRatioThreshold,
        minMatchCount,
        isMatching,
        matchingResult,
    ]);

    return (
        <FeatureMatchingTraditionalContext.Provider value={contextValue}>
            {children}
        </FeatureMatchingTraditionalContext.Provider>
    );
};

// --- Hook for consuming context ---
export const useFeatureMatchingTraditional = (): FeatureMatchingTraditionalContextType => {
    const context = useContext(FeatureMatchingTraditionalContext);
    if (context === undefined) {
        throw new Error('useFeatureMatchingTraditional must be used within a FeatureMatchingTraditionalProvider');
    }
    return context;
};