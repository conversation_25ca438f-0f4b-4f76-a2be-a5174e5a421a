"""
Docker环境Django设置
专门为Docker容器环境优化的配置
"""

from pathlib import Path
import os

# 导入Docker配置管理器
from .config.docker_config.manager import DockerConfigManager

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

# 初始化Docker配置管理器
docker_config_manager = DockerConfigManager(BASE_DIR)

# 获取所有Docker配置
_all_configs = docker_config_manager.get_all_configs()

# 将配置应用到当前模块的全局变量中
globals().update(_all_configs)

# 添加Docker环境的调试信息
print("=== Django Docker Settings Loaded ===")
print(f"DEBUG: {globals().get('DEBUG', 'Not Set')}")
print(f"ALLOWED_HOSTS: {globals().get('ALLOWED_HOSTS', 'Not Set')}")
print(f"DATABASE: {globals().get('DATABASES', {}).get('default', {}).get('NAME', 'Not Set')}")
print(f"MEDIA_ROOT: {globals().get('MEDIA_ROOT', 'Not Set')}")
print(f"SYSTEM_MODELS_ROOT: {globals().get('SYSTEM_MODELS_ROOT', 'Not Set')}")
print(f"EXAMPLE_IMAGES_ROOT: {globals().get('EXAMPLE_IMAGES_ROOT', 'Not Set')}")
