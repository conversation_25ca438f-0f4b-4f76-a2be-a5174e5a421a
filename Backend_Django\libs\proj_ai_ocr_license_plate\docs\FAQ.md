---
comments: true
hide:
  - navigation
  - toc
---

> 恭喜你发现宝藏！

PaddleOCR收集整理了自从开源以来在issues和用户群中的常见问题并且给出了简要解答，旨在为OCR的开发者提供一些参考，也希望帮助大家少走一些弯路。

其中[通用问题](#1)一般是初次接触OCR相关算法时用户会提出的问题，在[1.5 垂类场景实现思路](#15)中总结了如何在一些具体的场景中确定技术路线进行优化。[PaddleOCR常见问题](#2-paddleocr)是开发者在使用PaddleOCR之后可能会遇到的问题也是PaddleOCR实践过程中的避坑指南。

同时PaddleOCR也会在review issue的过程中添加 `good issue`、 `good first issue` 标签，但这些问题可能不会被立刻补充在FAQ文档里，开发者也可对应查看。我们也非常希望开发者能够帮助我们将这些内容补充在FAQ中。

OCR领域大佬众多，本文档回答主要依赖有限的项目实践，难免挂一漏万，如有遗漏和不足，也**希望有识之士帮忙补充和修正**，万分感谢。

## 1. 通用问题

### 1.1 检测

#### Q: 基于深度学习的文字检测方法有哪几种？各有什么优缺点？

**A**：常用的基于深度学习的文字检测方法一般可以分为基于回归的、基于分割的两大类，当然还有一些将两者进行结合的方法。

（1）基于回归的方法分为box回归和像素值回归。a. 采用box回归的方法主要有CTPN、Textbox系列和EAST，这类算法对规则形状文本检测效果较好，但无法准确检测不规则形状文本。 b. 像素值回归的方法主要有CRAFT和SA-Text，这类算法能够检测弯曲文本且对小文本效果优秀但是实时性能不够。

（2）基于分割的算法，如PSENet，这类算法不受文本形状的限制，对各种形状的文本都能取得较好的效果，但是往往后处理比较复杂，导致耗时严重。目前也有一些算法专门针对这个问题进行改进，如DB，将二值化进行近似，使其可导，融入训练，从而获取更准确的边界，大大降低了后处理的耗时。

### 1.2 识别

#### Q: PaddleOCR提供的文本识别算法包括哪些？

**A**：PaddleOCR主要提供五种文本识别算法，包括CRNN\StarNet\RARE\Rosetta和SRN, 其中CRNN\StarNet和Rosetta是基于ctc的文字识别算法，RARE是基于attention的文字识别算法；SRN为百度自研的文本识别算法，引入了语义信息，显著提升了准确率。 详情可参照如下页面: 文本识别算法

#### Q: 文本识别方法CRNN关键技术有哪些？

**A**：CRNN 关键技术包括三部分。（1）CNN提取图像卷积特征。（2）深层双向LSTM网络，在卷积特征的基础上继续提取文字序列特征。（3）Connectionist Temporal Classification(CTC)，解决训练时字符无法对齐的问题。

#### Q: 对于中文行文本识别，CTC和Attention哪种更优？

**A**：（1）从效果上来看，通用OCR场景CTC的识别效果优于Attention，因为带识别的字典中的字符比较多，常用中文汉字三千字以上，如果训练样本不足的情况下，对于这些字符的序列关系挖掘比较困难。中文场景下Attention模型的优势无法体现。而且Attention适合短语句识别，对长句子识别比较差。

（2）从训练和预测速度上，Attention的串行解码结构限制了预测速度，而CTC网络结构更高效，预测速度上更有优势。

#### Q: 弯曲形变的文字识别需要怎么处理？TPS应用场景是什么，是否好用？

**A**：（1）在大多数情况下，如果遇到的场景弯曲形变不是太严重，检测4个顶点，然后直接通过仿射变换转正识别就足够了。

（2）如果不能满足需求，可以尝试使用TPS（Thin Plate Spline），即薄板样条插值。TPS是一种插值算法，经常用于图像变形等，通过少量的控制点就可以驱动图像进行变化。一般用在有弯曲形变的文本识别中，当检测到不规则的/弯曲的（如，使用基于分割的方法检测算法）文本区域，往往先使用TPS算法对文本区域矫正成矩形再进行识别，如，STAR-Net、RARE等识别算法中引入了TPS模块。

> **Warning**：TPS看起来美好，在实际应用时经常发现并不够鲁棒，并且会增加耗时，需要谨慎使用。

### 1.3 端到端

#### Q: 请问端到端的pgnet相比于DB+CRNN在准确率上有优势吗？或者是pgnet最擅长的场景是什么场景呢？

**A**：pgnet是端到端算法，检测识别一步到位，不用分开训练2个模型，也支持弯曲文本的识别，但是在中文上的效果还没有充分验证；db+crnn的验证更充分，应用相对成熟，常规非弯曲的文本都能解的不错。

#### Q: 目前OCR普遍是二阶段，端到端的方案在业界落地情况如何？

**A**：端到端在文字分布密集的业务场景，效率会比较有保证，精度的话看自己业务数据积累情况，如果行级别的识别数据积累比较多的话two-stage会比较好。百度的落地场景，比如工业仪表识别、车牌识别都用到端到端解决方案。

#### Q: 二阶段的端到端的场景文本识别方法的不足有哪些？

**A**：这类方法一般需要设计针对ROI提取特征的方法，而ROI操作一般比较耗时。

#### Q: AAAI 2021最新的端到端场景文本识别PGNet算法有什么特点？

**A**：PGNet不需要字符级别的标注，NMS操作以及ROI操作。同时提出预测文本行内的阅读顺序模块和基于图的修正模块来提升文本识别效果。该算法是百度自研，近期会在PaddleOCR开源。

### 1.4 评估方法

#### Q: OCR领域常用的评估指标是什么？

**A**：对于两阶段的可以分开来看，分别是检测和识别阶段

（1）检测阶段：先按照检测框和标注框的IOU评估，IOU大于某个阈值判断为检测准确。这里检测框和标注框不同于一般的通用目标检测框，是采用多边形进行表示。检测准确率：正确的检测框个数在全部检测框的占比，主要是判断检测指标。检测召回率：正确的检测框个数在全部标注框的占比，主要是判断漏检的指标。

（2）识别阶段：
字符识别准确率，即正确识别的文本行占标注的文本行数量的比例，只有整行文本识别对才算正确识别。

（3）端到端统计：
端对端召回率：准确检测并正确识别文本行在全部标注文本行的占比；
端到端准确率：准确检测并正确识别文本行在 检测到的文本行数量 的占比；
准确检测的标准是检测框与标注框的IOU大于某个阈值，正确识别的检测框中的文本与标注的文本相同。

### 1.5 垂类场景实现思路

#### Q：背景干扰的文字（如印章盖到落款上，需要识别落款或者印章中的文字），如何识别？

**A**：（1）在人眼确认可识别的条件下，对于背景有干扰的文字，首先要保证检测框足够准确，如果检测框不准确，需要考虑是否可以通过过滤颜色等方式对图像预处理并且增加更多相关的训练数据；在识别的部分，注意在训练数据中加入背景干扰类的扩增图像。

（2）如果MobileNet模型不能满足需求，可以尝试ResNet系列大模型来获得更好的效果。

#### Q：请问对于图片中的密集文字，有什么好的处理办法吗？

**A**：可以先试用预训练模型测试一下，例如DB+CRNN，判断下密集文字图片中是检测还是识别的问题，然后针对性的改善。还有一种是如果图象中密集文字较小，可以尝试增大图像分辨率，对图像进行一定范围内的拉伸，将文字稀疏化，提高识别效果。

#### Q： 文本行较紧密的情况下如何准确检测？

**A**：使用基于分割的方法，如DB，检测密集文本行时，最好收集一批数据进行训练，并且在训练时，并将生成二值图像的shrink_ratio参数调小一些。

#### Q：对于一些在识别时稍微模糊的文本，有没有一些图像增强的方式？

**A**：在人类肉眼可以识别的前提下，可以考虑图像处理中的均值滤波、中值滤波或者高斯滤波等模糊算子尝试。也可以尝试从数据扩增扰动来强化模型鲁棒性，另外新的思路有对抗性训练和超分SR思路，可以尝试借鉴。但目前业界尚无普遍认可的最优方案，建议优先在数据采集阶段增加一些限制提升图片质量。

#### Q：低像素文字或者字号比较小的文字有什么超分辨率方法吗

**A**：超分辨率方法分为传统方法和基于深度学习的方法。基于深度学习的方法中，比较经典的有SRCNN，另外CVPR2020也有一篇超分辨率的工作可以参考文章：Unpaired Image Super-Resolution using Pseudo-Supervision，但是没有充分的实践验证过，需要看实际场景下的效果。

#### Q：对于一些尺寸较大的文档类图片，在检测时会有较多的漏检，怎么避免这种漏检的问题呢？

**A**：PaddleOCR中在图像最长边大于960时，将图像等比例缩放为长边960的图像再进行预测，对于这种图像，可以通过修改det_limit_side_len，增大检测的最长边：tools/infer/utility.py#L42

#### Q：文档场景中，使用DB模型会出现整行漏检的情况应该怎么解决？

**A**：可以在预测时调小 det_db_box_thresh 阈值，默认为0.5, 可调小至0.3观察效果。

#### Q: 弯曲文本（如略微形变的文档图像）漏检问题

**A**: db后处理中计算文本框平均得分时，是求rectangle区域的平均分数，容易造成弯曲文本漏检，已新增求polygon区域的平均分数，会更准确，但速度有所降低，可按需选择，在相关pr中可查看[可视化对比效果](https://github.com/PaddlePaddle/PaddleOCR/pull/2604)。该功能通过参数 [det_db_score_mode](https://github.com/PaddlePaddle/PaddleOCR/blob/release/2.1/tools/infer/utility.py#L51)进行选择，参数值可选[`fast`(默认)、`slow`]，`fast`对应原始的rectangle方式，`slow`对应polygon方式。感谢用户[buptlihang](https://github.com/buptlihang)提[pr](https://github.com/PaddlePaddle/PaddleOCR/pull/2574)帮助解决该问题🌹。

#### Q：如何识别文字比较长的文本？

**A**：在中文识别模型训练时，并不是采用直接将训练样本缩放到[3,32,320]进行训练，而是先等比例缩放图像，保证图像高度为32，宽度不足320的部分补0，宽高比大于10的样本直接丢弃。预测时，如果是单张图像预测，则按上述操作直接对图像缩放，不做宽度320的限制。如果是多张图预测，则采用batch方式预测，每个batch的宽度动态变换，采用这个batch中最长宽度。

#### Q：如何识别带空格的英文行文本图像？

**A**：空格识别可以考虑以下两种方案：

(1)优化文本检测算法。检测结果在空格处将文本断开。这种方案在检测数据标注时，需要将含有空格的文本行分成好多段。

(2)优化文本识别算法。在识别字典里面引入空格字符，然后在识别的训练数据中，如果用空行，进行标注。此外，合成数据时，通过拼接训练数据，生成含有空格的文本。

#### Q：弯曲文本有试过opencv的TPS进行弯曲校正吗？

**A**：opencv的tps需要标出上下边界对应的点，这个点很难通过传统方法或者深度学习方法获取。PaddleOCR里StarNet网络中的tps模块实现了自动学点，自动校正，可以直接尝试这个。

#### Q: 如何识别招牌或者广告图中的艺术字？

**A**: 招牌或者广告图中的艺术字是文本识别一个非常有挑战性的难题，因为艺术字中的单字和印刷体相比，变化非常大。如果需要识别的艺术字是在一个词典列表内，可以将改每个词典认为是一个待识别图像模板，通过通用图像检索识别系统解决识别问题。可以尝试使用PaddleClas的图像识别系统。

#### Q: 印章如何识别

**A**：1. 使用带tps的识别网络或abcnet,2.使用极坐标变换将图片拉平之后使用crnn

#### Q: 使用预训练模型进行预测，对于特定字符识别识别效果较差，怎么解决？

**A**: 由于我们所提供的识别模型是基于通用大规模数据集进行训练的，部分字符可能在训练集中包含较少，因此您可以构建特定场景的数据集，基于我们提供的预训练模型进行微调。建议用于微调的数据集中，每个字符出现的样本数量不低于300，但同时需要注意不同字符的数量均衡。具体可以参考：微调。

#### Q: 在使用训练好的识别模型进行预测的时候，发现有很多重复的字，这个怎么解决呢？

**A**：可以看下训练的尺度和预测的尺度是否相同，如果训练的尺度为[3, 32, 320]，预测的尺度为[3, 64, 640]，则会有比较多的重复识别现象。

#### Q: 图像正常识别出来的文字是OK的，旋转90度后识别出来的结果就比较差，有什么方法可以优化？

**A**:  整图旋转90之后效果变差是有可能的，因为目前PPOCR默认输入的图片是正向的； 可以自己训练一个整图的方向分类器，放在预测的最前端（可以参照现有方向分类器的方式），或者可以基于规则做一些预处理，比如判断长宽等等。

#### Q: 如何识别竹简上的古文？

**A**：对于字符都是普通的汉字字符的情况，只要标注足够的数据，finetune模型就可以了。如果数据量不足，您可以尝试[StyleText](https://github.com/PFCCLab/StyleText)工具。
而如果使用的字符是特殊的古文字、甲骨文、象形文字等，那么首先需要构建一个古文字的字典，之后再进行训练。

#### Q: 只想要识别票据中的部分片段，重新训练它的话，只需要训练文本检测模型就可以了吗？问文本识别，方向分类还是用原来的模型这样可以吗？

**A**：可以的。PaddleOCR的检测、识别、方向分类器三个模型是独立的，在实际使用中可以优化和替换其中任何一个模型。

#### Q: 如何用PaddleOCR识别视频中的文字？

**A**: 目前PaddleOCR主要针对图像做处理，如果需要视频识别，可以先对视频抽帧，然后用PPOCR识别。

#### Q: 相机采集的图像为四通道，应该如何处理？

**A**: 有两种方式处理：

- 如果没有其他需要，可以在解码数据的时候指定模式为三通道，例如如果使用opencv，可以使用cv::imread(img_path, cv::IMREAD_COLOR)。
- 如果其他模块需要处理四通道的图像，那也可以在输入PaddleOCR模块之前进行转换，例如使用cvCvtColor(&img,img3chan,CV_RGBA2RGB)。

#### Q: 遇到中英文识别模型不支持的字符，该如何对模型做微调？

**A**：如果希望识别中英文识别模型中不支持的字符，需要更新识别的字典，并完成微调过程。比如说如果希望模型能够进一步识别罗马数字，可以按照以下步骤完成模型微调过程。

1. 准备中英文识别数据以及罗马数字的识别数据，用于训练，同时保证罗马数字和中英文识别数字的效果；
2. 修改默认的字典文件，在后面添加罗马数字的字符；
3. 下载PaddleOCR提供的预训练模型，配置预训练模型和数据的路径，开始训练。

#### Q：特殊字符（例如一些标点符号）识别效果不好怎么办？

**A**：首先请您确认要识别的特殊字符是否在字典中。
如果字符在已经字典中但效果依然不好，可能是由于识别数据较少导致的，您可以增加相应数据finetune模型。

---

#### Q：单张图上多语种并存识别（如单张图印刷体和手写文字并存），应该如何处理？

**A**：单张图像中存在多种类型文本的情况很常见，典型的以学生的试卷为代表，一张图像同时存在手写体和印刷体两种文本，这类情况下，可以尝试”1个检测模型+1个N分类模型+N个识别模型”的解决方案。
其中不同类型文本共用同一个检测模型，N分类模型指额外训练一个分类器，将检测到的文本进行分类，如手写+印刷的情况就是二分类，N种语言就是N分类，在识别的部分，针对每个类型的文本单独训练一个识别模型，如手写+印刷的场景，就需要训练一个手写体识别模型，一个印刷体识别模型，如果一个文本框的分类结果是手写体，那么就传给手写体识别模型进行识别，其他情况同理。

#### Q: 多语言的字典里是混合了不同的语种，这个是有什么讲究吗？统一到一个字典里会对精度造成多大的损失？

**A**：统一到一个字典里，会造成最后一层FC过大，增加模型大小。如果有特殊需求的话，可以把需要的几种语言合并字典训练模型，合并字典之后如果引入过多的形近字，可能会造成精度损失，字符平衡的问题可能也需要考虑一下。在PaddleOCR里暂时将语言字典分开。

#### Q：类似泰语这样的小语种，部分字会占用两个字符甚至三个字符，请问如何制作字典

**A**：处理字符的时候，把多字符的当作一个字就行，字典中每行是一个字。

---

#### Q: 想把简历上的文字识别出来后，能够把关系一一对应起来，比如姓名和它后面的名字组成一对，籍贯、邮箱、学历等等都和各自的内容关联起来，这个应该如何处理，PPOCR目前支持吗？

**A**:  这样的需求在企业应用中确实比较常见，但往往都是个性化的需求，没有非常规整统一的处理方式。常见的处理方式有如下两种：

1. 对于单一版式、或者版式差异不大的应用场景，可以基于识别场景的一些先验信息，将识别内容进行配对； 比如运用表单结构信息：常见表单"姓名"关键字的后面，往往紧跟的就是名字信息
2. 对于版式多样，或者无固定版式的场景， 需要借助于NLP中的NER技术，给识别内容中的某些字段，赋予key值

由于这部分需求和业务场景强相关，难以用一个统一的模型去处理，目前PPOCR暂不支持。 如果需要用到NER技术，可以参照Paddle团队的另一个开源套件: [PaddlePaddle/ERNIE](https://github.com/PaddlePaddle/ERNIE)， 其提供的预训练模型ERNIE,  可以帮助提升NER任务的准确率。

### 1.6 训练过程与模型调优

#### Q: 增大batch_size模型训练速度没有明显提升

**A**：如果batch_size打得太大，加速效果不明显的话，可以试一下增大初始化内存的值，运行代码前设置环境变量：
export FLAGS_initial_cpu_memory_in_mb=2000  # 设置初始化内存约2G左右

#### Q: 预测时提示图像过大，显存、内存溢出了，应该如何处理？

**A**：可以按照这个PR的修改来缓解显存、内存占用 #2230

#### Q: 识别训练时，训练集精度已经到达90了，但验证集精度一直在70，涨不上去怎么办？

**A**：训练集精度90，测试集70多的话，应该是过拟合了，有两个可尝试的方法：（1）加入更多的增广方式或者调大增广prob的概率，默认为0.4。（2）调大系统的l2 decay值

### 1.7 补充资料

#### Q: 对于小白如何快速入门中文OCR项目实践？

**A**：建议可以先了解OCR方向的基础知识，大概了解基础的检测和识别模型算法。然后在Github上可以查看OCR方向相关的repo。目前来看，从内容的完备性来看，PaddleOCR的中英文双语教程文档是有明显优势的，在数据集、模型训练、预测部署文档详实，可以快速入手。而且还有微信用户群答疑，非常适合学习实践。项目地址：PaddleOCR AI 快车道课程：<https://aistudio.baidu.com/aistudio/course/introduce/1519>

## 2. PaddleOCR实战问题

### 2.1 PaddleOCR repo

#### Q: PaddleOCR develop分支和dygraph分支的区别？

**A**：目前PaddleOCR有四个分支，分别是：

- develop：基于Paddle静态图开发的分支，推荐使用paddle1.8 或者2.0版本，该分支具备完善的模型训练、预测、推理部署、量化裁剪等功能，领先于release/1.1分支。
- release/1.1：PaddleOCR 发布的第一个稳定版本，基于静态图开发，具备完善的训练、预测、推理部署、量化裁剪等功能。
- dygraph：基于Paddle动态图开发的分支，目前仍在开发中，未来将作为主要开发分支，运行要求使用Paddle2.0.0版本。
- release/2.0-rc1-0：PaddleOCR发布的第二个稳定版本，基于动态图和paddle2.0版本开发，动态图开发的工程更易于调试，目前支，支持模型训练、预测，暂不支持移动端部署。

如果您已经上手过PaddleOCR，并且希望在各种环境上部署PaddleOCR，目前建议使用静态图分支，develop或者release/1.1分支。如果您是初学者，想快速训练，调试PaddleOCR中的算法，建议尝鲜PaddleOCR dygraph分支。

**注意**：develop和dygraph分支要求的Paddle版本、本地环境有差别，请注意不同分支环境安装部分的差异。

#### Q：PaddleOCR与百度的其他OCR产品有什么区别？

**A**：PaddleOCR主要聚焦通用ocr，如果有垂类需求，您可以用PaddleOCR+垂类数据自己训练；
如果缺少带标注的数据，或者不想投入研发成本，建议直接调用开放的API，开放的API覆盖了目前比较常见的一些垂类。

### 2.2 安装环境

#### Q：OSError： [WinError 126] 找不到指定的模块。mac pro python 3.4 shapely import 问题

**A**：这个问题是因为shapely库安装有误，可以参考 #212 这个issue重新安装一下

#### Q：PaddlePaddle怎么指定GPU运行 os.environ["CUDA_VISIBLE_DEVICES"]这种不生效

**A**：通过设置 export CUDA_VISIBLE_DEVICES='0'环境变量

#### Q：PaddleOCR是否支持在Windows或Mac系统上运行？

**A**：PaddleOCR已完成Windows和Mac系统适配，运行时注意两点：
（1）在快速安装时，如果不想安装docker，可跳过第一步，直接从第二步安装paddle开始。
（2）inference模型下载时，如果没有安装wget，可直接点击模型链接或将链接地址复制到浏览器进行下载，并解压放置到相应目录。

### 2.3 数据量说明

#### Q：简单的对于精度要求不高的OCR任务，数据集需要准备多少张呢？

**A**：（1）训练数据的数量和需要解决问题的复杂度有关系。难度越大，精度要求越高，则数据集需求越大，而且一般情况实际中的训练数据越多效果越好。

（2）对于精度要求不高的场景，检测任务和识别任务需要的数据量是不一样的。对于检测任务，500张图像可以保证基本的检测效果。对于识别任务，需要保证识别字典中每个字符出现在不同场景的行文本图像数目需要大于200张（举例，如果有字典中有5个字，每个字都需要出现在200张图片以上，那么最少要求的图像数量应该在200-1000张之间），这样可以保证基本的识别效果。

#### Q：请问PaddleOCR项目中的中文超轻量和通用模型用了哪些数据集？训练多少样本，gpu什么配置，跑了多少个epoch，大概跑了多久？

**A**：
（1）检测的话，LSVT街景数据集共3W张图像，超轻量模型，150epoch左右，2卡V100 跑了不到2天；通用模型：2卡V100 150epoch 不到4天。
（2）识别的话，520W左右的数据集（真实数据26W+合成数据500W）训练，超轻量模型：4卡V100，总共训练了5天左右。通用模型：4卡V100，共训练6天。

超轻量模型训练分为2个阶段：
(1)全量数据训练50epoch，耗时3天
(2)合成数据+真实数据按照1:1数据采样，进行finetune训练200epoch，耗时2天

通用模型训练：
真实数据+合成数据，动态采样(1：1)训练，200epoch，耗时 6天左右。

#### Q：训练文字识别模型，真实数据有30w，合成数据有500w，需要做样本均衡吗？

**A**：需要，一般需要保证一个batch中真实数据样本和合成数据样本的比例是5：1~10：1左右效果比较理想。如果合成数据过大，会过拟合到合成数据，预测效果往往不佳。还有一种启发性的尝试是可以先用大量合成数据训练一个base模型，然后再用真实数据微调，在一些简单场景效果也是会有提升的。

#### Q: 当训练数据量少时，如何获取更多的数据？

**A**：当训练数据量少时，可以尝试以下三种方式获取更多的数据：（1）人工采集更多的训练数据，最直接也是最有效的方式。（2）基于PIL和opencv基本图像处理或者变换。例如PIL中ImageFont, Image, ImageDraw三个模块将文字写到背景中，opencv的旋转仿射变换，高斯滤波等。（3）利用数据生成算法合成数据，例如pix2pix等算法。

### 2.4 数据标注与生成

> [!NOTE]
> StyleText 已经移动到 [PFCCLab/StyleText](https://github.com/PFCCLab/StyleText)

#### Q: Style-Text 如何不文字风格迁移，就像普通文本生成程序一样默认字体直接输出到分割的背景图？

**A**：使用image_synth模式会输出fake_bg.jpg，即为背景图。如果想要批量提取背景，可以稍微修改一下代码，将fake_bg保存下来即可。要修改的位置：
<https://github.com/PaddlePaddle/PaddleOCR/blob/de3e2e7cd3b8b65ee02d7a41e570fa5b511a3c1d/StyleText/engine/synthesisers.py#L68>

#### Q: 能否修改StyleText配置文件中的分辨率？

**A**：StyleText目前的训练数据主要是高度32的图片，建议不要改变高度。未来我们会支持更丰富的分辨率。

#### Q: StyleText是否可以更换字体文件？

**A**：StyleText项目中的字体文件为标准字体，主要用作模型的输入部分，不能够修改。
StyleText的用途主要是：提取style_image中的字体、背景等style信息，根据语料生成同样style的图片。

#### Q: StyleText批量生成图片为什么没有输出？

**A**：需要检查以下您配置文件中的路径是否都存在。尤其要注意的是[label_file配置](https://github.com/PFCCLab/StyleText/blob/main/README_ch.md#%E4%B8%89%E5%BF%AB%E9%80%9F%E4%B8%8A%E6%89%8B)。
如果您使用的style_image输入没有label信息，您依然需要提供一个图片文件列表。

#### Q：使用StyleText进行数据合成时，文本(TextInput)的长度远超StyleInput的长度，该怎么处理与合成呢？

**A**：在使用StyleText进行数据合成的时候，建议StyleInput的长度长于TextInput的长度。有2种方法可以处理上述问题：

1. 将StyleInput按列的方向进行复制与扩充，直到其超过TextInput的长度。
2. 将TextInput进行裁剪，保证每段TextInput都稍短于StyleInput，分别合成之后，再拼接在一起。

实际使用中发现，使用第2种方法的效果在长文本合成的场景中的合成效果更好，StyleText中提供的也是第2种数据合成的逻辑。

#### Q: StyleText 合成数据效果不好？

**A**：StyleText模型生成的数据主要用于OCR识别模型的训练。PaddleOCR目前识别模型的输入为32 x N，因此当前版本模型主要适用高度为32的数据。
建议要合成的数据尺寸设置为32 x N。尺寸相差不多的数据也可以生成，尺寸很大或很小的数据效果确实不佳。

### 2.5 预训练模型与微调

#### Q：如何更换文本检测/识别的backbone？

**A**：无论是文字检测，还是文字识别，骨干网络的选择是预测效果和预测效率的权衡。一般，选择更大规模的骨干网络，例如ResNet101_vd，则检测或识别更准确，但预测耗时相应也会增加。而选择更小规模的骨干网络，例如MobileNetV3_small_x0_35，则预测更快，但检测或识别的准确率会大打折扣。幸运的是不同骨干网络的检测或识别效果与在ImageNet数据集图像1000分类任务效果正相关。飞桨图像分类套件PaddleClas汇总了ResNet_vd、Res2Net、HRNet、MobileNetV3、GhostNet等23种系列的分类网络结构，在上述图像分类任务的top1识别准确率，GPU(V100和T4)和CPU(骁龙855)的预测耗时以及相应的117个预训练模型下载地址。

（1）文字检测骨干网络的替换，主要是确定类似于ResNet的4个stages，以方便集成后续的类似FPN的检测头。此外，对于文字检测问题，使用ImageNet训练的分类预训练模型，可以加速收敛和效果提升。

（2）文字识别的骨干网络的替换，需要注意网络宽高stride的下降位置。由于文本识别一般宽高比例很大，因此高度下降频率少一些，宽度下降频率多一些。可以参考PaddleOCR中MobileNetV3骨干网络的改动。

#### Q: 参照文档做实际项目时，是重新训练还是在官方训练的基础上进行训练？具体如何操作？

**A**： 基于官方提供的模型，进行finetune的话，收敛会更快一些。 具体操作上，以识别模型训练为例：如果修改了字符文件，可以设置pretraind_model为官方提供的预训练模型

#### Q: 下载的识别模型解压后缺失文件，没有期望的inference.pdiparams, inference.pdmodel等文件

**A**：用解压软件解压可能会出现这个问题，建议二次解压下或者用命令行解压tar xf

#### Q: 为什么在checkpoints中load下载的预训练模型会报错？

**A**：这里有两个不同的概念：

pretrained_model：指预训练模型，是已经训练完成的模型。这时会load预训练模型的参数，但并不会load学习率、优化器以及训练状态等。如果需要finetune，应该使用pretrained。
checkpoints：指之前训练的中间结果，例如前一次训练到了100个epoch，想接着训练。这时会load尝试所有信息，包括模型的参数，之前的状态等。

#### Q: 如何对检测模型finetune，比如冻结前面的层或某些层使用小的学习率学习？

**A**：如果是冻结某些层，可以将变量的stop_gradient属性设置为True，这样计算这个变量之前的所有参数都不会更新了，参考：<https://www.paddlepaddle.org.cn/documentation/docs/zh/develop/faq/train_cn.html#id4>

如果对某些层使用更小的学习率学习，静态图里还不是很方便，一个方法是在参数初始化的时候，给权重的属性设置固定的学习率，参考：<https://www.paddlepaddle.org.cn/documentation/docs/zh/develop/api/paddle/fluid/param_attr/ParamAttr_cn.html#paramattr>

实际上我们实验发现，直接加载模型去fine-tune，不设置某些层不同学习率，效果也都不错

### 2.6 模型超参调整

#### Q: DB检测训练输入尺寸640，可以改大一些吗？

**A**：不建议改大。检测模型训练输入尺寸是预处理中random crop后的尺寸，并非直接将原图进行resize，多数场景下这个尺寸并不小了，改大后可能反而并不合适，而且训练会变慢。另外，代码里可能有的地方参数按照预设输入尺寸适配的，改大后可能有隐藏风险。

#### Q: 预处理部分，图片的长和宽为什么要处理成32的倍数？

**A**：以检测中的resnet骨干网络为例，图像输入网络之后，需要经过5次2倍降采样，共32倍，因此建议输入的图像尺寸为32的倍数。

#### Q: 在识别模型中，为什么降采样残差结构的stride为(2, 1)？

**A**: stride为(2, 1)，表示在图像y方向（高度方向）上stride为2，x方向（宽度方向）上为1。由于待识别的文本图像通常为长方形，这样只在高度方向做下采样，尽量保留宽度方向的序列信息，避免宽度方向下采样后丢失过多的文字信息。

#### Q：训练识别时，如何选择合适的网络输入shape？

**A**：一般高度采用32，最长宽度的选择，有两种方法：

（1）统计训练样本图像的宽高比分布。最大宽高比的选取考虑满足80%的训练样本。

（2）统计训练样本文字数目。最长字符数目的选取考虑满足80%的训练样本。然后中文字符长宽比近似认为是1，英文认为3：1，预估一个最长宽度。

#### Q：识别模型框出来的位置太紧凑，会丢失边缘的文字信息，导致识别错误

**A**：可以在命令中加入 --det_db_unclip_ratio ，参数定义位置，这个参数是检测后处理时控制文本框大小的，默认1.6，可以尝试改成2.5或者更大，反之，如果觉得文本框不够紧凑，也可以把该参数调小。

### 2.7 模型结构

#### Q：文本识别训练不加LSTM是否可以收敛？

**A**：理论上是可以收敛的，加上LSTM模块主要是为了挖掘文字之间的序列关系，提升识别效果。对于有明显上下文语义的场景效果会比较明显。

#### Q：文本识别中LSTM和GRU如何选择？

**A**：从项目实践经验来看，序列模块采用LSTM的识别效果优于GRU，但是LSTM的计算量比GRU大一些，可以根据自己实际情况选择。

#### Q：对于CRNN模型，backbone采用DenseNet和ResNet_vd，哪种网络结构更好？

**A**：Backbone的识别效果在CRNN模型上的效果，与Imagenet 1000 图像分类任务上识别效果和效率一致。在图像分类任务上ResnNet_vd（79%+）的识别精度明显优于DenseNet（77%+），此外对于GPU，Nvidia针对ResNet系列模型做了优化，预测效率更高，所以相对而言，resnet_vd是较好选择。如果是移动端，可以优先考虑MobileNetV3系列。

#### Q:  如何根据不同的硬件平台选用不同的backbone？

**A**：在不同的硬件上，不同的backbone的速度优势不同，可以根据不同平台的速度-精度图来确定backbone，这里可以参考[PaddleClas模型速度-精度图](https://github.com/PaddlePaddle/PaddleClas/tree/release/2.0/docs/zh_CN/models)。

### 2.8 PP-OCR系统

#### Q: 在PP-OCR系统中，文本检测的骨干网络为什么没有使用SE模块？

**A**：SE模块是MobileNetV3网络一个重要模块，目的是估计特征图每个特征通道重要性，给特征图每个特征分配权重，提高网络的表达能力。但是，对于文本检测，输入网络的分辨率比较大，一般是640\*640，利用SE模块估计特征图每个特征通道重要性比较困难，网络提升能力有限，但是该模块又比较耗时，因此在PP-OCR系统中，文本检测的骨干网络没有使用SE模块。实验也表明，当去掉SE模块，超轻量模型大小可以减小40%，文本检测效果基本不受影响。详细可以参考PP-OCR技术文章，<https://arxiv.org/abs/2009.09941>.

#### Q: PP-OCR系统中，文本检测的结果有置信度吗？

**A**：文本检测的结果有置信度，由于推理过程中没有使用，所以没有显示的返回到最终结果中。如果需要文本检测结果的置信度，可以在[文本检测DB的后处理代码](../../ppocr/postprocess/db_postprocess.py)的155行，添加scores信息。这样，在[检测预测代码](../../tools/infer/predict_det.py)的197行，就可以拿到文本检测的scores信息。

#### Q: DB文本检测，特征提取网络金字塔构建的部分代码在哪儿？

**A**：特征提取网络金字塔构建的部分:[代码位置](../../ppocr/modeling/necks/db_fpn.py)。ppocr/modeling文件夹里面是组网相关的代码，其中architectures是文本检测或者文本识别整体流程代码；backbones是骨干网络相关代码；necks是类似与FPN的颈函数代码；heads是提取文本检测或者文本识别预测结果相关的头函数；transforms是类似于TPS特征预处理模块。更多的信息可以参考[代码组织结构](./tree.md)。

#### Q：PaddleOCR如何做到横排和竖排同时支持的？

**A**：合成了一批竖排文字，逆时针旋转90度后加入训练集与横排一起训练。预测时根据图片长宽比判断是否为竖排，若为竖排则将crop出的文本逆时针旋转90度后送入识别网络。

#### Q: 目前知识蒸馏有哪些主要的实践思路？

**A**：知识蒸馏即利用教师模型指导学生模型的训练，目前有3种主要的蒸馏思路：

1. 基于输出结果的蒸馏，即让学生模型学习教师模型的软标签（分类或者OCR识别等任务中）或者概率热度图（分割等任务中）。
2. 基于特征图的蒸馏，即让学生模型学习教师模型中间层的特征图，拟合中间层的一些特征。
3. 基于关系的蒸馏，针对不同的样本（假设个数为N），教师模型会有不同的输出，那么可以基于不同样本的输出，计算一个NxN的相关性矩阵，可以让学生模型去学习教师模型关于不同样本的相关性矩阵。

当然，知识蒸馏方法日新月异，也欢迎大家提出更多的总结与建议。

#### Q: 文字识别模型模型的输出矩阵需要进行解码才能得到识别的文本。代码中实现为preds_idx = preds.argmax(axis=2)，也就是最佳路径解码法。这是一种贪心算法，是每一个时间步只将最大概率的字符作为当前时间步的预测输出，但得到的结果不一定是最好的。为什么不使用beam search这种方式进行解码呢？

**A**：实验发现，使用贪心的方法去做解码，识别精度影响不大，但是速度方面的优势比较明显，因此PaddleOCR中使用贪心算法去做识别的解码。

### 2.9 端到端

#### Q: 端到端算法PGNet是否支持中文识别，速度会很慢嘛？

**A**：目前开源的PGNet算法模型主要是用于检测英文数字，对于中文的识别需要自己训练，大家可以使用开源的端到端中文数据集，而对于复杂文本（弯曲文本）的识别，也可以自己构造一批数据集针对进行训练，对于推理速度，可以先将模型转换为inference再进行预测，速度应该会相当可观。

#### Q: 端到端算法PGNet提供了两种后处理方式，两者之间有什么区别呢？

**A**: 两种后处理的区别主要在于速度的推理，config中PostProcess有fast/slow两种模式，slow模式的后处理速度慢，精度相对较高，fast模式的后处理速度快，精度也在可接受的范围之内。建议使用速度快的后处理方式。

#### Q: 使用PGNet进行eval报错？

**A**: 需要注意，我们目前在release/2.1更新了评测代码，目前支持A，B两种评测模式：

- A模式：该模式主要为了方便用户使用，与训练集一样的标注文件就可以正常进行eval操作, 代码中默认是A模式。
- B模式：该模式主要为了保证我们的评测代码可以和Total Text官方的评测方式对齐，该模式下直接加载官方提供的mat文件进行eval。

#### Q: PGNet有中文预训练模型吗？

**A**: 目前我们尚未提供针对中文的预训练模型，如有需要，可以尝试自己训练。具体需要修改的地方有：

  1. [config文件中](https://github.com/PaddlePaddle/PaddleOCR/blob/release/2.1/configs/e2e/e2e_r50_vd_pg.yml#L23-L24)，字典文件路径及语种设置；
  1. [网络结构中](https://github.com/PaddlePaddle/PaddleOCR/blob/release/2.1/ppocr/modeling/heads/e2e_pg_head.py#L181)，`out_channels`修改为字典中的字符数目+1（考虑到空格）；
  1. [loss中](https://github.com/PaddlePaddle/PaddleOCR/blob/release/2.1/ppocr/losses/e2e_pg_loss.py#L93)，修改`37`为字典中的字符数目+1（考虑到空格）；

#### Q: 用于PGNet的训练集，文本框的标注有要求吗？

**A**: PGNet支持多点标注，比如4点、8点、14点等。但需要注意的是，标注点尽可能分布均匀（相邻标注点间隔距离均匀一致），且label文件中的标注点需要从标注框的左上角开始，按标注点顺时针顺序依次编写，以上问题都可能对训练精度造成影响。
我们提供的，基于Total Text数据集的PGNet预训练模型使用了14点标注方式。

#### Q: 用PGNet做进行端到端训练时，数据集标注的点的个数必须都是统一一样的吗? 能不能随意标点数，只要能够按顺时针从左上角开始标这样?

**A**: 目前代码要求标注为统一的点数。

### 2.10 模型效果与效果不一致

#### Q: PP-OCR检测效果不好，该如何优化？

**A**： 具体问题具体分析:
如果在你的场景上检测效果不可用，首选是在你的数据上做finetune训练；
如果图像过大，文字过于密集，建议不要过度压缩图像，可以尝试修改检测预处理的resize逻辑，防止图像被过度压缩；
检测框大小过于紧贴文字或检测框过大，可以调整db_unclip_ratio这个参数，加大参数可以扩大检测框，减小参数可以减小检测框大小；
检测框存在很多漏检问题，可以减小DB检测后处理的阈值参数det_db_box_thresh，防止一些检测框被过滤掉，也可以尝试设置det_db_score_mode为'slow';
其他方法可以选择use_dilation为True，对检测输出的feature map做膨胀处理，一般情况下，会有效果改善；

#### Q：同一张图通用检测出21个条目，轻量级检测出26个 ，难道不是轻量级的好吗？

**A**：可以主要参考可视化效果，通用模型更倾向于检测一整行文字，轻量级可能会有一行文字被分成两段检测的情况，不是数量越多，效果就越好。

#### Q: DB有些框太贴文本了反而去掉了一些文本的边角影响识别，这个问题有什么办法可以缓解吗？

**A**：可以把后处理的参数unclip_ratio适当调大一点。

#### Q: 使用合成数据精调小模型后，效果可以，但是还没开源的小infer模型效果好，这是为什么呢？

**A**：（1）要保证使用的配置文件和pretrain weights是对应的；

（2）在微调时，一般都需要真实数据，如果使用合成数据，效果反而可能会有下降，PaddleOCR中放出的识别inference模型也是基于预训练模型在真实数据上微调得到的，效果提升比较明显；

（3）在训练的时候，文本长度超过25的训练图像都会被丢弃，因此需要看下真正参与训练的图像有多少，太少的话也容易过拟合。

#### Q: 表格识别中，如何提高单字的识别结果？

**A**: 首先需要确认一下检测模型有没有有效的检测出单个字符，如果没有的话，需要在训练集当中添加相应的单字数据集。

#### Q: 动态图分支(dygraph,release/2.0)，训练模型和推理模型效果不一致

**A**：当前问题表现为：使用训练完的模型直接测试结果较好，但是转换为inference model后，预测结果不一致；出现这个问题一般是两个原因：

1. 预处理函数设置的不一致
2. 后处理参数不一致 repo中config.yml文件的前后处理参数和inference预测默认的超参数有不一致的地方，建议排查下训练模型预测和inference预测的前后处理， 参考issue。

#### Q: 自己训练的det模型，在同一张图片上，inference模型与eval模型结果差别很大，为什么？

**A**：这是由于图片预处理不同造成的。如果训练的det模型图片输入并不是默认的shape[600, 600]，eval的程序中图片预处理方式与train时一致 （由xxx_reader.yml中的test_image_shape参数决定缩放大小，但predict_eval.py中的图片预处理方式由程序里的preprocess_params决定， 最好不要传入max_side_len，而是传入和训练时一样大小的test_image_shape。

#### Q: 训练模型和测试模型的检测结果差距较大

**A**：1. 检查两个模型使用的后处理参数是否是一样的，训练的后处理参数在配置文件中的PostProcess部分，测试模型的后处理参数在tools/infer/utility.py中，最新代码中两个后处理参数已保持一致。

#### Q: PaddleOCR模型Python端预测和C++预测结果不一致？

**A**：正常来说，python端预测和C++预测文本是一致的，如果预测结果差异较大， 建议首先排查diff出现在检测模型还是识别模型，或者尝试换其他模型是否有类似的问题。 其次，检查python端和C++端数据处理部分是否存在差异，建议保存环境，更新PaddleOCR代码再试下。 如果更新代码或者更新代码都没能解决，建议在PaddleOCR微信群里或者issue中抛出您的问题。

用户总结的排查步骤：<https://github.com/PaddlePaddle/PaddleOCR/issues/2470>

### 2.11 训练调试与配置文件

#### Q: 某个类别的样本比较少，通过增加训练的迭代次数或者是epoch，变相增加小样本的数目，这样能缓解这个问题么？

**A**： 尽量保证类别均衡， 某些类别样本少，可以通过补充合成数据的方式处理；实验证明训练集中出现频次较少的字符，识别效果会比较差，增加迭代次数不能改变样本量少的问题。

#### Q：文本检测换成自己的数据没法训练，有一些”###”是什么意思？

**A**：数据格式有问题，”###” 表示要被忽略的文本区域，所以你的数据都被跳过了，可以换成其他任意字符或者就写个空的。

#### Q：如何调试数据读取程序？

**A**：tools/train.py中有一个test_reader()函数用于调试数据读取。

#### Q：中文文本检测、文本识别构建训练集的话，大概需要多少数据量

**A**：检测需要的数据相对较少，在PaddleOCR模型的基础上进行Fine-tune，一般需要500张可达到不错的效果。 识别分英文和中文，一般英文场景需要几十万数据可达到不错的效果，中文则需要几百万甚至更多。

#### Q: config yml文件中的ratio_list参数的作用是什么？

**A**: 在动态图中，ratio_list在有多个数据源的情况下使用，ratio_list中的每个值是每个epoch从对应数据源采样数据的比例。如ratio_list=[0.3,0.2]，label_file_list=['data1','data2'],代表每个epoch的训练数据包含data1 30%的数据，和data2里 20%的数据，ratio_list中数值的和不需要等于1。ratio_list和label_file_list的长度必须一致。

静态图检测数据采样的逻辑与动态图不同，但基本不影响训练精度。

在静态图中，使用 检测 dataloader读取数据时，会先设置每个epoch的数据量，比如这里设置为1000，ratio_list中的值表示在1000中的占比，比如ratio_list是[0.3, 0.7]，则表示使用两个数据源，每个epoch从第一个数据源采样1000*0.3=300张图，从第二个数据源采样700张图。ratio_list的值的和也不需要等于1。

#### Q: iaa里面添加的数据增强方式，是每张图像训练都会做增强还是随机的？如何添加一个数据增强方法？

**A**：iaa增强的训练配置参考：[这里](https://github.com/PaddlePaddle/PaddleOCR/blob/0ccc1720c252beb277b9e522a1b228eb6abffb8a/configs/det/ch_ppocr_v2.0/ch_det_mv3_db_v2.0.yml#L82)。其中{ 'type': Fliplr, 'args': { 'p': 0.5 } } p是概率。新增数据增强，可以参考[这个方法](https://github.com/PaddlePaddle/PaddleOCR/blob/release%2F2.1/doc/doc_ch/add_new_algorithm.md#%E6%95%B0%E6%8D%AE%E5%8A%A0%E8%BD%BD%E5%92%8C%E5%A4%84%E7%90%86)

#### Q: 怎么加速训练过程呢？

**A**：OCR模型训练过程中一般包含大量的数据增广，这些数据增广是比较耗时的，因此可以离线生成大量增广后的图像，直接送入网络进行训练，机器资源充足的情况下，也可以使用分布式训练的方法，可以参考[分布式训练教程文档](https://github.com/PaddlePaddle/PaddleOCR/blob/dygraph/doc/doc_ch/distributed_training.md)。

#### Q: 一些特殊场景的数据识别效果差，但是数据量很少，不够用来finetune怎么办？

**A**：您可以合成一些接近使用场景的数据用于训练。
我们计划推出基于特定场景的文本数据合成工具，请您持续关注PaddleOCR的近期更新。

#### Q: PaddleOCR可以识别灰度图吗？

**A**：PaddleOCR的模型均为三通道输入。如果您想使用灰度图作为输入，建议直接用3通道的模式读入灰度图，
或者将单通道图像转换为三通道图像再识别。例如，opencv的cvtColor函数就可以将灰度图转换为RGB三通道模式。

#### Q: 如何合成手写中文数据集？

**A**: 手写数据集可以通过手写单字数据集合成得到。随机选取一定数量的单字图片和对应的label，将图片高度resize为随机的统一高度后拼接在一起，即可得到合成数据集。对于需要添加文字背景的情况，建议使用阈值化将单字图片的白色背景处理为透明背景，再与真实背景图进行合成。具体可以参考文档[手写数据集](https://github.com/PaddlePaddle/PaddleOCR/blob/a72d6f23be9979e0c103d911a9dca3e4613e8ccf/doc/doc_ch/handwritten_datasets.md)。

#### Q：PaddleOCR默认不是200个step保存一次模型吗？为啥文件夹下面都没有生成

**A**：因为默认保存的起始点不是0，而是4000，将eval_batch_step [4000, 5000]改为[0, 2000] 就是从第0次迭代开始，每2000迭代保存一次模型

#### Q: PaddleOCR在训练的时候一直使用cosine_decay的学习率下降策略，这是为什么呢？

**A**：cosine_decay表示在训练的过程中，学习率按照cosine的变化趋势逐渐下降至0，在迭代轮数更长的情况下，比常量的学习率变化策略会有更好的收敛效果，因此在实际训练的时候，均采用了cosine_decay，来获得精度更高的模型。

#### Q: Cosine学习率的更新策略是怎样的？训练过程中为什么会在一个值上停很久？

**A**: Cosine学习率的说明可以参考[这里](https://www.paddlepaddle.org.cn/documentation/docs/zh/api/paddle/optimizer/lr/CosineAnnealingDecay_cn.html#cosineannealingdecay)

在PaddleOCR中，为了让学习率更加平缓，我们将其中的epoch调整成了iter。
学习率的更新会和总的iter数量有关。当iter比较大时，会经过较多iter才能看出学习率的值有变化。

#### Q: 之前的CosineWarmup方法为什么不见了？

**A**: 我们对代码结构进行了调整，目前的Cosine可以覆盖原有的CosineWarmup的功能，只需要在配置文件中增加相应配置即可。
例如下面的代码，可以设置warmup为2个epoch：

```
lr:
  name: Cosine
  learning_rate: 0.001
  warmup_epoch: 2
```

#### Q: 训练识别和检测时学习率要加上warmup，目的是什么？

**A**: Warmup机制先使学习率从一个较小的值逐步升到一个较大的值，而不是直接就使用较大的学习率，这样有助于模型的稳定收敛。在OCR检测和OCR识别中，一般会带来精度~0.5%的提升。

#### Q: 关于dygraph分支中，文本识别模型训练，要使用数据增强应该如何设置？

**A**：可以参考[配置文件](../configs/rec/ch_ppocr_v2.0/rec_chinese_lite_train_v2.0.yml)在`Train['dataset']['transforms']`添加RecAug字段，使数据增强生效。可以通过添加对aug_prob设置，表示每种数据增强采用的概率。aug_prob默认是0.4。详细设置可以参考[ISSUE 1744](https://github.com/PaddlePaddle/PaddleOCR/issues/1744)。

#### Q: 训练过程中，训练程序意外退出/挂起，应该如何解决？

**A**: 考虑内存，显存（使用GPU训练的话）是否不足，可在配置文件中，将训练和评估的batch size调小一些。需要注意，训练batch size调小时，学习率learning rate也要调小，一般可按等比例调整。

#### Q: 训练程序启动后直到结束，看不到训练过程log？

**A**: 可以从以下三方面考虑：
    1. 检查训练进程是否正常退出、显存占用是否释放、是否有残留进程，如果确定是训练程序卡死，可以检查环境配置，遇到环境问题建议使用docker，可以参考说明文档[安装](https://github.com/PaddlePaddle/PaddleOCR/blob/release/2.1/doc/doc_ch/installation.md)。
    2. 检查数据集的数据量是否太小，可调小batch size从而增加一个epoch中的训练step数量，或在训练config文件中，将参数print_batch_step改为1，即每一个step打印一次log信息。
    3. 如果使用私有数据集训练，可先用PaddleOCR提供/推荐的数据集进行训练，排查私有数据集是否存在问题。

#### Q: 配置文件中的参数num workers是什么意思，应该如何设置？

**A**: 训练数据的读取需要硬盘IO，而硬盘IO速度远小于GPU运算速度，为了避免数据读取成为训练速度瓶颈，可以使用多进程读取数据，num workers表示数据读取的进程数量，0表示不使用多进程读取。在Linux系统下，多进程读取数据时，进程间通信需要基于共享内存，因此使用多进程读取数据时，建议设置共享内存不低于2GB，最好可以达到8GB，此时，num workers可以设置为CPU核心数。如果机器硬件配置较低，或训练进程卡死、dataloader报错，可以将num workers设置为0，即不使用多进程读取数据。

### 2.12 预测

#### Q: 为什么PaddleOCR检测预测是只支持一张图片测试？即test_batch_size_per_card=1

**A**：测试的时候，对图像等比例缩放，最长边960，不同图像等比例缩放后长宽不一致，无法组成batch，所以设置为test_batch_size为1。

#### Q: PaddleOCR支持tensorrt推理吗？

**A**：支持的，需要在编译的时候将CMakeLists.txt文件当中，将相关代码option(WITH_TENSORRT "Compile demo with TensorRT." OFF)的OFF改成ON。关于服务器端部署的更多设置，可以参考飞桨官网

#### Q: 如何使用TensorRT加速PaddleOCR预测？

**A**： 目前paddle的dygraph分支已经支持了python和C++ TensorRT预测的代码，python端inference预测时把参数[--use_tensorrt=True](https://github.com/PaddlePaddle/PaddleOCR/blob/3ec57e8df9263de6fa897e33d2d91bc5d0849ef3/tools/infer/utility.py#L37)即可，
C++TensorRT预测需要使用支持TRT的预测库并在编译时打开[-DWITH_TENSORRT=ON](https://github.com/PaddlePaddle/PaddleOCR/blob/3ec57e8df9263de6fa897e33d2d91bc5d0849ef3/deploy/cpp_infer/tools/build.sh#L15)。
如果想修改其他分支代码支持TensorRT预测，可以参考[PR](https://github.com/PaddlePaddle/PaddleOCR/pull/2921)。

注：建议使用TensorRT大于等于6.1.0.5以上的版本。

#### Q: 为什么识别模型做预测的时候，预测图片的数量数量还会影响预测的精度

**A**： 推理时识别模型默认的batch_size=6, 如预测图片长度变化大，可能影响预测效果。如果出现上述问题可在推理的时候设置识别bs=1，命令如下：

```bash linenums="1"
python3 tools/infer/predict_rec.py --image_dir="./doc/imgs_words/ch/word_4.jpg" --rec_model_dir="./ch_PP-OCRv3_rec_infer/" --rec_batch_num=1
```

### 2.13 推理部署

#### Q：PaddleOCR模型推理方式有几种？各自的优缺点是什么

**A**：目前推理方式支持基于训练引擎推理和基于预测引擎推理。

（1）基于训练引擎推理不需要转换模型，但是需要先组网再load参数，语言只支持python，不适合系统集成。

（2）基于预测引擎的推理需要先转换模型为inference格式，然后可以进行不需要组网的推理，语言支持c++和python，适合系统集成。

#### Q：PaddleOCR中，对于模型预测加速，CPU加速的途径有哪些？基于TenorRT加速GPU对输入有什么要求？

**A**：（1）CPU可以使用mkldnn进行加速；对于python inference的话，可以把enable_mkldnn改为true，[参考代码](https://github.com/PaddlePaddle/PaddleOCR/blob/dygraph/tools/infer/utility.py#L99)，对于cpp inference的话，可参考[文档](https://github.com/PaddlePaddle/PaddleOCR/tree/dygraph/deploy/cpp_infer)

（2）GPU需要注意变长输入问题等，TRT6 之后才支持变长输入

#### Q：hubserving、pdserving这两种部署方式区别是什么？

**A**：hubserving原本是paddlehub的配套服务部署工具，可以很方便的将paddlehub内置的模型部署为服务，paddleocr使用了这个功能，并将模型路径等参数暴露出来方便用户自定义修改。paddle serving是面向所有paddle模型的部署工具，文档中可以看到我们提供了快速版和标准版，其中快速版和hubserving的本质是一样的，而标准版基于rpc，更稳定，更适合分布式部署。

#### Q: 目前paddle hub serving 只支持 imgpath，如果我想用imgurl 去哪里改呢？

**A**：图片是在[这里](https://github.com/PaddlePaddle/PaddleOCR/blob/67ef25d593c4eabfaaceb22daade4577f53bed81/deploy/hubserving/ocr_system/module.py#L55)读取的， 可以参考下面的写法，将url path转化为np array

```
response = request.urlopen('http://i1.whymtj.com/uploads/tu/201902/9999/52491ae4ba.jpg')
img_array = np.array(bytearray(response.read()), dtype=np.uint8)
img = cv.imdecode(img_array, -1)
```

#### Q: C++ 端侧部署可以只对OCR的检测部署吗？

**A**：可以的，识别和检测模块是解耦的。如果想对检测部署，需要自己修改一下main函数， 只保留检测相关就可以: [参考](https://github.com/PaddlePaddle/PaddleOCR/blob/de3e2e7cd3b8b65ee02d7a41e570fa5b511a3c1d/deploy/cpp_infer/src/main.cpp#L72)

#### Q：服务部署可以只发布文本识别，而不带文本检测模型么？

**A**：可以的。默认的服务部署是检测和识别串联预测的。也支持单独发布文本检测或文本识别模型，比如使用PaddleHUBPaddleOCR 模型时，deploy下有三个文件夹，分别是
ocr_det：检测预测
ocr_rec: 识别预测
ocr_system: 检测识别串联预测

#### Q: lite预测库和nb模型版本不匹配，该如何解决？

**A**： 如果可以正常预测就不用管，如果这个问题导致无法正常预测，可以尝试使用同一个commit的Paddle Lite代码编译预测库和opt文件，可以参考[移动端部署教程](https://github.com/PaddlePaddle/PaddleOCR/blob/release%2F2.1/deploy/lite/readme.md)。

#### Q：如何将PaddleOCR预测模型封装成SDK

**A**：如果是Python的话，可以使用tools/infer/predict_system.py中的TextSystem进行sdk封装，如果是c++的话，可以使用deploy/cpp_infer/src下面的DBDetector和CRNNRecognizer完成封装

#### Q：为什么PaddleOCR检测预测是只支持一张图片测试？即test_batch_size_per_card=1

**A**：测试的时候，对图像等比例缩放，最长边960，不同图像等比例缩放后长宽不一致，无法组成batch，所以设置为test_batch_size为1。

#### Q：为什么第一张张图预测时间很长，第二张之后预测时间会降低？

**A**：第一张图需要显存资源初始化，耗时较多。完成模型加载后，之后的预测时间会明显缩短。

#### Q: 采用Paddle-Lite进行端侧部署，出现问题，环境没问题

**A**：如果你的预测库是自己编译的，那么你的nb文件也要自己编译，用同一个lite版本。不能直接用下载的nb文件，因为版本不同。

#### Q: 如何多进程运行paddleocr？

**A**：实例化多个paddleocr服务，然后将服务注册到注册中心，之后通过注册中心统一调度即可，关于注册中心，可以搜索eureka了解一下具体使用，其他的注册中心也行。

#### Q: 如何多进程预测？

**A**: 近期PaddleOCR新增了[多进程预测控制参数](https://github.com/PaddlePaddle/PaddleOCR/blob/a312647be716776c1aac33ff939ae358a39e8188/tools/infer/utility.py#L103)，`use_mp`表示是否使用多进程，`total_process_num`表示在使用多进程时的进程数。具体使用方式请参考[文档](https://github.com/PaddlePaddle/PaddleOCR/blob/release/2.1/doc/doc_ch/inference.md#1-%E8%B6%85%E8%BD%BB%E9%87%8F%E4%B8%AD%E6%96%87ocr%E6%A8%A1%E5%9E%8B%E6%8E%A8%E7%90%86)。

#### Q: 怎么解决paddleOCR在T4卡上有越预测越慢的情况？

**A**：

1. T4 GPU没有主动散热，因此在测试的时候需要在每次infer之后需要sleep 30ms，否则机器容易因为过热而降频(inference速度会变慢)，温度过高也有可能会导致宕机。
2. T4在不使用的时候，也有可能会降频，因此在做benchmark的时候需要锁频，下面这两条命令可以进行锁频。

```
nvidia-smi -i 0 -pm ENABLED
nvidia-smi --lock-gpu-clocks=1590 -i 0
```

#### Q: 在windows上进行cpp inference的部署时，总是提示找不到`paddle_fluid.dll`和`opencv_world346.dll`

**A**：有2种方法可以解决这个问题：

1. 将paddle预测库和opencv库的地址添加到系统环境变量中。
2. 将提示缺失的dll文件拷贝到编译产出的`ocr_system.exe`文件夹中。

#### Q: win下C++部署中文识别乱码的解决方法

**A**: win下编码格式不是utf8,而ppocr_keys_v1.txt的编码格式的utf8，将ppocr_keys_v1.txt 的编码从utf-8修改为 Ansi 编码格式就行了。

#### Q: windows 3060显卡GPU模式启动 加载模型慢

**A**: 30系列的显卡需要使用cuda11。

#### Q：想在Mac上部署，从哪里下载预测库呢？

**A**：Mac上的Paddle预测库可以从这里下载：[https://paddle-inference-lib.bj.bcebos.com/mac/2.0.0/cpu_avx_openblas/paddle_inference.tgz](https://paddle-inference-lib.bj.bcebos.com/mac/2.0.0/cpu_avx_openblas/paddle_inference.tgz)

#### Q：内网环境如何进行服务化部署呢？

**A**：仍然可以使用PaddleServing或者HubServing进行服务化部署，保证内网地址可以访问即可。

#### Q: 使用hub_serving部署，延时较高，可能的原因是什么呀？

**A**: 首先，测试的时候第一张图延时较高，可以多测试几张然后观察后几张图的速度；其次，如果是在cpu端部署serving端模型（如backbone为ResNet34），耗时较慢，建议在cpu端部署mobile（如backbone为MobileNetV3）模型。

#### Q: 在使用PaddleLite进行预测部署时，启动预测后卡死/手机死机？

**A**: 请检查模型转换时所用PaddleLite的版本，和预测库的版本是否对齐。即PaddleLite版本为2.8，则预测库版本也要为2.8。

#### Q: 预测时显存爆炸、内存泄漏问题？

**A**: 打开显存/内存优化开关`enable_memory_optim`可以解决该问题，相关代码已合入，[查看详情](https://github.com/PaddlePaddle/PaddleOCR/blob/release/2.1/tools/infer/utility.py#L153)。
