# Hot Reload Deployment Script for Server
# Encoding: UTF-8

param(
    [string]$Mode = "start",  # start, stop, restart, status, logs
    [string]$Service = "all", # all, frontend, backend
    [switch]$Build,
    [switch]$Pull
)

Write-Host "=== Hot Reload Deployment ===" -ForegroundColor Green
Write-Host "Mode: $Mode" -ForegroundColor Cyan
Write-Host "Service: $Service" -ForegroundColor Cyan
Write-Host ""

# Check Docker and docker-compose
$dockerAvailable = Get-Command docker -ErrorAction SilentlyContinue
$composeAvailable = Get-Command docker-compose -ErrorAction SilentlyContinue

if (-not $dockerAvailable) {
    Write-Host "Error: Docker not found" -ForegroundColor Red
    exit 1
}

if (-not $composeAvailable) {
    Write-Host "Error: docker-compose not found" -ForegroundColor Red
    exit 1
}

# Set compose file
$composeFile = "docker-compose.hotreload.yml"

if (-not (Test-Path $composeFile)) {
    Write-Host "Error: $composeFile not found" -ForegroundColor Red
    Write-Host "Please ensure you're in the docker directory" -ForegroundColor Yellow
    exit 1
}

# Create necessary directories
# Make paths relative to the script's invocation directory (expected to be the 'docker' root)
$baseDirForData = (Get-Location).Path
$directories = @(
    Join-Path $baseDirForData "data/db"
    Join-Path $baseDirForData "data/models"
    Join-Path $baseDirForData "data/media"
    Join-Path $baseDirForData "data/logs"
    Join-Path $baseDirForData "code-sync"
)
foreach ($dir in $directories) {
    if (-not (Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
        Write-Host "Created directory: $dir" -ForegroundColor Gray
    }
}

# Function: Show service status
function Show-Status {
    Write-Host "Service Status:" -ForegroundColor Cyan
    docker-compose -f $composeFile ps

    Write-Host ""
    Write-Host "Container Logs (last 10 lines):" -ForegroundColor Cyan

    if ($Service -eq "all" -or $Service -eq "backend") {
        Write-Host "--- Backend ---" -ForegroundColor Yellow
        docker-compose -f $composeFile logs --tail=10 backend
    }

    if ($Service -eq "all" -or $Service -eq "frontend") {
        Write-Host "--- Frontend ---" -ForegroundColor Yellow
        docker-compose -f $composeFile logs --tail=10 frontend
    }
}

# Function: Show access information
function Show-AccessInfo {
    # Auto-detect local IP address with improved logic
    $localIP = $null

    # Method 1: Try to get the IP from default gateway route (most reliable)
    try {
        $defaultRoute = Get-NetRoute -DestinationPrefix "0.0.0.0/0" -ErrorAction SilentlyContinue |
                       Where-Object { $_.RouteMetric -eq (Get-NetRoute -DestinationPrefix "0.0.0.0/0" | Measure-Object RouteMetric -Minimum).Minimum } |
                       Select-Object -First 1

        if ($defaultRoute) {
            $adapter = Get-NetAdapter -InterfaceIndex $defaultRoute.InterfaceIndex -ErrorAction SilentlyContinue
            if ($adapter -and $adapter.Status -eq "Up") {
                $localIP = (Get-NetIPAddress -InterfaceIndex $defaultRoute.InterfaceIndex -AddressFamily IPv4 -ErrorAction SilentlyContinue |
                           Where-Object { $_.PrefixOrigin -eq "Dhcp" -or $_.PrefixOrigin -eq "Manual" }).IPAddress
            }
        }
    }
    catch {
        # Ignore errors and try next method
    }

    # Method 2: Fallback to prioritize 192.168.* range (common home networks)
    if (-not $localIP) {
        $localIP = (Get-NetIPAddress -AddressFamily IPv4 -ErrorAction SilentlyContinue | Where-Object {
            $_.IPAddress -like "192.168.*" -and
            $_.IPAddress -ne "127.0.0.1" -and
            $_.PrefixOrigin -ne "WellKnown"
        }).IPAddress | Select-Object -First 1
    }

    # Method 3: Try other private IP ranges (excluding Docker/VM ranges)
    if (-not $localIP) {
        $localIP = (Get-NetIPAddress -AddressFamily IPv4 -ErrorAction SilentlyContinue | Where-Object {
            ($_.IPAddress -like "10.*" -and $_.IPAddress -notlike "10.0.2.*" -and $_.IPAddress -notlike "10.3.*") -or
            ($_.IPAddress -like "172.*" -and $_.IPAddress -notlike "172.1*" -and $_.IPAddress -notlike "172.2*") -and
            $_.IPAddress -ne "127.0.0.1" -and
            $_.PrefixOrigin -ne "WellKnown"
        }).IPAddress | Select-Object -First 1
    }

    # Method 4: Last resort - use any non-loopback IP
    if (-not $localIP) {
        $localIP = (Get-NetIPAddress -AddressFamily IPv4 -ErrorAction SilentlyContinue | Where-Object {
            $_.IPAddress -ne "127.0.0.1" -and $_.PrefixOrigin -ne "WellKnown"
        }).IPAddress | Select-Object -First 1
    }

    Write-Host ""
    Write-Host "=== Access Information ===" -ForegroundColor Green
    Write-Host "Frontend (Local): http://localhost:8080" -ForegroundColor White
    if ($localIP) {
        Write-Host "Frontend (LAN):   http://${localIP}:8080" -ForegroundColor White
    } else {
        Write-Host "Frontend (LAN):   [IP auto-detection failed]" -ForegroundColor Yellow
    }
    Write-Host "Backend (Local):  http://localhost:8000" -ForegroundColor White
    if ($localIP) {
        Write-Host "Backend (LAN):    http://${localIP}:8000" -ForegroundColor White
    } else {
        Write-Host "Backend (LAN):    [IP auto-detection failed]" -ForegroundColor Yellow
    }
    Write-Host ""
    Write-Host "Hot Reload Status:" -ForegroundColor Cyan
    Write-Host "- Code sync directory: ./code-sync/" -ForegroundColor Gray # This path is relative to docker-compose context
    Write-Host "- Frontend files will be served directly from sync directory" -ForegroundColor Gray
    Write-Host "- Backend changes will trigger Django auto-reload" -ForegroundColor Gray
}

# Main logic
switch ($Mode.ToLower()) {
    "start" {
        Write-Host "Starting services in hot reload mode..." -ForegroundColor Yellow

        # 检查前端构建产物
        # FIX: Use (Get-Location).Path to ensure $currentDir is a string path
        $currentDir = (Get-Location).Path
        $frontendDistPath = Join-Path $currentDir "code-sync\Frontend\dist"
        $frontendIndexPath = Join-Path $frontendDistPath "index.html"

        Write-Host "Current directory: $currentDir" -ForegroundColor Gray
        Write-Host "Checking frontend build at: $frontendDistPath" -ForegroundColor Gray

        if (-not (Test-Path $frontendDistPath) -or -not (Test-Path $frontendIndexPath)) {
            Write-Host "Frontend build not found, building now..." -ForegroundColor Yellow

            # 检查构建脚本是否存在
            # The build script path is relative to the docker directory, where this deploy script is invoked from.
            # $PSScriptRoot would be ...docker/scripts/, so we need to go up one level or use $currentDir
            $buildScript = Join-Path $currentDir "scripts\build-frontend-hotreload.ps1"
            if (-not (Test-Path $buildScript)) { # If current dir is 'docker', scripts is a subdir
                 $buildScript = Join-Path $currentDir "..\scripts\build-frontend-hotreload.ps1" # if invoked from script dir for some reason
                 # More robustly, if the build script is ALWAYS in 'project_root/scripts/':
                 # $projectRoot = (Split-Path $currentDir) # Assuming $currentDir is 'docker', projectRoot is one level up
                 # $buildScript = Join-Path $projectRoot "scripts\build-frontend-hotreload.ps1"
                 # For this case, assuming the build script is at 'docker/scripts/build-frontend-hotreload.ps1'
                 # and this deploy script is also in 'docker/scripts/deploy-hotreload.ps1'
                 # and CWD is 'docker'
                 $buildScript = ".\scripts\build-frontend-hotreload.ps1" # Relative to CWD ('docker')
            }


            if (Test-Path $buildScript) {
                Write-Host "Found build script at $buildScript, executing..." -ForegroundColor Cyan
                & $buildScript
                if ($LASTEXITCODE -ne 0) {
                    Write-Host "ERROR Frontend build failed" -ForegroundColor Red
                    exit 1
                }
            } else {
                Write-Host "WARNING Build script '$buildScript' not found, please build frontend manually" -ForegroundColor Yellow
                Write-Host "Example: cd ../Frontend && npm run build && robocopy dist ../docker/code-sync/Frontend/dist /E" -ForegroundColor Gray
            }
        } else {
            Write-Host "Frontend build found, proceeding..." -ForegroundColor Green
        }

        if ($Pull) {
            Write-Host "Pulling latest images..." -ForegroundColor Gray
            docker-compose -f $composeFile pull
        }

        if ($Build) {
            Write-Host "Building images..." -ForegroundColor Gray
            docker-compose -f $composeFile build
        }

        if ($Service -eq "all") {
            docker-compose -f $composeFile up -d
        } else {
            docker-compose -f $composeFile up -d $Service
        }

        if ($LASTEXITCODE -eq 0) {
            Write-Host "OK Services started successfully" -ForegroundColor Green
            Show-AccessInfo
        } else {
            Write-Host "ERROR Failed to start services" -ForegroundColor Red
        }
    }

    "stop" {
        Write-Host "Stopping services..." -ForegroundColor Yellow

        if ($Service -eq "all") {
            docker-compose -f $composeFile down
        } else {
            docker-compose -f $composeFile stop $Service
        }

        Write-Host "OK Services stopped" -ForegroundColor Green
    }

    "restart" {
        Write-Host "Restarting services (down + up to reload .env)..." -ForegroundColor Yellow

        # 停止并移除容器以重新读取.env文件
        if ($Service -eq "all") {
            docker-compose -f $composeFile down
            docker-compose -f $composeFile up -d
        } else {
            docker-compose -f $composeFile stop $Service
            docker-compose -f $composeFile up -d $Service
        }

        if ($LASTEXITCODE -eq 0) {
            Write-Host "OK Services restarted successfully" -ForegroundColor Green
            Show-AccessInfo
        } else {
            Write-Host "ERROR Failed to restart services" -ForegroundColor Red
        }
    }

    "status" {
        Show-Status
        Show-AccessInfo
    }

    "logs" {
        Write-Host "Showing logs (press Ctrl+C to exit)..." -ForegroundColor Yellow

        if ($Service -eq "all") {
            docker-compose -f $composeFile logs -f
        } else {
            docker-compose -f $composeFile logs -f $Service
        }
    }

    default {
        Write-Host "Invalid mode: $Mode" -ForegroundColor Red
        Write-Host ""
        Write-Host "Usage:" -ForegroundColor Yellow
        Write-Host "  .\scripts\deploy-hotreload.ps1 -Mode start [-Service all|frontend|backend] [-Build] [-Pull]" -ForegroundColor Gray
        Write-Host "  .\scripts\deploy-hotreload.ps1 -Mode stop [-Service all|frontend|backend]" -ForegroundColor Gray
        Write-Host "  .\scripts\deploy-hotreload.ps1 -Mode restart [-Service all|frontend|backend]" -ForegroundColor Gray
        Write-Host "  .\scripts\deploy-hotreload.ps1 -Mode status" -ForegroundColor Gray
        Write-Host "  .\scripts\deploy-hotreload.ps1 -Mode logs [-Service all|frontend|backend]" -ForegroundColor Gray
        exit 1
    }
}