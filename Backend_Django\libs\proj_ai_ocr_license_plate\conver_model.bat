﻿REM 新版的paddlepaddle导出静态图模型，是不会再导出.pdmodel文件了，而是导出.pdiparams和.yml文件


@echo off
chcp 65001 > nul
REM 切换到项目根目录
cd /d "%~dp0"

REM 定义配置文件和模型权重路径
set CONFIG_FILE=models/Identity_card_rec_model/config.yml
set MODEL_CHECKPOINT=models/Identity_card_rec_model/best_model/best_accuracy
set OUTPUT_DIR=./output/inference

REM 运行模型导出脚本
python tools/export_model.py --config %CONFIG_FILE% --opt Global.checkpoints=%MODEL_CHECKPOINT% Global.save_inference_dir=%OUTPUT_DIR%

echo 模型转换完成！导出的模型在 %OUTPUT_DIR% 目录下。 