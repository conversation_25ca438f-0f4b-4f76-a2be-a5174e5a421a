metrics:
- mindeo/metrics/det.json
- mindeo/metrics/rec.json
stages:
  create_data_set_det:
    cmd: python3 create_data_set_det.py
    deps:
    - create_data_set_det.py
    - mindeo/data/common/CCPD2019/ccpd_base/Label.txt
    - mindeo/data/common/CCPD2019/ccpd_blur/Label.txt
    - mindeo/data/common/CCPD2019/ccpd_challenge/Label.txt
    - mindeo/data/common/CCPD2019/ccpd_db/Label.txt
    - mindeo/data/common/CCPD2019/ccpd_fn/Label.txt
    - mindeo/data/common/CCPD2019/ccpd_rotate/Label.txt
    - mindeo/data/common/CCPD2019/ccpd_tilt/Label.txt
    - mindeo/data/common/CCPD2019/ccpd_weather/Label.txt
    - mindeo/data/common/CCPD2020/ccpd_green/test/Label.txt
    - mindeo/data/common/CCPD2020/ccpd_green/train/Label.txt
    - mindeo/data/common/CCPD2020/ccpd_green/val/Label.txt
    outs:
    - mindeo/data_processed/Det
  train_det:
    cmd: ./train_det.sh
    deps:
    - train_det.sh
    - mindeo/data_processed/Det
    - configs/det/ccpd/ch_PP-OCRv4_det_student.yml
    outs:
    - output/ccpd_det_ppocr_v4
  evaluate_det:
    cmd: ./val_det.sh
    deps:
    - val_det.sh
    - output/ccpd_det_ppocr_v4
  create_data_set_rec:
    cmd: python3 create_data_set_rec.py
    deps:
    - create_data_set_rec.py
    - mindeo/data/common/CCPD2019/ccpd_base/Label.txt
    - mindeo/data/common/CCPD2019/ccpd_blur/Label.txt
    - mindeo/data/common/CCPD2019/ccpd_challenge/Label.txt
    - mindeo/data/common/CCPD2019/ccpd_db/Label.txt
    - mindeo/data/common/CCPD2019/ccpd_fn/Label.txt
    - mindeo/data/common/CCPD2019/ccpd_rotate/Label.txt
    - mindeo/data/common/CCPD2019/ccpd_tilt/Label.txt
    - mindeo/data/common/CCPD2019/ccpd_weather/Label.txt
    - mindeo/data/common/CCPD2020/ccpd_green/test/Label.txt
    - mindeo/data/common/CCPD2020/ccpd_green/train/Label.txt
    - mindeo/data/common/CCPD2020/ccpd_green/val/Label.txt
    outs:
    - mindeo/data_processed/Rec
  train_rec:
    cmd: ./train_rec.sh
    deps:
    - train_rec.sh
    - mindeo/data_processed/Rec
    - configs/rec/ccpd/ch_PP-OCRv4_rec.yml
    outs:
    - output/ccpd_rec_ppocr_v4
  evaluate_rec:
    cmd: ./val_rec.sh
    deps:
    - val_rec.sh
    - output/ccpd_rec_ppocr_v4