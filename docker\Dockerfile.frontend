# 多阶段构建 - 构建阶段
FROM node:18-alpine AS builder

# 设置工作目录
WORKDIR /app

# 设置npm镜像源和配置
RUN npm config set registry https://registry.npmmirror.com/ && \
    npm config set cache /tmp/.npm && \
    npm config set fund false && \
    npm config set audit false

# 复制package文件
COPY code-sync/Frontend/package*.json ./

# 安装依赖（使用legacy-peer-deps解决依赖冲突）
RUN npm install --legacy-peer-deps --no-audit --no-fund

# 复制源代码
COPY code-sync/Frontend/ .

# 构建应用
RUN npm run build

# 验证构建结果
RUN ls -l dist

# 生产阶段
FROM nginx:alpine

# 复制nginx配置
COPY nginx.conf /etc/nginx/nginx.conf

# 复制构建好的文件到nginx目录
COPY --from=builder /app/dist /usr/share/nginx/html

# 创建健康检查文件
RUN echo '{"status":"ok"}' > /usr/share/nginx/html/health

# 暴露端口
EXPOSE 80

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=10s --retries=3 \
    CMD curl -f http://localhost/health || exit 1

# 启动nginx
CMD ["nginx", "-g", "daemon off;"]
