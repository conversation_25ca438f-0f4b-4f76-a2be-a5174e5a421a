# Generated manually for updating OCR model file paths

from django.db import migrations

def update_ocr_model_file_paths(apps, schema_editor):
    """
    更新OCR模型的文件路径以匹配新的文件夹名称
    """
    AIModel = apps.get_model('vision_app', 'AIModel')
    db_alias = schema_editor.connection.alias
    
    print("开始更新OCR模型文件路径...")
    
    # 1. 更新车牌识别模型路径: car_liencese -> car_liencese_ch
    print("1. 更新车牌识别模型文件路径...")
    license_plate_models = AIModel.objects.using(db_alias).filter(
        name='license_plate_cn',
        model_type='ocr',
        is_system_model=True
    )
    
    for model in license_plate_models:
        old_path = model.model_file.name
        if 'car_liencese/' in old_path:
            new_path = old_path.replace('car_liencese/', 'car_liencese_ch/')
            model.model_file = new_path
            model.save()
            print(f"   更新模型 ID {model.id} ({model.ocr_role}): {old_path} -> {new_path}")
    
    # 2. 更新身份证识别模型路径: Identity_card_number_ch -> Identity_card_number_en
    print("2. 更新身份证识别模型文件路径...")
    id_card_models = AIModel.objects.using(db_alias).filter(
        name='id_card_en',
        model_type='ocr',
        is_system_model=True
    )
    
    for model in id_card_models:
        old_path = model.model_file.name
        if 'Identity_card_number_ch/' in old_path:
            new_path = old_path.replace('Identity_card_number_ch/', 'Identity_card_number_en/')
            model.model_file = new_path
            model.save()
            print(f"   更新模型 ID {model.id} ({model.ocr_role}): {old_path} -> {new_path}")
    
    # 3. 更新通用文字识别模型路径: general_ocr_mobile -> general_ocr_mobile_ch_en
    print("3. 更新通用文字识别模型文件路径...")
    general_models = AIModel.objects.using(db_alias).filter(
        name='general_text_mobile_ch_en',
        model_type='ocr',
        is_system_model=True
    )
    
    for model in general_models:
        old_path = model.model_file.name
        if 'general_ocr_mobile/' in old_path:
            new_path = old_path.replace('general_ocr_mobile/', 'general_ocr_mobile_ch_en/')
            model.model_file = new_path
            model.save()
            print(f"   更新模型 ID {model.id} ({model.ocr_role}): {old_path} -> {new_path}")
    
    print("OCR模型文件路径更新完成！")

def reverse_update_ocr_model_file_paths(apps, schema_editor):
    """
    回滚操作：恢复原始文件路径
    """
    AIModel = apps.get_model('vision_app', 'AIModel')
    db_alias = schema_editor.connection.alias
    
    print("开始回滚OCR模型文件路径...")
    
    # 1. 回滚车牌识别模型路径
    license_plate_models = AIModel.objects.using(db_alias).filter(
        name='license_plate_cn',
        model_type='ocr',
        is_system_model=True
    )
    
    for model in license_plate_models:
        old_path = model.model_file.name
        if 'car_liencese_ch/' in old_path:
            new_path = old_path.replace('car_liencese_ch/', 'car_liencese/')
            model.model_file = new_path
            model.save()
    
    # 2. 回滚身份证识别模型路径
    id_card_models = AIModel.objects.using(db_alias).filter(
        name='id_card_en',
        model_type='ocr',
        is_system_model=True
    )
    
    for model in id_card_models:
        old_path = model.model_file.name
        if 'Identity_card_number_en/' in old_path:
            new_path = old_path.replace('Identity_card_number_en/', 'Identity_card_number_ch/')
            model.model_file = new_path
            model.save()
    
    # 3. 回滚通用文字识别模型路径
    general_models = AIModel.objects.using(db_alias).filter(
        name='general_text_mobile_ch_en',
        model_type='ocr',
        is_system_model=True
    )
    
    for model in general_models:
        old_path = model.model_file.name
        if 'general_ocr_mobile_ch_en/' in old_path:
            new_path = old_path.replace('general_ocr_mobile_ch_en/', 'general_ocr_mobile/')
            model.model_file = new_path
            model.save()
    
    print("OCR模型文件路径回滚完成！")

class Migration(migrations.Migration):

    dependencies = [
        ('vision_app', '0014_rename_and_cleanup_ocr_models'),
    ]

    operations = [
        migrations.RunPython(
            update_ocr_model_file_paths,
            reverse_update_ocr_model_file_paths
        ),
    ]
