#ifndef _PREPROCESS_H
#define _PREPROCESS_H

//-----------------------------------------------------------------------------
//  Includes

#include <cmath>
#include <cstdint>
#include <stdint.h>
#include <unordered_map>

#include "Log.hpp"
#include "ModelLoader.hpp"
#include "AIEngineCommon.h"
#include "utils/InputImage.hpp"

//-----------------------------------------------------------------------------
//  Definitions


//-----------------------------------------------------------------------------
//  Declarations

class BasePreprocess {
public:
    /**
     * @brief    
     *           请求前处理数据
     *           
     * @param    index:         输入张量索引
     *           
     * @retval   指向前处理数据的指针，找不到前处理数据时，返回空指针
     *           
     * @date     2024-02-29 Created by HuangJP
     */
    const PreprocessedData *Request_Preprocessed_Data(void *index)
    {
        // 尝试获取前处理数据
        if (preprocessed_data.find(index) == preprocessed_data.end())
        {
            // 找不到前处理数据，返回空指针
            LOGE("Unable to find preprocess data through index");
            return nullptr;
        }

        return (const PreprocessedData *)&preprocessed_data[index];
    }

    /**
     * @brief    
     *           图像输入函数
     *           
     * @param    index:         输入张量索引
     * @param    image_info:    指向图片信息的指针
     *           
     * @retval   错误码
     *           
     * @date     2024-02-28 Created by HuangJP
     */
    int Input_Image(void *index, struct ImageInfo *image_info)
    {
        // 检查输入数据是否合法
        if (image_info == nullptr)
        {
            LOGE("A null pointer have been detected");
            return AIENGINE_INPUT_DATA_ERROR;
        }

        PreprocessedData *prep = Get_Preprocessed_Data(index);
        if (prep == nullptr)
        {
            return AIENGINE_GOT_NULLPTR;
        }

        int rev = AIENGINE_NO_ERROR; // 返回值
        CoordinateVOC *coord = &prep->coord; // ROI坐标
        memset(coord, 0, sizeof(CoordinateVOC)); // 重置ROI坐标

        // 根据用户指定前处理类型计算检测区域起始位置和大小
        int start_y = 0, start_x = 0; // 检测起始位置
        int det_h = 0, det_w = 0; // 检测区域大小
        bool keep_ratio = false; // 高度和宽度方向保持相同的缩放比例
        bool integer_factor = false; // 整数缩放系数
        switch (image_info->prep_type)
        {
            // 全图resize
            case PREP_TYPE_FULL_IMG_RESIZE:
            {
                // 检测区域即为整张原图
                start_y = 0;
                start_x = 0;
                det_h = image_info->img_height;
                det_w = image_info->img_width;
                break;
            }

            // 根据四个坐标点做检测
            case PREP_TYPE_ROI_DEP_ON_QUAD_POINTS:
            case PREP_TYPE_ROI_DEP_ON_QUAD_POINTS_90_DEGREES_CLOCKWISE:
            {
                // 获取检测区域
                QuadPoints quad_points = image_info->quad_points;
                coord->xmin = std::min(quad_points.x0, quad_points.x3);
                coord->xmax = std::max(quad_points.x1, quad_points.x2);
                coord->ymin = std::min(quad_points.y0, quad_points.y1);
                coord->ymax = std::max(quad_points.y2, quad_points.y3);
                prep->det_h = coord->ymax - coord->ymin;
                prep->det_w = coord->xmax - coord->xmin;

                // 检测区域为四边形的最小包围框
                start_y = coord->ymin;
                start_x = coord->xmin;
                det_h = coord->ymax - coord->ymin + 1;
                det_w = coord->xmax - coord->xmin + 1;

                break;
            }

            // 中心挖图
            case PREP_TYPE_ROI_CETNER:
            {
                // 计算中心ROI坐标
                coord->xmin = (float)(image_info->img_width - prep->resize_w) / 2;
                coord->ymin = (float)(image_info->img_height - prep->resize_h) / 2;
                coord->xmax = coord->xmin + (prep->resize_w - 1);
                coord->ymax = coord->ymin + (prep->resize_h - 1);

                // 设置检测区域大小和起始坐标
                start_y = coord->ymin;
                start_x = coord->xmin;
                det_h = prep->resize_h;
                det_w = prep->resize_w;

                // 检查ROI区域是否合法
                if (_check_roi_coordinate(image_info->img_height, image_info->img_width, prep->resize_h, prep->resize_w, *coord) == false)
                {
                    return AIENGINE_ROI_COORD_ERROR;
                }
                break;
            }

            // 根据ROI坐标执行挖图
            case PREP_TYPE_ROI_DEP_ON_COORD:
            {
                // 检查ROI坐标是否合法
                *coord = image_info->roi_coord;
                if (_check_roi_coordinate(image_info->img_height, image_info->img_width, prep->resize_h, prep->resize_w, *coord) == false)
                {
                    return AIENGINE_ROI_COORD_ERROR;
                }

                // 设置检测区域大小和起始坐标
                start_y = coord->ymin;
                start_x = coord->xmin;
                det_h = coord->ymax - coord->ymin + 1;
                det_w = coord->xmax - coord->xmin + 1;
                break;
            }

            // 根据ROI坐标执行挖图自适应采样
            case PREP_TYPE_ROI_RESIZE_ADAPTIVE_DEP_ON_COORD:
            {
                keep_ratio = true; // 宽度和高度方向保持相同缩放比例
                integer_factor = true; // 使用整数缩放系数
                // 获取ROI坐标
                *coord = image_info->roi_coord;

                // 设置检测区域大小和起始坐标
                start_y = coord->ymin;
                start_x = coord->xmin;
                det_h = coord->ymax - coord->ymin + 1;
                det_w = coord->xmax - coord->xmin + 1;

                // 检查ROI坐标是否合法
                if (_check_roi_coordinate(image_info->img_height, image_info->img_width, det_h, det_w, *coord) == false)
                {
                    return AIENGINE_ROI_COORD_ERROR;
                }
                break;
            }

            // 根据中心坐标执行挖图采样
            case PREP_TYPE_ROI_RESIZE_2X_DEP_ON_CENTER_COORD:
            {
                int factor = 2; // 设置缩放系数
                // 设置测区域高度和宽度
                det_h = prep->resize_h * factor;
                det_w = prep->resize_w * factor;
                // 首先根据中心坐标计算右下角点的坐标，其坐标值等于中心坐标加上检测区域高度/宽度的一半
                coord->ymax = image_info->center_coord.center_y + (float)det_h / 2;
                coord->xmax = image_info->center_coord.center_x + (float)det_w / 2;
                // 接着根据右下角点坐标和检测区域大小计算左上角点坐标；注意右下角点坐标也算1个像素点，计算左上角点坐标时要算上该像素点（rows-1/cols-1）
                coord->ymin = coord->ymax - (det_h - 1);
                coord->xmin = coord->xmax - (det_w - 1);
                // 检查ROI区域是否合法
                if (_check_roi_coordinate(image_info->img_height, image_info->img_width, det_h, det_w, *coord) == false)
                {
                    // 传入坐标超出图像边界的情况，默认使用中心检测
                    coord->xmin = (float)(image_info->img_width - det_w) / 2;
                    coord->ymin = (float)(image_info->img_height - det_h) / 2;
                    coord->xmax = coord->xmin + (det_w - 1);
                    coord->ymax = coord->ymin + (det_h - 1);
                    // 检查ROI区域是否合法
                    if (_check_roi_coordinate(image_info->img_height, image_info->img_width, det_h, det_w, *coord) == false)
                    {
                        return AIENGINE_ROI_COORD_ERROR;
                    }
                }
                // 设置检测区域起始坐标
                start_y = coord->ymin;
                start_x = coord->xmin;
                break;
            }

            // 不支持的前处理类型
            default:
                LOGE("Unsupported preprocess method.");
                return AIENGINE_INPUT_DATA_ERROR;
        }

        // 获取图片输入图片工具
        InputImage *input_image = Get_Input_Image_Instance(image_info->bit_depth, image_info->color_format);
        if (input_image == nullptr)
        {
            LOGE("A null pointer has been encountered.");
            return AIENGINE_GOT_NULLPTR;
        }

        // 获取位深度提取范围
        int8_t rsn = image_info->sub_bit_range;
        if (rsn == MACR_SUB_BIT_EXTRACTED_RANGE_ADAPTIVE)
        {
            // 自动计算位深度提取范围
            rsn = input_image->get_sub_bit_range_adaptive(_sber_adap_ref_size, 
                                                _sber_adap_ref_size, 
                                                det_h, 
                                                det_w,
                                                image_info->img_data_pt,
                                                start_y,
                                                image_info->img_width,
                                                start_x);
        }

        // 判断子位深度提取范围是否合理
        if (_check_sub_bit_range(rsn, image_info->bit_depth) == false)
        {
            return AIENGINE_INVALID_PARAM;
        }

        // 清空输入张量
        input_image->clear_input_tensor(prep);

        // 填充输入张量
        if ((image_info->prep_type == PREP_TYPE_ROI_DEP_ON_QUAD_POINTS)
            || (image_info->prep_type == PREP_TYPE_ROI_DEP_ON_QUAD_POINTS_90_DEGREES_CLOCKWISE))
        {
            // 旋转和裁剪指定区域图像
            rev = input_image->rotate_to_fill_input_tensor(image_info->img_data_pt, image_info->quad_points, false, image_info->img_height, image_info->img_width, *prep, rsn);
        }
        else
        {
            // 缩放填充输入张量
            rev = input_image->resize_to_fill_input_tensor(image_info->img_data_pt, image_info->img_height, image_info->img_width, start_y, start_x, det_h, det_w, integer_factor, keep_ratio, *prep, rsn);
        }

        Input_Tensor_Flush_Memory(index); // 将输入张量数据同步至内存中

        return rev;
    }

private:
    /**
     * @brief    
     *           检查子位深度提取范围是否合理
     *           
     * @param    rsn:           右移位数（子位深度提取范围）
     * @param    bit_depth:     图像位深度
     *           
     * @retval   true:          提取范围合法
     * @retval   false:         提取范围不合法
     *           
     * @date     2024-03-06 Created by HuangJP
     */
    inline bool _check_sub_bit_range(int8_t rsn, bit_depth_t bit_depth)
    {
        if ((rsn < 0) || (((int16_t)bit_depth - rsn) < 8))
        {
            LOGD("rsn max: %d, but %d demand.", bit_depth - 8, rsn);
            LOGE("Sub-Bit depth extraction out of range!");
            return false;
        }

        return true;
    }

    /**
     * @brief    
     *           检查ROI坐标是否有效
     *           
     * @param    img_h,img_w:       输入图片的高度、宽度
     * @param    limit_h,limit_w:   限制检测区域的高度、宽度
     * @param    coord:             ROI坐标
     *           
     * @retval   true:              ROI坐标合法
     * @retval   false:             ROI坐标不合法
     *           
     * @date     2024-02-28 Created by HuangJP
     */
    inline bool _check_roi_coordinate(int img_h, int img_w, int limit_h, int limit_w, CoordinateVOC coord)
    {
        // ROI不能在原图区域范围外
        if ((coord.xmin < 0)
            || (coord.ymin < 0)
            || (coord.xmax > (img_w - 1))
            || (coord.ymax > (img_h - 1)))
        {
            LOGI("ROI is out of image range");
            return false;
        }

        // 计算ROI大小
        int cpy_h = coord.ymax - coord.ymin + 1;
        int cpy_w = coord.xmax - coord.xmin + 1;
        // ROI不能大于限定的检测区域大小
        if ((cpy_h > limit_h)
            || (cpy_h < 0)
            || (cpy_w > limit_w)
            || (cpy_w < 0))
        {
            LOGE("ROI size is out of limit");
            LOGD("ROI size(%d, %d) is out of limit(%d, %d)", cpy_w, cpy_h, limit_w, limit_h);
            return false;
        }

        return true;
    }

protected:
    typedef std::unordered_map<void *, PreprocessedData> preprocessed_data_map;

    const int _sber_adap_ref_size; // 自适应子位深度提取范围功能参考区域尺寸
    const BaseModelLoader::ModelData *_model_data; // 模型数据
    preprocessed_data_map preprocessed_data; // 前处理数据

    /**
     * @brief    
     *           前处理数据构造函数
     *           
     * @param    index:     输入张量索引
     * @param    data:      指向前处理数据的指针
     *           
     * @retval   错误码
     *           
     * @date     2024-07-30 Created by HuangJP
     */
    virtual int Construct_Preprocessed_Data(void *index, PreprocessedData *data) = 0;

    /**
     * @brief    
     *           同步输入张量数据到内存
     *           
     * @param    index:     输入张量索引
     *           
     * @retval   错误码
     *           
     * @date     2024-07-30 Created by HuangJP
     */
    virtual int Input_Tensor_Flush_Memory(void *index) = 0;

    /**
     * @brief    
     *           获取输入图片工具实例
     *           
     * @param    color_format:      输入图片颜色格式
     * @param    rsn:               右移位数
     * @param    memory_format:     内存存储格式
     *           
     * @retval   指向输入图片工具实例的指针
     *           
     * @date     2024-10-31 Created by HuangJP
     */
    virtual InputImage *Get_Input_Image_Instance(bit_depth_t bit_depth, color_format_t color_format) = 0; // 获取输入图片工具实例

    /**
     * @brief    
     *           获取前处理数据
     *           
     * @param    index:     输入张量索引
     *           
     * @retval   指向前处理数据的指针，若无法找到或构建指定张量的前处理数据，则返回空指针
     *           
     * @date     2024-02-28 Created by HuangJP
     */
    PreprocessedData *Get_Preprocessed_Data(void *index)
    {
        // 尝试获取前处理数据
        if (preprocessed_data.find(index) == preprocessed_data.end())
        {
            // 获取失败时，尝试构造前处理数据
            PreprocessedData data{};
            int error_code = Construct_Preprocessed_Data(index, &data);
            if (error_code != AIENGINE_NO_ERROR)
            {
                // 构造失败时，返回空指针
                LOGE("Unable to find/construct preprocess data through index");
                LOGI("Error Code: %d", error_code);
                return nullptr;
            }
            // 构造成功，添加前处理数据
            preprocessed_data[index] = data;
        }

        return &preprocessed_data[index];
    }

    /**
     * @brief    
     *           BasePreprocess析构函数
     *           
     * @date     2024-07-31 Created by HuangJP
     */
    virtual ~BasePreprocess()
    {

    }

    /**
     * @brief    
     *           BasePreprocess构造函数
     *           
     * @param    enable_normalization:      是否对输入数据做归一化
     * @param    sber_adap_ref_size:        自适应子位深度提取范围功能参考区域尺寸
     * @param    model_data:                模型数据
     *           
     * @date     2024-07-31 Created by HuangJP
     */
    BasePreprocess(int sber_adap_ref_size, const BaseModelLoader::ModelData *model_data):
        _sber_adap_ref_size(sber_adap_ref_size),
        _model_data(model_data)
    {

    }
};

#endif
//-----------------------------------------------------------------------------
//  End of file