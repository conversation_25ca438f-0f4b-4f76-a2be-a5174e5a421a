from ..YOLO import ObjectDete<PERSON>YOL<PERSON>
from pathlib import Path, PosixPath

if __name__ == "__main__":
    #================================================================
    #   训练
    #* 分配数据集
    yolo = ObjectDetectionYOLO(["Barcode", "Capped", "Uncapped", "Cap"])
    yolo.Distribute_Data_Set({
        "datasets/AI_Cap/DS_Test_Tube_Cap/Capped_R_E/deep" : 1.0,
        "datasets/AI_Cap/DS_Test_Tube_Cap/Capped_R_E/light" : 1.0,
        "datasets/AI_Cap/DS_Test_Tube_Cap/Capped_R_E/middle" : 1.0,
        "datasets/AI_Cap/DS_Test_Tube_Cap/Capped_R_L/deep" : 1.0,
        "datasets/AI_Cap/DS_Test_Tube_Cap/Capped_R_L/light" : 1.0,
        "datasets/AI_Cap/DS_Test_Tube_Cap/Capped_R_L/middle" : 1.0,
        "datasets/AI_Cap/DS_Test_Tube_Cap/Capped_W_E/deep" : 1.0,
        "datasets/AI_Cap/DS_Test_Tube_Cap/Capped_W_E/light" : 1.0,
        "datasets/AI_Cap/DS_Test_Tube_Cap/Capped_W_E/middle" : 1.0,
        "datasets/AI_Cap/DS_Test_Tube_Cap/Capped_W_L/deep" : 1.0,
        "datasets/AI_Cap/DS_Test_Tube_Cap/Capped_W_L/middle" : 1.0,
        "datasets/AI_Cap/DS_Test_Tube_Cap/Uncapped_R_E" : 0.8,
        "datasets/AI_Cap/DS_Test_Tube_Cap/Uncapped_R_L" : 0.8,
        "datasets/AI_Cap/DS_Test_Tube_Cap/Uncapped_W_E" : 0.8,
        "datasets/AI_Cap/DS_Test_Tube_Cap/Uncapped_W_L" : 0.8,
        "datasets/AI_Cap/CappedShort" : 1.0,
        "datasets/AI_Cap/UncappedLong" : 1.0,
        "datasets/AI_Cap/NewCappedDifficult" : 1.0,
        "datasets/AI_Cap/TestTubeWithLiquidHighQuality" : 1.0,
        "datasets/AI_Cap/UncappedShortWithoutLiquid" : 1.0,
        }, 0.8, 0.1, "datasets/Done/DataSet", seed=20240908)

    # 添加噪声
    yolo.Add_Quantized_Noise([
        "datasets/Done/DataSet/train",
    ], 1.0, 6, 32, "datasets/Done/DataSet/train", append_mode=True)

    # 裁剪检测区域
    for data_type in ["train", "val", "test"]:
        yolo.Vertical_Crop([
                f"datasets/Done/DataSet/{data_type}",
            ],
            192, 0.42, 8, f"datasets/Done/Vertical_Crop_Middle/{data_type}", mode="middle", min_obj_w=10.0)

    # 拼接图片
    for data_type in ["train", "val", "test"]:
        yolo.Horizontal_Joint([
                f"datasets/Done/Vertical_Crop_Middle/{data_type}",
            ], ["Capped", "Uncapped"], 36, f"datasets/Done/Horizontal_Joint/{data_type}", min_obj_w=16)

    # 按照使用场景裁剪图片
    for data_type in ["train", "val", "test"]:
        yolo.Horizontal_Crop_Ref([
            f"datasets/Done/Vertical_Crop_Middle/{data_type}",
            f"datasets/Done/Horizontal_Joint/{data_type}",
            ], "Barcode", 0, 192, f"datasets/Done/Horizontal_Ref/{data_type}", min_obj_h=32,
            maintain_classes={"Cap":{"ratio":0.9, "relevance":["Capped"]}})

    # 按照板端的方式resize图片
    for data_type in ["train", "val", "test"]:
        yolo.Resize_Image([
            f"datasets/Done/Horizontal_Ref/{data_type}",
            ], (480, 96), f"datasets/Done/Resize/{data_type}")

    # 转换YOLO标签
    yolo = ObjectDetectionYOLO(["Capped", "Uncapped", "Cap"])
    for data_type in ["train", "val", "test"]:
        yolo.Labelme_To_YOLO([
            f"datasets/Done/Vertical_Crop_Middle/{data_type}",
            f"datasets/Done/Horizontal_Joint/{data_type}",
            f"datasets/Done/Horizontal/{data_type}",
            f"datasets/Done/Horizontal_Ref/{data_type}",
            f"datasets/Done/Resize/{data_type}",
            ], f"datasets/Train/{data_type}")

    #================================================================
    #   测试
    # #* 分配数据集
    # yolo = ObjectDetectionYOLO(["Barcode", "Capped", "Uncapped", "Cap"])
    # yolo.Distribute_Data_Set({
    #     "datasets/AI_Cap/z_Mixed_test_0909-Done-Full": 1.0,
    #     }, 1.0, 0.0, "datasets/Done/DataSet", seed=20240908)

    # # 裁剪检测区域
    # for data_type in ["train", "val", "test"]:
    #     yolo.Vertical_Crop([
    #             f"datasets/Done/DataSet/{data_type}",
    #         ],
    #         192, 0.42, 8, f"datasets/Done/Vertical_Crop_Middle/{data_type}", mode="middle", min_obj_w=10.0)

    # # 按照使用场景裁剪图片
    # for data_type in ["train", "val", "test"]:
    #     yolo.Horizontal_Crop_Ref([
    #         f"datasets/Done/Vertical_Crop_Middle/{data_type}",
    #         ], "Barcode", 0, 192, f"datasets/Done/Horizontal_Ref/{data_type}", min_obj_h=32,
    #         maintain_classes={"Cap":{"ratio":0.9, "relevance":["Capped"]}})

    # # 按照板端的方式resize图片
    # for data_type in ["train", "val", "test"]:
    #     yolo.Resize_Image([
    #         f"datasets/Done/Horizontal_Ref/{data_type}",
    #         ], (480, 96), f"datasets/Done/Resize/{data_type}")

    # # 转换YOLO标签
    # # yolo = ObjectDetectionYOLO(["Capped", "Uncapped", "Cap"])
    # yolo = ObjectDetectionYOLO(["Capped", "Uncapped"])
    # for data_type in ["train", "val", "test"]:
    #     yolo.Labelme_To_YOLO([
    #         f"datasets/Done/Resize/{data_type}",
    #         ], f"datasets/Test/{data_type}")