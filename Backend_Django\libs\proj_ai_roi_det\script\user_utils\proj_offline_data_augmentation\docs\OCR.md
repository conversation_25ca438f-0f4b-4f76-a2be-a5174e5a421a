<div align="center">
    <h1>OCR</h1>
    OCR模型训练离线增强脚本
</div>

# DatasetOCR

## 方法预览

| 函数名                                          | 功能                                        |
| ----------------------------------------------- | ------------------------------------------- |
| [Distribute_Data_Set](#Distribute_Data_Set)     | 基于PaddleOCR标签划分训练集、验证集和测试集 |
| [Classify_Data](#Classify_Data)                 | 对数据进行分类                              |
| [Write_Classified_Data](#Write_Classified_Data) | 保存分类数据                                |
| [Affine_Transform](#Affine_Transform)           | 对图像做仿射变换                            |
| [Add_Shadow](#Add_Shadow)                       | 添加阴影                                    |
| [Add_Noise](#Add_Noise)                         | 往图片中添加噪声                            |

## 方法说明

### Distribute_Data_Set

- 功能

基于PaddleOCR标签划分训练集、验证集和测试集。

- 声明

```python
def Distribute_Data_Set(
        self,
        src_pathes: dict[str, float],
        train: float,
        val: float,
        dst_path: str,
        append_mode: bool = False,
        seed: int = 20000330,
        only_label: bool = False,
        ) -> tuple[str] | None:
```

- 返回值

训练集、验证集和测试集位置，失败时返回None。

- 形参

| 参数名称    | 描述                                                         |
| ----------- | ------------------------------------------------------------ |
| src_pathes  | 数据集路径字典及分配比例                                     |
| train       | 训练数据集比例                                               |
| val         | 验证数据集比例                                               |
| dst_path    | 目标生成路径                                                 |
| append_mode | 是否采用追加模式，追加模式下会在保留原有数据集的基础上增加新数据 |
| seed        | 随机种子，用于确保数据集划分的结果不变                       |
| only_label  | 仅生成label                                                  |

- 示例

```python
det = ocrDet()
# 分配数据集
det.Distribute_Data_Set({
    "data/CCPD2019/ccpd_base" : 1.0,
    "data/CCPD2019/ccpd_blur" : 1.0,
    "data/CCPD2019/ccpd_challenge" : 1.0,
    "data/CCPD2019/ccpd_db" : 1.0,
    "data/CCPD2019/ccpd_fn" : 1.0,
    "data/CCPD2019/ccpd_rotate" : 1.0,
    "data/CCPD2019/ccpd_tilt" : 1.0,
    "data/CCPD2019/ccpd_weather" : 1.0,
    "data/CCPD2020/ccpd_green/test" : 1.0,
    "data/CCPD2020/ccpd_green/train" : 1.0,
    "data/CCPD2020/ccpd_green/val" : 1.0,
    }, 0.75, 0.2, "datasets/Det/DataSet", seed=20241030, only_label=True)
```

### Classify_Data

- 功能

对数据进行分类。

- 声明

```python
def Classify_Data(
        self,
        src_pathes: dict[str, float],
        classify_func: Callable[[str], str],
    ) -> dict[str, dict[str, list[LabelPaddleOCR]]] | None:
```

- 返回值

分类好的数据，键：类别ID，值：图片及对应标签，失败时返回None。

- 形参

| 参数名称      | 描述                                       |
| ------------- | ------------------------------------------ |
| src_pathes    | 源文件路径列表                             |
| classify_func | 数据分类函数，输入：字符内容，输出：分类ID |

- 示例

```python
det = ocrDet()
classify_func = lambda x: x[0:1]
for data_type in ["train", "val", "test"]:
    classified_labels_dict = det.Classify_Data([
        f"datasets/Det/DataSet/{data_type}",
        ], classify_func)
```

### Write_Classified_Data

- 说明

保存分类数据。

- 声明

```python
def Write_Classified_Data(
        self,
        modified_proportion: dict[str, float],
        classified_labels_dict: dict[str, dict[str, list[LabelPaddleOCR]]],
        dst_path: str,
        only_label: bool = False,
    ) -> str | None:
```

- 返回值

生成数据位置，失败时返回None。

- 形参

| 参数名称               | 描述                                         |
| ---------------------- | -------------------------------------------- |
| modified_proportion    | 调控比例，用于控制数据占比                   |
| classified_labels_dict | 分类好的数据，键：类别ID，值：图片及对应标签 |
| dst_path               | 目标生成路径                                 |
| only_label             | 仅生成label                                  |

- 示例

```python
# 减少特定数据的比例
det = ocrDet()
classify_func = lambda x: x[0:1]
for data_type in ["train", "val", "test"]:
    classified_labels_dict = det.Classify_Data([
        f"datasets/Det/DataSet/{data_type}",
        ], classify_func)

    det.Write_Classified_Data({
        "皖": 0.01,
    }, classified_labels_dict, f"datasets/Det/Classify/{data_type}", only_label=True)
```

### Affine_Transform

- 说明

对图像做仿射变换。

- 声明

```python
def Affine_Transform(
        self,
        src_pathes: list[str],
        quantity: int,
        dst_path: str,
        scale_range: tuple[float] = (0.8, 1.2),
        shear_range: tuple[float] = (-0.1, 0.1),
        angle_range: float = (-10, 10),
        seed: int = 20241108,
    ) -> str | None:
```

- 返回值

生成数据位置，失败时返回None。

- 形参

| 参数名称    | 描述                           |
| ----------- | ------------------------------ |
| src_pathes  | 源文件路径列表                 |
| quantity    | 生成图片数量                   |
| dst_path    | 目标生成路径                   |
| scale_range | 缩放范围（单位：百分比）       |
| shear_range | 剪切范围（单位：百分比）       |
| angle_range | 旋转范围（单位：度）           |
| seed        | 随机种子，用于确保随机过程一致 |

- 示例

```python
det = ocrDet()
for data_type in ["train", "val", "test"]:
    det.Affine_Transform([
        f"datasets/Det/Classify/{data_type}",
        ], 1, f"datasets/Det/Affine/{data_type}", angle_range=(-90, 90))
```

### Add_Shadow

- 说明

添加阴影。

- 声明

```python
def Add_Shadow(
        self,
        src_pathes: list[str],
        quantity: int,
        dst_path: str,
        num_circles: int = 5,
        max_radius: int = 50,
        shadow_intensity_range: tuple[float] = (0.3, 0.6),
        brightness_adjust_range: tuple[float] = (0.5, 3.5),
        seed: int = 20241108,
    ) -> str | None:
```

- 返回值

生成数据位置，失败时返回None。

- 形参

| 参数名称                | 描述                           |
| ----------------------- | ------------------------------ |
| src_pathes              | 源文件路径列表                 |
| quantity                | 生成图片数量                   |
| dst_path                | 目标生成路径                   |
| num_circles             | 用于生成阴影的随机圆的个数     |
| max_radius              | 圆形阴影的最大半径             |
| shadow_intensity_range  | 阴影的强度范围                 |
| brightness_adjust_range | 亮度调整范围                   |
| seed                    | 随机种子，用于确保随机过程一致 |

### Add_Noise

- 说明

往图片中添加噪声。

- 声明

```python
def Add_Noise(
        self,
        src_pathes: list[str],
        proportion: float,
        dst_path: str,
        noise_type: dict[str, tuple[float]] = {'poisson': (4.0, 5.0), 'gaussian': (10.0, 15.0), 'halo': (0.1, 0.3)},
        append_mode: bool = False,
        seed: int = 20241114,
    ):
```

- 返回值

生成数据位置，失败时返回None。

- 形参

| 参数名称   | 描述                                                         |
| ---------- | ------------------------------------------------------------ |
| src_pathes | 源文件路径列表                                               |
| proportion | 加噪图片占比                                                 |
| dst_path   | 目标生成路径                                                 |
| noise_type | 添加的噪声类型及噪声强度范围，可选[<br />'poisson': 泊松噪声,<br /> 'gaussian': 高斯噪声,<br />'halo': 光晕,<br />] |
| seed       | 随机种子，用于确保随机过程一致                               |

- 示例

```python
det = ocrDet()
for data_type in ["train", "val", "test"]:
    det.Add_Noise([
        f"datasets/Det/Classify/{data_type}",
        ], 0.2, f"datasets/Det/Add_Noise/{data_type}")
```

# ocrDet

> 继承自[DatasetOCR](#DatasetOCR)

## 方法预览

| 函数名                        | 功能     |
| ----------------------------- | -------- |
| [Resize_Image](#Resize_Image) | 缩放图像 |
| [Crop_Image](#Crop_Image)     | 裁剪图像 |

## 方法说明

### Resize_Image

- 说明

缩放图像。

- 声明

```python
def Resize_Image(
        self,
        src_pathes: list[str],
        size:tuple[int, int],
        dst_path: str,
        keep_ratio: bool = True,
        mode: str = "float_factor",
    ) -> str | None:
```

- 返回值

生成数据位置，失败时返回None。

- 形参

| 参数名称   | 描述                                                         |
| ---------- | ------------------------------------------------------------ |
| src_pathes | 源文件路径列表                                               |
| size       | 目标缩放尺寸（H, W）                                         |
| dst_path   | 目标生成路径                                                 |
| keep_ratio | 保持高度和宽度方向上的缩放系数一致，确保图片缩放后不会变形   |
| mode       | 缩放模式，可选[<br />"float_factor":使用浮点缩放系数,<br />] |

- 示例

```python
det = ocrDet()
for data_type in ["train", "val", "test"]:
    det.Resize_Image([
        f"datasets/Det/DataSet/{data_type}",
        ], [320, 320], f"datasets/Det/Train/{data_type}")
```

### Crop_Image

- 说明

裁剪图像。

- 声明

```python
def Crop_Image(
        self,
        src_pathes: list[str],
        size:tuple[int, int],
        dst_path: str,
        seed: int = 20241108,
    ) -> str | None:
```

- 返回值

生成数据位置，失败时返回None。

- 形参

| 参数名称   | 描述                           |
| ---------- | ------------------------------ |
| src_pathes | 源文件路径列表                 |
| size       | 目标裁剪尺寸（H, W）           |
| dst_path   | 目标生成路径                   |
| seed       | 随机种子，用于确保随机过程一致 |

- 示例

```python
det = ocrDet()
for data_type in ["train", "val", "test"]:
    det.Crop_Image([
        f"datasets/Det/Add_Shadow/{data_type}",
        ], [480, 480], f"datasets/Det/Crop/{data_type}")
```

# ocrRec

## 方法预览

| 函数名                              | 功能           |
| ----------------------------------- | -------------- |
| [Rotate_And_Crop](#Rotate_And_Crop) | 旋转和裁剪图像 |

## 方法说明

### Rotate_And_Crop

- 说明

旋转和裁剪图像。

- 声明

```python
def Rotate_And_Crop(
        self,
        src_pathes: list[str],
        size:tuple[int, int],
        dst_path: str,
    ):
```

- 返回值

生成数据位置，失败时返回None。

- 形参

| 参数名称   | 描述                 |
| ---------- | -------------------- |
| src_pathes | 源文件路径列表       |
| size       | 目标缩放尺寸（H, W） |
| dst_path   | 目标生成路径         |

- 示例

```python
rec = ocrRec()
for data_type in ["train", "val", "test"]:
    rec.Rotate_And_Crop([
        f"datasets/Rec/DataSet/{data_type}",
        ], (48, 320), f"datasets/Train/{data_type}")
```

