#ifndef _LOG_H
#define _LOG_H

//-----------------------------------------------------------------------------
//  Includes

#include <queue>
#include <ctime>
#include <sstream>
#include <cstring>
//-----------------------------------------------------------------------------
//  Definitions

// 是否内置log功能
#ifndef MACR_BUILD_IN_LOG
#define MACR_BUILD_IN_LOG               (1)
#endif

// 是否默认开启log
#ifndef MACR_LOG_ENABLE_BY_DEFAULT
#define MACR_LOG_ENABLE_BY_DEFAULT      (false)
#endif

// 判断是否内置LOG功能
#if (MACR_BUILD_IN_LOG == 1)

// log分支
#define log_branch(action)\
if (__builtin_expect(Log::Instance()->log_enable, 0))\
{\
    action;\
}

/**
 * @brief    
 *           指定使用的输出函数
 *           
 * @param    0:     system_log
 * @param    1:     __android_log_print
 * @param    2:     printf
 *           
 * @date     2024-02-27 Created by HuangJP
 */
#ifndef MACR_LOG_FUNC
#define MACR_LOG_FUNC (0)
#endif

/**
 * @brief    
 *           开启/关闭LOG
 *           
 * @param    enable:    开启/关闭LOG，true为开启，false为关闭
 *           
 * @date     2024-02-27 Created by HuangJP
 */
#define SET_LOG_ENABLE(enable) (Log::Instance()->log_enable = enable)

#if (MACR_LOG_FUNC == 0)
#define LOGD(...)
#define LOGI(...)
#define LOGW(...)
#define LOGE(message) log_branch(Log::Instance()->Record_Error_Message(message))

#elif (MACR_LOG_FUNC == 1) /* (MACR_LOG_FUNC == 0) */
#include <android/log.h>
#define MACR_LOG_TAG ("AIEngine")
#define LOGD(...) log_branch(__android_log_print(ANDROID_LOG_DEBUG, MACR_LOG_TAG, __VA_ARGS__))
#define LOGI(...) log_branch(__android_log_print(ANDROID_LOG_INFO, MACR_LOG_TAG, __VA_ARGS__))
#define LOGW(...) log_branch(__android_log_print(ANDROID_LOG_WARN, MACR_LOG_TAG, __VA_ARGS__))
#define LOGE(message) log_branch(__android_log_print(ANDROID_LOG_ERROR, MACR_LOG_TAG, message))

#elif (MACR_LOG_FUNC == 2) /* (MACR_LOG_FUNC == 0) */
#include <stdio.h>
#define LOGD(...) log_branch(printf("[D]: ");printf(__VA_ARGS__);printf("\n"))
#define LOGI(...) log_branch(printf("[I]: ");printf(__VA_ARGS__);printf("\n"))
#define LOGW(...) log_branch(printf("[W]: ");printf(__VA_ARGS__);printf("\n"))
#define LOGE(message) log_branch(printf("[E] [%s:%d]: ", __FILE__, __LINE__);printf(message);printf("\n"))

#endif /* (MACR_LOG_FUNC == 0) */

#else /* (MACR_BUILD_IN_LOG == 1) */
#define LOGD(...)
#define LOGI(...)
#define LOGW(...)
#define LOGE(...)

#define SET_LOG_ENABLE(...)

#endif /* (MACR_BUILD_IN_LOG == 1) */
//-----------------------------------------------------------------------------
//  Declarations

// 判断是否内置LOG功能
#if (MACR_BUILD_IN_LOG == 1)
/**
 * @brief    
 *           Log类
 *           
 * @date     2024-03-05 Created by HuangJP
 */
class Log
{
public:
    bool log_enable;

    /**
     * @brief    
     *           获取Log模块实例
     *           
     * @retval   指向Log模块实例的指针
     *           
     * @date     2024-03-05 Created by HuangJP
     */
    static Log *Instance(void)
    {
        static Log log;
        return &log;
    }

    /**
     * @brief    
     *           记录错误信息
     *           
     * @param    message:   错误信息
     *           
     * @date     2024-03-05 Created by HuangJP
     */
    void Record_Error_Message(const char *message)
    {
        // 获取当前经过时间
        struct timespec now;
        clock_gettime(CLOCK_REALTIME, &now);
        long seconds = now.tv_sec - time_base.tv_sec;
        long nanoseconds = now.tv_nsec - time_base.tv_nsec;
        float timestamp = seconds + nanoseconds * (1e-9);

        int len = strlen(message); // 获取字符串长度
        error_msg_queue.push((msg_t){
            .str = message,
            .timestamp = timestamp,
            .len = len,
        }); // 记录数据到列表中
    }

    /**
     * @brief    
     *           获取错误信息
     *           
     * @param    buff:          错误信息存储地址
     * @param    buff_size:     存储缓冲大小
     *           
     * @retval   剩余消息数量
     *           
     * @date     2024-03-05 Created by HuangJP
     */
    int Pop_Error_Messages(char *buff, int buff_size)
    {
        int remain_buff_size = buff_size; // 剩余buff大小
        
        // 遍历队列获取错误信息
        while (!error_msg_queue.empty())
        {
            // 获取队列中的错误信息
            msg_t msg = error_msg_queue.front();

            std::stringstream ss;
            ss << "[" << msg.timestamp << "]" << msg.str << std::endl;
            int len = ss.tellp();
            // 判断剩余buff大小是否不足够填充信息
            if (remain_buff_size < len)
            {
                break; // 剩余buff大小不足时，停止填充
            }

            ss.write(buff + buff_size - remain_buff_size, len);
            remain_buff_size -= len; // 更新剩余buff长度 

            error_msg_queue.pop(); // 从队列中移除已添加的数据
        }

        return error_msg_queue.size();
    }

private:
    typedef struct{
        const char *str; // 指向信息内容的指针
        float timestamp; // 时间戳
        int len; // 信息长度
    }msg_t;

    ~Log() {}
    Log()
    {
        // 初始化变量
        log_enable = MACR_LOG_ENABLE_BY_DEFAULT;
        clock_gettime(CLOCK_REALTIME, &time_base); // 记录基准时间
    }

    struct timespec time_base; // 基准时间
    std::queue<msg_t> error_msg_queue; // 错误信息队列
};

#endif /* (MACR_BUILD_IN_LOG == 1) */

#endif
//-----------------------------------------------------------------------------
//  End of file