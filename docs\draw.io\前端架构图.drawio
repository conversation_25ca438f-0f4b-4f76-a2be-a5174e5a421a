<mxfile host="Electron" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/26.2.15 Chrome/134.0.6998.205 Electron/35.2.1 Safari/537.36" version="26.2.15">
  <diagram name="第 1 页" id="yjIkY3QX-MKA_AXwZHXy">
    <mxGraphModel dx="2852" dy="1718" grid="0" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1654" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="Lvz0KHMB1awMAYVr9GuM-81" value="&lt;b&gt;&lt;font style=&quot;font-size: 25px;&quot;&gt;FastAPI&lt;/font&gt;&lt;/b&gt;" style="rounded=0;whiteSpace=wrap;html=1;verticalAlign=top;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="43" y="1512" width="1214" height="135" as="geometry" />
        </mxCell>
        <mxCell id="Lvz0KHMB1awMAYVr9GuM-82" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="Lvz0KHMB1awMAYVr9GuM-80" target="Lvz0KHMB1awMAYVr9GuM-81" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="Lvz0KHMB1awMAYVr9GuM-80" value="&lt;b&gt;&lt;font style=&quot;font-size: 25px;&quot;&gt;Frontend（React）&lt;/font&gt;&lt;/b&gt;" style="rounded=0;whiteSpace=wrap;html=1;verticalAlign=top;fillColor=#e1d5e7;strokeColor=#9673a6;" parent="1" vertex="1">
          <mxGeometry x="43" y="18" width="1490" height="1433" as="geometry" />
        </mxCell>
        <mxCell id="Lvz0KHMB1awMAYVr9GuM-34" value="认证流程" style="whiteSpace=wrap;strokeWidth=2;verticalAlign=top;fontStyle=1;fontSize=25;fillColor=#d5e8d4;strokeColor=#82b366;" parent="1" vertex="1">
          <mxGeometry x="958" y="173" width="526" height="879" as="geometry" />
        </mxCell>
        <mxCell id="Lvz0KHMB1awMAYVr9GuM-35" value="用户交互 (尝试访问页面/登录)" style="whiteSpace=wrap;strokeWidth=2;fontStyle=1;fontSize=20;" parent="1" vertex="1">
          <mxGeometry x="1088" y="45" width="274" height="78" as="geometry" />
        </mxCell>
        <mxCell id="Lvz0KHMB1awMAYVr9GuM-36" value="路由守卫 / 认证检查" style="rhombus;strokeWidth=2;whiteSpace=wrap;fontSize=20;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="1112" y="262" width="226" height="136" as="geometry" />
        </mxCell>
        <mxCell id="Lvz0KHMB1awMAYVr9GuM-37" value="登录界面/组件" style="whiteSpace=wrap;strokeWidth=2;fontSize=20;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="1183" y="498" width="164" height="54" as="geometry" />
        </mxCell>
        <mxCell id="Lvz0KHMB1awMAYVr9GuM-38" value="调用认证API" style="whiteSpace=wrap;strokeWidth=2;fontSize=20;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="1085" y="710" width="147" height="54" as="geometry" />
        </mxCell>
        <mxCell id="Lvz0KHMB1awMAYVr9GuM-39" value="登录成功: 更新状态管理 (存Token, 用户角色)" style="whiteSpace=wrap;strokeWidth=2;fontSize=20;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="982" y="914" width="260" height="78" as="geometry" />
        </mxCell>
        <mxCell id="Lvz0KHMB1awMAYVr9GuM-40" value="显示失败提示" style="whiteSpace=wrap;strokeWidth=2;fontSize=20;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="1282" y="710" width="156" height="54" as="geometry" />
        </mxCell>
        <mxCell id="Lvz0KHMB1awMAYVr9GuM-41" value="UI 路由与基础布局" style="whiteSpace=wrap;strokeWidth=2;fontSize=20;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="389" y="502" width="192" height="54" as="geometry" />
        </mxCell>
        <mxCell id="Lvz0KHMB1awMAYVr9GuM-42" value="角色判断 (读取状态中的角色)" style="rhombus;strokeWidth=2;whiteSpace=wrap;fontSize=20;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="325" y="615" width="320" height="179" as="geometry" />
        </mxCell>
        <mxCell id="Lvz0KHMB1awMAYVr9GuM-73" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.25;exitY=1;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="Lvz0KHMB1awMAYVr9GuM-43" target="Lvz0KHMB1awMAYVr9GuM-46" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="Lvz0KHMB1awMAYVr9GuM-76" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.75;exitY=1;exitDx=0;exitDy=0;entryX=0.25;entryY=0;entryDx=0;entryDy=0;" parent="1" source="Lvz0KHMB1awMAYVr9GuM-43" target="Lvz0KHMB1awMAYVr9GuM-47" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="284" y="1015" />
              <mxPoint x="284" y="1116" />
              <mxPoint x="559" y="1116" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="Lvz0KHMB1awMAYVr9GuM-43" value="开发者视图: 数据集/微调等" style="whiteSpace=wrap;strokeWidth=2;fontSize=20;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="56" y="961" width="255" height="54" as="geometry" />
        </mxCell>
        <mxCell id="Lvz0KHMB1awMAYVr9GuM-74" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.25;exitY=1;exitDx=0;exitDy=0;entryX=0.25;entryY=0;entryDx=0;entryDy=0;" parent="1" source="Lvz0KHMB1awMAYVr9GuM-44" target="Lvz0KHMB1awMAYVr9GuM-46" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="423" y="1068" />
              <mxPoint x="234" y="1068" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="Lvz0KHMB1awMAYVr9GuM-77" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.75;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="Lvz0KHMB1awMAYVr9GuM-44" target="Lvz0KHMB1awMAYVr9GuM-47" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="546" y="1061" />
              <mxPoint x="621" y="1061" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="Lvz0KHMB1awMAYVr9GuM-44" value="测试人员视图: 批量推理等" style="whiteSpace=wrap;strokeWidth=2;fontSize=20;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="361" y="961" width="247" height="54" as="geometry" />
        </mxCell>
        <mxCell id="Lvz0KHMB1awMAYVr9GuM-75" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.25;exitY=1;exitDx=0;exitDy=0;entryX=0.75;entryY=0;entryDx=0;entryDy=0;" parent="1" source="Lvz0KHMB1awMAYVr9GuM-45" target="Lvz0KHMB1awMAYVr9GuM-46" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="720" y="1147" />
              <mxPoint x="345" y="1147" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="Lvz0KHMB1awMAYVr9GuM-79" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.75;exitY=1;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="Lvz0KHMB1awMAYVr9GuM-45" target="Lvz0KHMB1awMAYVr9GuM-47" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="Lvz0KHMB1awMAYVr9GuM-45" value="业务人员视图: 交互推理等" style="whiteSpace=wrap;strokeWidth=2;fontSize=20;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="658" y="961" width="247" height="54" as="geometry" />
        </mxCell>
        <mxCell id="Lvz0KHMB1awMAYVr9GuM-72" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="Lvz0KHMB1awMAYVr9GuM-46" target="Lvz0KHMB1awMAYVr9GuM-47" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="Lvz0KHMB1awMAYVr9GuM-46" value="通用组件: 导航/通知等" style="whiteSpace=wrap;strokeWidth=2;fontSize=20;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="178" y="1206" width="223" height="54" as="geometry" />
        </mxCell>
        <mxCell id="Lvz0KHMB1awMAYVr9GuM-47" value="状态管理 (Redux/Context)" style="whiteSpace=wrap;strokeWidth=2;fontSize=20;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="497" y="1206" width="248" height="54" as="geometry" />
        </mxCell>
        <mxCell id="kOM-1A1dfKhWAhorgb91-5" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="Lvz0KHMB1awMAYVr9GuM-48" target="kOM-1A1dfKhWAhorgb91-1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="Lvz0KHMB1awMAYVr9GuM-48" value="API 请求模块 (React Query - 用于业务请求)" style="whiteSpace=wrap;strokeWidth=2;fontSize=20;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="358" y="1352" width="260" height="78" as="geometry" />
        </mxCell>
        <mxCell id="Lvz0KHMB1awMAYVr9GuM-49" value="FastAPI 后端 (认证接口)" style="whiteSpace=wrap;strokeWidth=2;fontSize=20;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="914" y="1559" width="303" height="67" as="geometry" />
        </mxCell>
        <mxCell id="Lvz0KHMB1awMAYVr9GuM-50" value="FastAPI 后端 (业务接口)" style="whiteSpace=wrap;strokeWidth=2;fontSize=20;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="158" y="1559" width="303" height="67" as="geometry" />
        </mxCell>
        <mxCell id="Lvz0KHMB1awMAYVr9GuM-51" value="" style="curved=1;startArrow=none;endArrow=block;exitX=0.5;exitY=1;entryX=0.5;entryY=0;rounded=0;" parent="1" source="Lvz0KHMB1awMAYVr9GuM-35" target="Lvz0KHMB1awMAYVr9GuM-36" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Lvz0KHMB1awMAYVr9GuM-52" value="需要认证" style="curved=1;startArrow=none;endArrow=block;exitX=0.65;exitY=1;entryX=0.5;entryY=0;rounded=0;fontSize=18;" parent="1" source="Lvz0KHMB1awMAYVr9GuM-36" target="Lvz0KHMB1awMAYVr9GuM-37" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1265" y="435" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="Lvz0KHMB1awMAYVr9GuM-53" value="" style="curved=1;startArrow=none;endArrow=block;exitX=0.22;exitY=1;entryX=0.5;entryY=0;rounded=0;" parent="1" source="Lvz0KHMB1awMAYVr9GuM-37" target="Lvz0KHMB1awMAYVr9GuM-38" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1159" y="562" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="Lvz0KHMB1awMAYVr9GuM-54" value="" style="curved=1;startArrow=none;endArrow=block;exitX=0.49;exitY=1;entryX=0.5;entryY=0;rounded=0;" parent="1" source="Lvz0KHMB1awMAYVr9GuM-38" target="Lvz0KHMB1awMAYVr9GuM-39" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1112" y="857" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="Lvz0KHMB1awMAYVr9GuM-55" value="Login Failed" style="curved=1;startArrow=none;endArrow=block;exitX=0.75;exitY=1;entryX=0.5;entryY=0;rounded=0;fontSize=18;" parent="1" source="Lvz0KHMB1awMAYVr9GuM-37" target="Lvz0KHMB1awMAYVr9GuM-40" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1360" y="562" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="Lvz0KHMB1awMAYVr9GuM-56" value="已经登录过且登录状态仍然有效" style="curved=1;startArrow=none;endArrow=block;exitX=0;exitY=0.89;entryX=1;entryY=0.3;rounded=0;fontSize=18;" parent="1" source="Lvz0KHMB1awMAYVr9GuM-36" target="Lvz0KHMB1awMAYVr9GuM-41" edge="1">
          <mxGeometry x="-0.0022" relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1049" y="435" />
            </Array>
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Lvz0KHMB1awMAYVr9GuM-57" value="" style="curved=1;startArrow=none;endArrow=block;exitX=0.5;exitY=1;entryX=0.5;entryY=0;rounded=0;" parent="1" source="Lvz0KHMB1awMAYVr9GuM-41" target="Lvz0KHMB1awMAYVr9GuM-42" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Lvz0KHMB1awMAYVr9GuM-58" value="开发者" style="curved=1;startArrow=none;endArrow=block;exitX=0;exitY=0.79;entryX=0.5;entryY=0;rounded=0;fontSize=18;" parent="1" source="Lvz0KHMB1awMAYVr9GuM-42" target="Lvz0KHMB1awMAYVr9GuM-43" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="184" y="913" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="Lvz0KHMB1awMAYVr9GuM-59" value="测试人员" style="curved=1;startArrow=none;endArrow=block;exitX=0.5;exitY=1;entryX=0.5;entryY=0;rounded=0;fontSize=18;" parent="1" source="Lvz0KHMB1awMAYVr9GuM-42" target="Lvz0KHMB1awMAYVr9GuM-44" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Lvz0KHMB1awMAYVr9GuM-60" value="业务人员" style="curved=1;startArrow=none;endArrow=block;exitX=1;exitY=0.8;entryX=0.5;entryY=0;rounded=0;fontSize=18;" parent="1" source="Lvz0KHMB1awMAYVr9GuM-42" target="Lvz0KHMB1awMAYVr9GuM-45" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="781" y="913" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="Lvz0KHMB1awMAYVr9GuM-69" value="" style="curved=1;startArrow=none;endArrow=block;exitX=0.5;exitY=1;entryX=0.5;entryY=0;rounded=0;" parent="1" source="Lvz0KHMB1awMAYVr9GuM-47" target="Lvz0KHMB1awMAYVr9GuM-48" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Lvz0KHMB1awMAYVr9GuM-70" value="" style="curved=1;startArrow=none;endArrow=block;exitX=0.66;exitY=1;entryX=0.5;entryY=0;rounded=0;entryDx=0;entryDy=0;" parent="1" source="Lvz0KHMB1awMAYVr9GuM-38" target="Lvz0KHMB1awMAYVr9GuM-49" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1321" y="965" />
              <mxPoint x="1284" y="1270" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="Lvz0KHMB1awMAYVr9GuM-71" value="" style="curved=1;startArrow=none;endArrow=block;exitX=0.5;exitY=1;entryX=0.5;entryY=0;rounded=0;" parent="1" source="Lvz0KHMB1awMAYVr9GuM-48" target="Lvz0KHMB1awMAYVr9GuM-50" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points" />
          </mxGeometry>
        </mxCell>
        <mxCell id="kOM-1A1dfKhWAhorgb91-1" value="日志服务" style="whiteSpace=wrap;strokeWidth=2;verticalAlign=middle;fontStyle=1;fontSize=25;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="1282" y="1512" width="252" height="135" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
