﻿# AI 视觉应用 - 后端 (Django)

本项目使用 Django 框架构建 AI 视觉应用的后端服务，提供模型管理和多种 AI 视觉推理功能的 RESTful API。

## 技术栈

* **Web 框架**: Django 5.2.1
* **API 框架**: Django REST Framework
* **数据库**: SQLite (开发环境)
* **AI 推理引擎**:
  * Ultralytics YOLO 8.3.0 (目标检测)
  * PaddlePaddle 3.0.0 (OCR 识别)
  * ONNX Runtime (AI 图像修复)
* **图像处理**: OpenCV 4.11.0.86
* **跨域支持**: django-cors-headers

## 项目结构

```
Backend_Django/
├── backend_project/         # Django 项目配置目录
│   ├── __init__.py
│   ├── asgi.py              # ASGI 配置入口
│   ├── settings.py          # Django 项目核心设置 (数据库, 中间件, 静态/媒体文件, CORS等)
│   ├── urls.py              # 项目级 URL 路由配置 (主要包含 vision_app 的路由)
│   └── wsgi.py              # WSGI 配置入口
├── vision_app/              # 核心AI视觉功能的应用
│   ├── migrations/          # 数据库迁移文件 (定义 AIModel 模型的演变)
│   ├── management/          # 自定义 Django 管理命令
│   │   └── commands/
│   │       ├── clean_db_models.py # 清理数据库中与文件系统不同步的模型记录
│   │       └── list_db_models.py  # 列出数据库中所有AI模型
│   ├── __init__.py
│   ├── admin.py             # Django Admin 配置
│   ├── ai_restored_onnxruntime_predictor.py # AI图像修复ONNX推理封装
│   ├── apps.py              # 应用配置
│   ├── models.py            # 数据模型定义 (核心为 AIModel)
│   ├── ocr_paddle_predictor.py # PaddleOCR 推理封装
│   ├── serializers.py       # DRF 序列化器 (AIModelSerializer, AIModelUploadSerializer)
│   ├── tests.py             # 测试文件
│   ├── urls.py              # vision_app 的 URL 路由配置 (模型列表, 模型上传, 条码检测等)
│   └── views.py             # 视图逻辑 (API请求处理, YOLO推理等)
├── libs/                    # 第三方库和自定义模块
│   ├── proj_ai_roi_det/     # 特定版本的 ultralytics 库 (目标检测)
│   └── proj_ai_ocr_license_plate/ # PaddleOCR 相关模块 (OCR 识别)
├── models/                  # 存放AI模型文件的根目录
│   ├── custom_models/       # 用户上传的自定义模型 (由 MEDIA_ROOT 指向)
│   │   ├── barcode/         # 用户上传的条码检测模型
│   │   └── ocr/             # 用户上传的OCR模型
│   └── system_models/       # 系统内置模型 (由 SYSTEM_MODELS_ROOT 指向)
│       ├── barcode/
│       │   └── AI_ROI_Dete_NCHW_1x1x320x320_V1.2.3.3.pt # 系统条码模型
│       ├── ocr/
│       │   └── ... # OCR相关模型
│       └── ai_restored/
│           └── AI_Restorer_NCHW_1x1x256x256_V1.0.1.1.onnx # 系统AI图像修复模型
├── scripts/                 # 测试和辅助脚本
│   ├── test_ocr_predictor.py # OCR预测器测试脚本
│   └── ai_restored_onnx_predictor_test.py # AI图像修复测试脚本
├── .gitignore
├── db.sqlite3               # SQLite 数据库文件
├── manage.py                # Django 命令行工具
├── README.md                # 本文档
└── requirements.txt         # Python 依赖列表
```

## 核心功能

### 1. 模型管理系统

后端实现了一个完整的 AI 模型管理系统，支持系统内置模型和用户自定义模型：

* **数据模型**: `AIModel` 类定义了模型的基本属性
  ```python
  class AIModel(models.Model):
      name = models.CharField(max_length=255)
      description = models.TextField(blank=True, null=True)
      model_file = models.FileField(upload_to=get_model_upload_path, max_length=255)
      version = models.CharField(max_length=50, blank=True, null=True)
      model_type = models.CharField(max_length=50)
      ocr_role = models.CharField(max_length=20, choices=OCR_ROLE_CHOICES, blank=True, null=True)
      ocr_collection_name = models.CharField(max_length=255, blank=True, null=True)
      is_system_model = models.BooleanField(default=False)
      uploaded_at = models.DateTimeField(default=timezone.now)
  ```

* **模型列表 API**: 支持按类型分组和按范围筛选
  * 端点: `GET /api/vision/models/`
  * 查询参数: `model_scope` (`system`, `custom`, `all`), `model_type_filter`
  * 返回格式: 按 `model_type` 分组的模型列表
  * OCR模型特殊处理: 检测+识别模型配对管理

* **模型上传 API**: 支持用户上传自定义模型
  * 端点: `POST /api/vision/models/upload/`
  * 参数: `model_file`, `name`, `model_type`, `version`, `description`, `ocr_role`, `is_system_model`, `ocr_collection_name`
  * 验证: 文件格式、模型类型、名称唯一性、OCR角色验证等
  * 存储: 根据模型类别自动选择存储目录

* **模型删除 API**: 支持批量删除模型（管理员权限）
  * 端点: `POST /api/vision/models/delete/`
  * 参数: `model_ids` (数组)
  * 权限: 需要管理员权限验证

* **模型更新 API**: 支持模型信息编辑（管理员权限）
  * 端点: `PUT /api/vision/models/<model_id>/update/`
  * 参数: 模型基本信息和OCR配置
  * 权限: 需要管理员权限验证

### 2. 条码检测功能

基于 Ultralytics YOLO 实现的条码检测功能：

* **检测 API**:
  * 端点: `POST /api/vision/detect/barcode/ultralytics/`
  * 主要参数:
    * `image`: 待检测图像文件
    * `model_name`: 使用已定义模型 (与 `model_file` 二选一)
    * `model_file`: 临时上传模型文件 (与 `model_name` 二选一)
    * `confidence_threshold`: 置信度阈值 (0.0-1.0)
    * `preprocessing_method`: 预处理方法 (`full_scale` 或 `roi_area`)
  * 返回格式:
    ```json
    {
      "detections": [
        {
          "box": [x1, y1, x2, y2],
          "confidence": 0.95,
          "class_id": 0,
          "class_name": "barcode"
        },
        ...
      ]
    }
    ```

* **处理流程**:
  1. 接收图像和参数
  2. 根据 `model_name` 或 `model_file` 加载模型
  3. 根据 `preprocessing_method` 处理图像
  4. 执行推理并返回结果

### 3. OCR 识别功能

基于 PaddleOCR 实现的文字识别功能：

* **OCR API**:
  * 端点: `POST /api/vision/detect/ocr/paddle/`
  * 主要参数:
    * `image`: 待识别图像文件
    * `ocr_task_name`: OCR 任务名称 (如 `license_plate_cn`, `id_card_en`, `general_text_mobile_ch_en`)
    * `use_gpu`: 是否使用 GPU (布尔值，可选)
  * 返回格式:
    ```json
    {
      "results": [
        {
          "text": "识别的文字",
          "confidence": 0.98,
          "box": [[x1, y1], [x2, y2], [x3, y3], [x4, y4]]
        },
        ...
      ],
      "task_name": "license_plate_cn",
      "processing_time": 1.23
    }
    ```

* **OCR 任务列表 API**:
  * 端点: `GET /api/vision/ocr-tasks/`
  * 返回可用的OCR任务配置列表
  * 基于检测+识别模型配对的任务管理

* **OCR 配置**:
  * 在 `settings.py` 中定义了不同 OCR 任务的配置参数
  * 支持多种语言和场景: 车牌识别、身份证识别、通用文字识别
  * 模型配对管理: 每个任务包含检测模型和识别模型

### 4. AI 图像修复功能

基于 ONNX Runtime 实现的图像修复功能：

* **AI 图像修复 API**:
  * 端点: `POST /api/vision/restore/image/`
  * 主要参数:
    * `image`: 待修复图像文件
    * `model_name`: 修复模型名称 (可选，默认使用最新版本)
    * `output_format`: 输出图像格式 (`PNG` 或 `JPEG`，默认为 `PNG`)
  * 返回格式:
    ```json
    {
      "status": "success",
      "message": "图像修复成功",
      "original_size": [width, height],
      "restored_size": [width, height],
      "image_base64": "base64编码的修复后图像",
      "model_used": "AI_Restorer_V1.0.1.7"
    }
    ```

* **修复流程**:
  1. 接收图像并预处理（灰度转换、尺寸调整、归一化）
  2. 根据模型名称加载对应的ONNX模型
  3. 使用 ONNX Runtime 执行推理
  4. 对推理结果进行后处理（反归一化、转换为图像格式）
  5. 返回 Base64 编码的修复后图像

* **模型版本管理**:
  * 支持多版本AI修复模型 (如 V1.0.1.1, V1.0.1.7)
  * 自动选择最新版本或指定版本模型

### 5. 文件存储系统

* **系统模型存储**:
  * 路径: `models/system_models/<model_type>/`
  * 配置: `SYSTEM_MODELS_ROOT` 在 `settings.py` 中定义

* **用户模型存储**:
  * 路径: `models/custom_models/<model_type>/`
  * 配置: `MEDIA_ROOT` 在 `settings.py` 中定义
  * 上传处理: 通过 `get_model_upload_path` 函数动态确定存储路径

### 6. 示例图片管理系统

提供完整的示例图片管理功能，为用户提供测试和演示用的图像资源：

* **示例图片列表 API**:
  * 端点: `GET /api/vision/example-images/`
  * 返回格式: 按功能分类的图片列表 (barcode, ocr, ai_restored)
  * 图片信息: 包含文件名、描述、URL、尺寸、文件大小等

* **示例图片服务 API**:
  * 端点: `GET /api/vision/example-images/<category>/<filename>`
  * 功能: 提供示例图片文件的HTTP服务
  * 支持格式: JPG、PNG、BMP、WEBP、AVIF

* **示例图片上传 API** (管理员权限):
  * 端点: `POST /api/vision/example-images/upload/`
  * 参数: `file`, `category`, `name` (可选), `description` (可选)
  * 权限: 需要管理员权限验证
  * 验证: 文件格式、分类有效性、文件完整性

* **示例图片删除 API** (管理员权限):
  * 端点: `POST /api/vision/example-images/delete/`
  * 参数: `category`, `filenames` (数组)
  * 权限: 需要管理员权限验证
  * 功能: 支持批量删除操作

### 7. 管理员权限系统

实现了完整的管理员身份认证和权限控制系统：

* **管理员登录 API**:
  * 端点: `POST /api/vision/admin/login/`
  * 参数: `password` (固定密码: "mindeo")
  * 功能: 基于Django会话的身份验证
  * 安全: 会话状态管理和超时控制

* **管理员登出 API**:
  * 端点: `POST /api/vision/admin/logout/`
  * 功能: 清除会话中的管理员状态
  * 安全: 完全清理认证信息

* **管理员状态检查 API**:
  * 端点: `GET /api/vision/admin/status/`
  * 返回: 当前管理员登录状态和登录时间
  * 功能: 前端权限验证和状态同步

* **权限验证装饰器**:
  * 装饰器: `@require_admin_auth`
  * 功能: 保护需要管理员权限的API端点
  * 验证: 检查会话中的管理员标识

### 8. 管理命令

提供了自定义 Django 管理命令，方便系统维护：

* **列出模型**: `python manage.py list_db_models`
  * 显示数据库中所有 AI 模型记录

* **清理模型**: `python manage.py clean_db_models`
  * 同步数据库记录与文件系统
  * 删除文件已不存在的无效记录

## API 端点详情

### 核心功能 API

| 端点 | 方法 | 描述 | 参数 | 权限 |
|------|------|------|------|------|
| `/api/vision/models/` | GET | 获取模型列表 | `model_scope`, `model_type_filter` | 公开 |
| `/api/vision/models/upload/` | POST | 上传自定义模型 | `model_file`, `name`, `model_type`, `version`, ... | 公开 |
| `/api/vision/ocr-tasks/` | GET | 获取可用OCR任务列表 | 无 | 公开 |
| `/api/vision/detect/barcode/ultralytics/` | POST | 条码检测 | `image`, `model_name`/`model_file`, `confidence_threshold`, ... | 公开 |
| `/api/vision/detect/ocr/paddle/` | POST | OCR 文字识别 | `image`, `ocr_task_name`, `use_gpu` | 公开 |
| `/api/vision/restore/image/` | POST | AI 图像修复 | `image`, `model_name`, `output_format` | 公开 |

### 示例图片管理 API

| 端点 | 方法 | 描述 | 参数 | 权限 |
|------|------|------|------|------|
| `/api/vision/example-images/` | GET | 获取示例图片列表 | 无 | 公开 |
| `/api/vision/example-images/<category>/<filename>` | GET | 获取示例图片文件 | `category`, `filename` | 公开 |
| `/api/vision/example-images/upload/` | POST | 上传示例图片 | `file`, `category`, `name`, `description` | 管理员 |
| `/api/vision/example-images/delete/` | POST | 批量删除示例图片 | `category`, `filenames` | 管理员 |

### 管理员权限 API

| 端点 | 方法 | 描述 | 参数 | 权限 |
|------|------|------|------|------|
| `/api/vision/admin/login/` | POST | 管理员登录 | `password` | 公开 |
| `/api/vision/admin/logout/` | POST | 管理员登出 | 无 | 公开 |
| `/api/vision/admin/status/` | GET | 检查管理员状态 | 无 | 公开 |
| `/api/vision/models/delete/` | POST | 批量删除模型 | `model_ids` | 管理员 |
| `/api/vision/models/<model_id>/update/` | PUT | 更新模型信息 | 模型基本信息 | 管理员 |

## 安装与配置

### 依赖安装

1. **创建并激活 Python 虚拟环境**:
   ```bash
   python -m venv venv
   # Windows:
   .\venv\Scripts\activate
   # Linux/macOS:
   source venv/bin/activate
   ```

2. **安装依赖**:
   ```bash
   pip install -r requirements.txt
   ```

   **注意**:
   * 本项目使用特定版本的 `ultralytics` 库，位于 `libs/proj_ai_roi_det/`。`requirements.txt` 已配置为以可编辑模式安装此版本。
   * 项目依赖 `onnxruntime` 用于AI图像修复功能，确保已正确安装。

### 数据库配置

1. **执行数据库迁移**:
   ```bash
   python manage.py makemigrations vision_app
   python manage.py migrate
   ```

2. **初始化系统模型**:
   数据库迁移会自动创建初始系统模型记录。如需手动检查:
   ```bash
   python manage.py list_db_models
   ```

### 运行服务器

```bash
python manage.py runserver 0.0.0.0:8000
```

服务将在 `http://0.0.0.0:8000/` 上运行，可通过 `http://<服务器IP>:8000/api/vision/models/` 等端点访问。

## 配置说明

### 重要配置项

#### 文件存储配置
```python
# settings.py
MEDIA_ROOT = BASE_DIR / 'models' / 'custom_models'  # 用户模型存储
SYSTEM_MODELS_ROOT = BASE_DIR / 'models' / 'system_models'  # 系统模型存储
EXAMPLE_IMAGES_ROOT = BASE_DIR / 'models' / 'example_images'  # 示例图片存储
```

#### CORS 配置
```python
# settings.py
CORS_ALLOWED_ORIGINS = [
    "http://localhost:5173",
    "http://127.0.0.1:5173",
    "http://*************:5173",
]
CORS_ALLOW_CREDENTIALS = True
```

#### 会话配置
```python
# settings.py
SESSION_COOKIE_AGE = 86400  # 24小时
SESSION_SAVE_EVERY_REQUEST = True
SESSION_EXPIRE_AT_BROWSER_CLOSE = False
SESSION_COOKIE_SAMESITE = "Lax"
SESSION_COOKIE_SECURE = False  # 开发环境用False，生产环境用True
```

#### OCR 任务配置
```python
# settings.py
OCR_TASK_CONFIGS = {
    'license_plate_cn': {
        'use_angle_cls': False,
        'det_algorithm': 'DB',
        'rec_algorithm': 'CRNN',
        'lang': 'ch',
        'use_gpu': False,
        # ... 更多配置
    },
    # ... 其他任务配置
}
```

### 管理员配置

#### 默认管理员密码
- **密码**: `mindeo`
- **修改位置**: `vision_app/views.py` 中的 `admin_login` 函数
- **安全建议**: 生产环境中应修改为更安全的密码

#### 权限验证
- **装饰器**: `@require_admin_auth`
- **会话键**: `is_admin`, `admin_login_time`
- **验证逻辑**: 检查Django会话中的管理员标识

## 已完成功能

### ✅ 核心AI功能
- **条码检测**: 基于YOLO的高精度条码检测，支持多种预处理方法
- **OCR识别**: 基于PaddleOCR的多场景文字识别，支持车牌、身份证、通用文字
- **AI图像修复**: 基于ONNX Runtime的图像修复，支持多版本模型

### ✅ 模型管理系统
- **模型分类管理**: 系统模型和用户模型分离存储
- **OCR模型配对**: 检测+识别模型集合管理
- **模型CRUD操作**: 完整的增删改查功能
- **文件存储管理**: 自动化的文件路径管理和存储

### ✅ 示例图片系统
- **分类管理**: 按AI功能分类的图片库
- **批量操作**: 支持批量上传和删除
- **文件服务**: HTTP文件服务和预览功能

### ✅ 管理员系统
- **身份认证**: 基于会话的安全认证机制
- **权限控制**: 多层级权限验证和API保护
- **管理界面**: 完整的后台管理功能

### ✅ 部署支持
- **Docker容器化**: 完整的Docker部署方案
- **环境配置**: 多环境配置支持
- **数据持久化**: 数据和模型文件持久化存储

## 下一步计划

1. **功能扩展**:
   * 实现语义分割功能
   * 添加目标跟踪功能
   * 支持更多AI视觉任务

2. **性能优化**:
   * 实现模型缓存机制，减少重复加载
   * 添加异步任务处理长时间运行的推理请求
   * 优化大图像处理性能

3. **安全性增强**:
   * 实现更复杂的用户认证和授权
   * 添加请求速率限制
   * 增强文件上传验证和安全检查

4. **监控和日志**:
   * 添加系统监控和性能指标
   * 实现详细的操作日志记录
   * 添加错误追踪和报警机制

## 故障排除

### 常见问题

1. **模型加载错误**:
   * 确保模型文件存在于正确路径
   * 检查模型格式是否兼容
   * 使用 `list_db_models` 和 `clean_db_models` 命令同步数据库记录

2. **依赖安装问题**:
   * 确保使用正确版本的 Python (推荐 3.10+)
   * 如遇 `ultralytics` 冲突，先卸载官方版本: `pip uninstall ultralytics`
   * 手动安装特定版本: `cd libs/proj_ai_roi_det && pip install -e .`

3. **CORS 错误**:
   * 检查 `settings.py` 中的 CORS 配置
   * 默认允许特定源，可根据需要调整

4. **管理员权限问题**:
   * 确保使用正确的管理员密码 ("mindeo")
   * 检查会话配置和Cookie设置
   * 验证前端的权限状态管理

5. **示例图片访问问题**:
   * 确保 `EXAMPLE_IMAGES_ROOT` 目录存在
   * 检查文件权限和路径配置
   * 验证图片文件格式支持

---

*本文档将随着项目进展持续更新。*