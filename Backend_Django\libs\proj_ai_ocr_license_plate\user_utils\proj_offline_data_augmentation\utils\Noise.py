
import numpy as np

class Perlin:
    offset = {} # 偏移量
    weight = {} # 权重
    def __init__(self, size=(240,420), T=60):
        "size:输出尺寸;T:周期,表示晶格尺寸"
        T,size= (T,T) if isinstance(T,int) else T, np.array(size)
        shape = size//T + (size%T).astype(bool)
        
        self.T     = T
        self.size  = np.s_[:size[0],:size[1]]
        self.shape = tuple(shape)
        self.angles= np.random.uniform(-np.pi, np.pi, (1,)+self.shape)
        
        if self.T not in Perlin.offset:
            # 插值法求晶格内各点权重
            y_x = np.indices(self.T)/np.array(self.T).reshape(2,1,1)
            y2_x2, y3_x3 = y_x**2, y_x**3
            x2_x3 = -2*y3_x3[1] + 3*y2_x2[1]
            y2_y3 = -2*y3_x3[0] + 3*y2_x2[0]
            
            weight00 =   x2_x3*y2_y3 - x2_x3 - y2_y3 + 1
            weight01 = - x2_x3*y2_y3 + x2_x3
            weight10 = - x2_x3*y2_y3 + y2_y3
            weight11 =   x2_x3*y2_y3
 
            Perlin.offset[self.T] = [-y_x,1-y_x] # arr00, arr11
            Perlin.weight[self.T] = [weight00,weight01,weight10,weight11]
 
    def __call__(self,roll=None,delta=None,amplitude=0.5,mean=None):
        "roll:角度变化范围;delta:画布偏移量;平均值默认为幅值"
        mean = amplitude if mean is None else mean
        arr00, arr11 = Perlin.offset[self.T]
        arr01, arr10 = arr00.copy(),arr00.copy()
        arr10[0], arr01[1] = arr11
        weight00,weight01,weight10,weight11 = Perlin.weight[self.T]
        if roll:
            self.angles += np.random.uniform(*roll, (1,)+self.shape)
 
        ang00 = np.concatenate((np.cos(self.angles),np.sin(self.angles)),0) *(amplitude/0.5**0.5)
        ang01 = np.concatenate((ang00[:,:,1:],ang00[:,:,:1]),2)
        ang10 = np.concatenate((ang00[:,1:,:],ang00[:,:1,:]),1)
        ang11 = np.concatenate((ang10[:,:,1:],ang10[:,:,:1]),2)
 
        ang00 = ang00.repeat(self.T[0],axis=1).repeat(self.T[1],axis=2)
        ang01 = ang01.repeat(self.T[0],axis=1).repeat(self.T[1],axis=2)
        ang10 = ang10.repeat(self.T[0],axis=1).repeat(self.T[1],axis=2)
        ang11 = ang11.repeat(self.T[0],axis=1).repeat(self.T[1],axis=2)
 
        arr00, weight00 = np.tile(arr00,(1,)+self.shape), np.tile(weight00,self.shape)
        arr01, weight01 = np.tile(arr01,(1,)+self.shape), np.tile(weight01,self.shape)
        arr10, weight10 = np.tile(arr10,(1,)+self.shape), np.tile(weight10,self.shape)
        arr11, weight11 = np.tile(arr11,(1,)+self.shape), np.tile(weight11,self.shape)
 
        dot00 = (arr00*ang00).sum(0)*weight00
        dot01 = (arr01*ang01).sum(0)*weight01
        dot10 = (arr10*ang10).sum(0)*weight10
        dot11 = (arr11*ang11).sum(0)*weight11
        img = dot00 + dot01 + dot10 + dot11
        if delta:
            dy, dx = np.array(delta)%img.shape
            img = np.concatenate((img[dy:,:],img[:dy,:]),0)
            img = np.concatenate((img[:,dx:],img[:,:dx]),1)
 
        return img[self.size] + mean