import json
import base64
import time
from channels.generic.websocket import AsyncWebsocketConsumer

class ScannerConsumer(AsyncWebsocketConsumer):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # WebSocket发送帧率监控
        self.websocket_frame_count = 0
        self.websocket_last_fps_time = time.time()
    
    async def connect(self):
        """处理WebSocket连接请求"""
        self.group_name = "scanner_stream"
        # 加入组
        await self.channel_layer.group_add(
            self.group_name,
            self.channel_name
        )
        await self.accept()
        print(f"WebSocket client connected: {self.channel_name}")
        # 重置发送计数器
        self.websocket_frame_count = 0
        self.websocket_last_fps_time = time.time()

    async def disconnect(self, close_code):
        """处理WebSocket断开连接"""
        # 离开组
        await self.channel_layer.group_discard(
            self.group_name,
            self.channel_name
        )
        print(f"WebSocket client disconnected: {self.channel_name}")

    async def receive(self, text_data):
        """接收来自WebSocket的消息（前端 -> 后端）"""
        # 在这个应用中，我们主要从后端推送到前端，所以这个方法可以很简单
        # 但可以保留用于未来的双向通信，例如前端发送控制命令
        pass

    async def send_image_data(self, event):
        """
        从Channel Layer接收图像数据并将其发送到WebSocket客户端
        这个方法由ScannerService通过channel_layer.group_send调用
        """
        image_bytes = event["image_bytes"]
        
        # WebSocket发送帧率统计
        self.websocket_frame_count += 1
        current_time = time.time()
        if current_time - self.websocket_last_fps_time >= 1.0:  # 每秒统计一次
            fps = self.websocket_frame_count / (current_time - self.websocket_last_fps_time)
            print(f"WebSocket Send FPS: {fps:.2f}")
            self.websocket_frame_count = 0
            self.websocket_last_fps_time = current_time
        
        # 将图像字节编码为Base64字符串，以便在JSON中传输
        image_base64 = base64.b64encode(image_bytes).decode('utf-8')
        
        # 发送数据到客户端
        await self.send(text_data=json.dumps({
            'image': image_base64,
            # 可以在这里添加其他数据，例如FPS
        }))