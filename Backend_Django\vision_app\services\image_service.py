"""
图像处理服务模块

专门处理图像相关的操作，包括：
- 图像文件验证和处理
- 图像格式转换和优化
- 图像HTTP响应服务
- 图像元数据提取
- 图像缓存和优化

完整的ImageService架构：

class ImageService(BaseService):
    # ==================== 图像验证和处理 ====================
    def validate_image_file()              # 图像文件验证
    def validate_image_format()            # 图像格式验证
    def get_image_info()                   # 获取图像元数据
    def optimize_image()                   # 图像优化处理
    
    # ==================== 图像HTTP服务 ====================
    def serve_image_response()             # 创建图像HTTP响应
    def get_image_mime_type()              # 获取图像MIME类型
    def create_cached_response()           # 创建带缓存的响应
    def handle_image_request()             # 处理图像请求
    
    # ==================== 图像格式转换 ====================
    def convert_image_format()             # 图像格式转换
    def resize_image()                     # 图像尺寸调整
    def compress_image()                   # 图像压缩
    
    # ==================== 图像安全处理 ====================
    def validate_image_path()              # 验证图像路径安全性
    def sanitize_filename()                # 清理文件名
    def check_path_traversal()             # 检查路径遍历攻击

"""

import os
import mimetypes
from typing import Dict, Any, Optional, Tuple, Union
from pathlib import Path
from PIL import Image as PILImage
from django.http import HttpResponse
from django.core.files.uploadedfile import UploadedFile
from django.conf import settings
from urllib.parse import unquote

from .base import BaseService
from .file_service import FileService


class ImageService(BaseService):
    """
    图像处理服务类
    
    专门处理图像相关的操作，包括验证、格式转换、HTTP服务等。
    """
    
    # 支持的图像格式
    SUPPORTED_IMAGE_FORMATS = {'.jpg', '.jpeg', '.png', '.bmp', '.webp', '.avif'}
    
    # 允许的示例图片分类
    ALLOWED_EXAMPLE_CATEGORIES = {'barcode', 'ocr', 'ai_restored'}
    
    # 默认缓存设置
    DEFAULT_CACHE_MAX_AGE = 3600  # 1小时
    
    def __init__(self):
        """初始化图像服务"""
        super().__init__()
        self.file_service = FileService()
    
    # ==================== 图像验证和处理 ====================
    
    def validate_image_file(self, file: UploadedFile) -> Tuple[bool, Optional[str]]:
        """
        验证图像文件（委托给FileService）

        Args:
            file: 上传的文件对象

        Returns:
            (是否有效, 错误消息)
        """
        return self.file_service.validate_file(file, 'image')
    
    def validate_image_format(self, file_path: str) -> Tuple[bool, Optional[str]]:
        """
        验证图像文件格式（委托给FileService）

        Args:
            file_path: 图像文件路径

        Returns:
            (是否有效, 错误消息)
        """
        return self.file_service.validate_file_path(file_path, 'image')
    
    def get_image_info(self, file_path: str) -> Dict[str, Any]:
        """
        获取图像文件信息
        
        Args:
            file_path: 图像文件路径
            
        Returns:
            图像信息字典
        """
        if not os.path.exists(file_path):
            return {}
        
        image_info = {
            'size': os.path.getsize(file_path),
            'name': os.path.basename(file_path),
            'path': file_path
        }
        
        # 获取图像尺寸和格式信息
        try:
            with PILImage.open(file_path) as img:
                image_info.update({
                    'width': img.width,
                    'height': img.height,
                    'format': img.format,
                    'mode': img.mode
                })
        except Exception as e:
            self.log_warning(f"Failed to get image info for {file_path}: {str(e)}")
        
        return image_info
    
    def optimize_image(self, image_path: str, quality: int = 85, 
                      max_width: Optional[int] = None, max_height: Optional[int] = None) -> str:
        """
        优化图像文件
        
        Args:
            image_path: 图像文件路径
            quality: 压缩质量 (1-100)
            max_width: 最大宽度
            max_height: 最大高度
            
        Returns:
            优化后的图像路径
        """
        try:
            with PILImage.open(image_path) as img:
                # 调整尺寸
                if max_width or max_height:
                    img.thumbnail((max_width or img.width, max_height or img.height), PILImage.Resampling.LANCZOS)
                
                # 保存优化后的图像
                optimized_path = image_path.replace('.', '_optimized.')
                img.save(optimized_path, optimize=True, quality=quality)
                
                self.log_info(f"Image optimized: {image_path} -> {optimized_path}")
                return optimized_path
                
        except Exception as e:
            self.log_error(f"Failed to optimize image {image_path}: {str(e)}")
            return image_path  # 返回原路径

    # ==================== 图像HTTP服务 ====================

    def serve_image_response(self, file_path: str, filename: Optional[str] = None,
                           cache_max_age: Optional[int] = None) -> HttpResponse:
        """
        创建图像HTTP响应

        Args:
            file_path: 图像文件路径
            filename: 响应文件名（可选）
            cache_max_age: 缓存时间（秒）

        Returns:
            HttpResponse对象

        Raises:
            FileNotFoundError: 文件不存在
            Exception: 读取文件失败
        """
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"图像文件不存在: {file_path}")

        # 验证图像格式
        is_valid, error_msg = self.validate_image_format(file_path)
        if not is_valid:
            raise ValueError(error_msg)

        try:
            # 读取文件内容
            with open(file_path, 'rb') as f:
                file_content = f.read()

            # 获取MIME类型
            content_type = self.get_image_mime_type(file_path)

            # 创建HTTP响应
            response = HttpResponse(file_content, content_type=content_type)

            # 设置文件名
            if filename is None:
                filename = os.path.basename(file_path)
            response['Content-Disposition'] = f'inline; filename="{filename}"'

            # 设置缓存头
            cache_age = cache_max_age or self.DEFAULT_CACHE_MAX_AGE
            response['Cache-Control'] = f'public, max-age={cache_age}'

            # 设置文件大小
            response['Content-Length'] = len(file_content)

            self.log_debug(f"Served image: {file_path} ({len(file_content)} bytes)")
            return response

        except Exception as e:
            self.log_error(f"Failed to serve image {file_path}: {str(e)}")
            raise Exception(f"读取图片文件失败: {str(e)}")

    def get_image_mime_type(self, file_path: str) -> str:
        """
        获取图像MIME类型（委托给FileService）

        Args:
            file_path: 图像文件路径

        Returns:
            MIME类型字符串
        """
        return self.file_service.get_file_mime_type(file_path)

    def create_cached_response(self, file_path: str, etag: Optional[str] = None) -> HttpResponse:
        """
        创建带缓存的图像响应

        Args:
            file_path: 图像文件路径
            etag: ETag值（可选）

        Returns:
            HttpResponse对象
        """
        response = self.serve_image_response(file_path)

        # 设置ETag
        if etag:
            response['ETag'] = etag

        # 设置Last-Modified
        if os.path.exists(file_path):
            mtime = os.path.getmtime(file_path)
            from django.utils.http import http_date
            response['Last-Modified'] = http_date(mtime)

        return response

    def handle_image_request(self, category: str, filename: str,
                           base_dir: Optional[str] = None) -> HttpResponse:
        """
        处理图像请求的完整流程

        Args:
            category: 图像分类
            filename: 文件名
            base_dir: 基础目录（可选）

        Returns:
            HttpResponse对象

        Raises:
            ValueError: 参数无效
            FileNotFoundError: 文件不存在
        """
        # 验证分类
        if category not in self.ALLOWED_EXAMPLE_CATEGORIES:
            raise ValueError(f"无效的图片分类: {category}")

        # URL解码文件名
        decoded_filename = unquote(filename)
        self.log_info(f"Handling image request: {category}/{decoded_filename}")

        # 构建文件路径
        if base_dir is None:
            base_dir = getattr(settings, 'EXAMPLE_IMAGES_ROOT',
                             os.path.join(settings.BASE_DIR, 'models', 'example_images'))

        file_path = os.path.join(base_dir, category, decoded_filename)

        # 安全检查
        if not self.validate_image_path(file_path, base_dir):
            raise ValueError("无效的文件路径")

        # 创建响应
        return self.serve_image_response(file_path, decoded_filename)

    # ==================== 图像格式转换 ====================

    def convert_image_format(self, input_path: str, output_format: str,
                           output_path: Optional[str] = None, quality: int = 95) -> str:
        """
        转换图像格式

        Args:
            input_path: 输入图像路径
            output_format: 输出格式 ('PNG', 'JPEG', 'WEBP' 等)
            output_path: 输出路径（可选）
            quality: 压缩质量 (1-100)

        Returns:
            输出文件路径
        """
        try:
            with PILImage.open(input_path) as img:
                # 处理RGBA模式到JPEG的转换
                if output_format.upper() == 'JPEG' and img.mode in ('RGBA', 'LA'):
                    # 创建白色背景
                    background = PILImage.new('RGB', img.size, (255, 255, 255))
                    background.paste(img, mask=img.split()[-1] if img.mode == 'RGBA' else None)
                    img = background

                # 生成输出路径
                if output_path is None:
                    base_name = os.path.splitext(input_path)[0]
                    output_path = f"{base_name}.{output_format.lower()}"

                # 保存转换后的图像
                save_kwargs = {'format': output_format.upper()}
                if output_format.upper() in ('JPEG', 'WEBP'):
                    save_kwargs['quality'] = quality
                    save_kwargs['optimize'] = True

                img.save(output_path, **save_kwargs)

                self.log_info(f"Image converted: {input_path} -> {output_path} ({output_format})")
                return output_path

        except Exception as e:
            self.log_error(f"Failed to convert image format: {str(e)}")
            raise Exception(f"图像格式转换失败: {str(e)}")

    def resize_image(self, input_path: str, width: int, height: int,
                    output_path: Optional[str] = None, maintain_aspect: bool = True) -> str:
        """
        调整图像尺寸

        Args:
            input_path: 输入图像路径
            width: 目标宽度
            height: 目标高度
            output_path: 输出路径（可选）
            maintain_aspect: 是否保持宽高比

        Returns:
            输出文件路径
        """
        try:
            with PILImage.open(input_path) as img:
                if maintain_aspect:
                    img.thumbnail((width, height), PILImage.Resampling.LANCZOS)
                else:
                    img = img.resize((width, height), PILImage.Resampling.LANCZOS)

                # 生成输出路径
                if output_path is None:
                    base_name, ext = os.path.splitext(input_path)
                    output_path = f"{base_name}_resized_{width}x{height}{ext}"

                img.save(output_path)

                self.log_info(f"Image resized: {input_path} -> {output_path} ({width}x{height})")
                return output_path

        except Exception as e:
            self.log_error(f"Failed to resize image: {str(e)}")
            raise Exception(f"图像尺寸调整失败: {str(e)}")

    def compress_image(self, input_path: str, quality: int = 85,
                      output_path: Optional[str] = None) -> str:
        """
        压缩图像文件

        Args:
            input_path: 输入图像路径
            quality: 压缩质量 (1-100)
            output_path: 输出路径（可选）

        Returns:
            输出文件路径
        """
        try:
            with PILImage.open(input_path) as img:
                # 生成输出路径
                if output_path is None:
                    base_name, ext = os.path.splitext(input_path)
                    output_path = f"{base_name}_compressed{ext}"

                # 压缩保存
                img.save(output_path, optimize=True, quality=quality)

                # 记录压缩效果
                original_size = os.path.getsize(input_path)
                compressed_size = os.path.getsize(output_path)
                compression_ratio = (1 - compressed_size / original_size) * 100

                self.log_info(f"Image compressed: {input_path} -> {output_path} "
                            f"({original_size} -> {compressed_size} bytes, "
                            f"{compression_ratio:.1f}% reduction)")

                return output_path

        except Exception as e:
            self.log_error(f"Failed to compress image: {str(e)}")
            raise Exception(f"图像压缩失败: {str(e)}")

    # ==================== 图像安全处理 ====================

    def validate_image_path(self, file_path: str, base_dir: str) -> bool:
        """
        验证图像路径安全性（委托给FileService）

        Args:
            file_path: 要验证的文件路径
            base_dir: 基础目录

        Returns:
            是否安全
        """
        return self.file_service.validate_path_security(file_path, base_dir)

    def sanitize_filename(self, filename: str) -> str:
        """
        清理文件名（委托给FileService）

        Args:
            filename: 原始文件名

        Returns:
            清理后的文件名
        """
        return self.file_service.sanitize_filename(filename)

    def check_path_traversal(self, path: str) -> bool:
        """
        检查路径是否包含路径遍历攻击模式（委托给FileService）

        Args:
            path: 要检查的路径

        Returns:
            是否包含攻击模式
        """
        return self.file_service.check_path_traversal(path)

    # ==================== 辅助方法 ====================

    def get_supported_formats(self) -> set:
        """
        获取支持的图像格式列表

        Returns:
            支持的格式集合
        """
        return self.SUPPORTED_IMAGE_FORMATS.copy()

    def is_image_file(self, filename: str) -> bool:
        """
        检查文件是否为支持的图像格式（委托给FileService）

        Args:
            filename: 文件名

        Returns:
            是否为图像文件
        """
        return self.file_service.is_image_file(filename)

    def get_image_dimensions(self, file_path: str) -> Tuple[int, int]:
        """
        获取图像尺寸

        Args:
            file_path: 图像文件路径

        Returns:
            (宽度, 高度)
        """
        try:
            with PILImage.open(file_path) as img:
                return img.size
        except Exception as e:
            self.log_error(f"Failed to get image dimensions: {str(e)}")
            return (0, 0)
