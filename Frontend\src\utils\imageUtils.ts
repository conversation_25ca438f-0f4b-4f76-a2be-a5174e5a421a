/**
 * 根据提供的 ROI 坐标裁剪图像。
 * @param imageUrl 原始图像的 URL。
 * @param roi 相对于原始图像的 ROI 坐标 { x, y, width, height }。
 * @returns Promise<string>，resolve 裁剪后图像的 Base64 Data URL。
 */
export const cropImage = (
  imageUrl: string,
  roi: { x: number; y: number; width: number; height: number }
): Promise<string> => {
  return new Promise((resolve, reject) => {
    const image = new Image();
    image.crossOrigin = "anonymous"; // 处理跨域问题，如果图像来自不同源
    image.src = imageUrl;

    image.onload = () => {
      if (roi.width === 0 || roi.height === 0) {
        // ROI 区域无效，可能导致 canvas 错误
        console.warn("cropImage: ROI width or height is 0. Skipping crop.");
        // 根据需求，这里可以 reject 或 resolve 一个空字符串/原始图像 URL
        // 为避免后续流程中断，暂时 resolve 原始图像 URL，但理想情况是更好的错误处理
        resolve(imageUrl); 
        return;
      }

      const canvas = document.createElement('canvas');
      canvas.width = roi.width;
      canvas.height = roi.height;
      const ctx = canvas.getContext('2d');

      if (!ctx) {
        reject(new Error('Failed to get canvas context'));
        return;
      }

      // sx, sy, sWidth, sHeight, dx, dy, dWidth, dHeight
      // 从原始图像的 (roi.x, roi.y) 点开始，截取 roi.width 和 roi.height 大小的区域，
      // 然后绘制到 canvas 的 (0,0) 点，宽高为 roi.width 和 roi.height。
      ctx.drawImage(
        image,
        roi.x,
        roi.y,
        roi.width,
        roi.height,
        0,
        0,
        roi.width,
        roi.height
      );

      try {
        const croppedDataUrl = canvas.toDataURL('image/png'); // 或者 'image/jpeg'
        resolve(croppedDataUrl);
      } catch (error) {
        console.error("Error converting canvas to Data URL:", error);
        // 对于 "tainted canvas" 错误，这通常意味着图像来自不允许跨域操作的源
        // 并且没有正确设置 crossOrigin 或服务器没有发送正确的 CORS 头。
        reject(new Error('Failed to convert canvas to Data URL. Possible CORS issue.'));
      }
    };

    image.onerror = (error) => {
      console.error("Error loading image for cropping:", error);
      reject(new Error('Failed to load image for cropping.'));
    };
  });
};


/**
 * 将 Base64 Data URL 转换为 Blob 对象。
 * @param dataurl Base64 Data URL 字符串。
 * @returns Blob 对象。
 */
export const dataURLtoBlob = (dataurl: string): Blob => {
  const arr = dataurl.split(',');
  const mimeMatch = arr[0].match(/:(.*?);/);
  if (!mimeMatch) {
    throw new Error('Invalid data URL: MIME type not found');
  }
  const mime = mimeMatch[1];
  const bstr = atob(arr[arr.length - 1]); // 确保分割后取最后一个元素，以防 data URL 中包含逗号
  let n = bstr.length;
  const u8arr = new Uint8Array(n);
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }
  return new Blob([u8arr], { type: mime });
};


/**
 * 将 Base64 Data URL 转换为 File 对象。
 * @param dataurl Base64 Data URL 字符串。
 * @param filename 要创建的 File 对象的文件名。
 * @returns File 对象。
 */
export const dataURLtoFile = (dataurl: string, filename: string): File => {
  const arr = dataurl.split(',');
  const mimeMatch = arr[0].match(/:(.*?);/);
  if (!mimeMatch) {
    throw new Error('Invalid data URL: MIME type not found');
  }
  const mime = mimeMatch[1];
  const bstr = atob(arr[arr.length - 1]);
  let n = bstr.length;
  const u8arr = new Uint8Array(n);
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }
  return new File([u8arr], filename, { type: mime });
};