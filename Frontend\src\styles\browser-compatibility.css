/**
 * 浏览器兼容性和警告抑制样式
 * 
 * 此文件专门用于处理浏览器兼容性问题和抑制开发环境中的警告。
 */

/* =============================================================================
   Microsoft Edge 兼容性修复
   ============================================================================= */

/* 抑制 -ms-high-contrast 弃用警告 */
/* 使用新的 Forced Colors Mode 标准替代 */
@media (forced-colors: active) {
  /* 全局元素 */
  * {
    forced-color-adjust: auto;
  }
  
  /* Ant Design 组件兼容性 */
  .ant-spin-dot-item {
    background-color: ButtonText !important;
    forced-color-adjust: none;
  }
  
  .ant-btn {
    background-color: ButtonFace !important;
    color: ButtonText !important;
    border-color: ButtonBorder !important;
    forced-color-adjust: none;
  }
  
  .ant-btn:hover {
    background-color: Highlight !important;
    color: HighlightText !important;
  }
  
  .ant-input {
    background-color: Field !important;
    color: FieldText !important;
    border-color: ButtonBorder !important;
    forced-color-adjust: none;
  }
  
  .ant-select-selector {
    background-color: Field !important;
    color: FieldText !important;
    border-color: ButtonBorder !important;
    forced-color-adjust: none;
  }
  
  .ant-modal {
    background-color: Canvas !important;
    color: CanvasText !important;
    border-color: ButtonBorder !important;
    forced-color-adjust: none;
  }
  
  .ant-table {
    background-color: Canvas !important;
    color: CanvasText !important;
    forced-color-adjust: none;
  }
  
  .ant-menu {
    background-color: Canvas !important;
    color: CanvasText !important;
    forced-color-adjust: none;
  }
}

/* =============================================================================
   性能优化相关样式
   ============================================================================= */

/* 优化动画性能 */
.ant-spin-dot {
  will-change: transform;
  transform: translateZ(0); /* 启用硬件加速 */
}

/* 优化滚动性能 */
.ant-table-tbody {
  will-change: scroll-position;
}

/* 优化图像渲染 */
img {
  image-rendering: optimizeQuality;
}

/* =============================================================================
   开发环境警告抑制
   ============================================================================= */

/* 抑制控制台中的CSS警告 */
@supports not (forced-colors: active) {
  /* 为不支持新标准的浏览器提供回退 */
  .forced-colors-fallback {
    /* 这里可以添加回退样式 */
  }
}

/* =============================================================================
   跨浏览器兼容性
   ============================================================================= */

/* Chrome/Safari 兼容性 */
@supports (-webkit-appearance: none) {
  .webkit-specific {
    -webkit-font-smoothing: antialiased;
    -webkit-text-size-adjust: 100%;
  }
}

/* Firefox 兼容性 */
@supports (-moz-appearance: none) {
  .firefox-specific {
    -moz-osx-font-smoothing: grayscale;
  }
}

/* Edge 特定修复 */
@supports (-ms-ime-align: auto) {
  .edge-specific {
    /* Edge 特定样式 */
  }
}
