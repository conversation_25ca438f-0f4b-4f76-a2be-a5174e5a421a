# Generated by Django 5.2.1 on 2025-05-29 02:12

from django.db import migrations
from django.utils import timezone

def populate_pp_ocrv4_mobile_models(apps, schema_editor):
    """
    添加PP-OCRv4 mobile模型到数据库
    """
    AIModel = apps.get_model('vision_app', 'AIModel')
    db_alias = schema_editor.connection.alias

    # PP-OCRv4 mobile检测模型
    AIModel.objects.using(db_alias).update_or_create(
        name='general_ocr_mobile',
        model_type='ocr',
        is_system_model=True,
        ocr_role='detection',
        defaults={
            'description': 'PP-OCRv4 mobile detection model - 轻量级通用文字检测模型',
            'model_file': 'ocr/general_ocr_mobile/PP-OCRv4_mobile_det_inference',
            'version': '4.0.0',
            'uploaded_at': timezone.now()
        }
    )
    print("Added PP-OCRv4 mobile detection model to database.")

    # PP-OCRv4 mobile识别模型
    AIModel.objects.using(db_alias).update_or_create(
        name='general_ocr_mobile',
        model_type='ocr',
        is_system_model=True,
        ocr_role='recognition',
        defaults={
            'description': 'PP-OCRv4 mobile recognition model - 轻量级通用文字识别模型',
            'model_file': 'ocr/general_ocr_mobile/PP-OCRv4_mobile_rec_inference',
            'version': '4.0.0',
            'uploaded_at': timezone.now()
        }
    )
    print("Added PP-OCRv4 mobile recognition model to database.")

def remove_pp_ocrv4_mobile_models(apps, schema_editor):
    """
    移除PP-OCRv4 mobile模型（回滚操作）
    """
    AIModel = apps.get_model('vision_app', 'AIModel')
    db_alias = schema_editor.connection.alias
    
    try:
        # 删除检测模型
        det_model = AIModel.objects.using(db_alias).get(
            name='general_ocr_mobile',
            model_type='ocr',
            is_system_model=True,
            ocr_role='detection'
        )
        det_model.delete()
        print("Removed PP-OCRv4 mobile detection model from database.")
    except AIModel.DoesNotExist:
        print("PP-OCRv4 mobile detection model not found, no need to delete.")
    
    try:
        # 删除识别模型
        rec_model = AIModel.objects.using(db_alias).get(
            name='general_ocr_mobile',
            model_type='ocr',
            is_system_model=True,
            ocr_role='recognition'
        )
        rec_model.delete()
        print("Removed PP-OCRv4 mobile recognition model from database.")
    except AIModel.DoesNotExist:
        print("PP-OCRv4 mobile recognition model not found, no need to delete.")

class Migration(migrations.Migration):

    dependencies = [
        ('vision_app', '0011_auto_20250521_1516'),
    ]

    operations = [
        migrations.RunPython(populate_pp_ocrv4_mobile_models, remove_pp_ocrv4_mobile_models),
    ]
