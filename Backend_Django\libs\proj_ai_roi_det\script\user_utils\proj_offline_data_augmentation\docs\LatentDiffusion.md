<div align="center">
    <h1>LatentDiffusion</h1>
    LatentDiffusion模型训练离线增强脚本
</div>

# DatasetLatentDiffusion

## 方法预览

| 函数名                                                       | 功能                           |
| ------------------------------------------------------------ | ------------------------------ |
| [Perspective_Transform_And_Crop](#Perspective_Transform_And_Crop) | 对图像做透视变换并裁剪目标区域 |

## 方法说明

### Perspective_Transform_And_Crop

- 功能

对图像做透视变换并裁剪目标区域。

- 声明

```python
def Perspective_Transform_And_Crop(
        self,
        src_pathes: list[str],
        size: tuple[int],
        quantity: int,
        dst_path: str,
        resize: tuple[int] | None = None,
        save_label: str | None = None,
        max_transform_range: int = (100, 200),
        angle_range: float = (-180, 180),
        num_processes: int = 20,
        seed: int = 20241211,
        mode: str = "fix",
    ) -> str | None
```

- 返回值

生成数据位置，失败时返回None。

- 形参

| 参数名称            | 描述                                     |
| ------------------- | ---------------------------------------- |
| src_pathes          | 源文件路径列表                           |
| size                | 目标尺寸（H, W）                         |
| quantity            | 生成图片数量                             |
| dst_path            | 目标生成路径                             |
| resize              | resize尺寸（H, W），填写None时不做resize |
| save_label          | 将标签内容写入到指定文件中                |
| max_transform_range | 最大变换像素距离范围                     |
| angle_range         | 旋转范围（单位：度）                     |
| num_processes       | 同时执行的进程数                         |
| seed                | 随机种子，用于确保随机过程一致           |
| mode                | 裁剪模式，可选["fix", "dilate"]          |

- 示例

```python
data_list = list(Path("mindeo/data/MetalReflect").iterdir())
diffusion = DatasetLatentDiffusion(["2D_QR"])
diffusion.Perspective_Transform_And_Crop(data_list, (20, 20), 5, "mindeo/data_processed/Crop", resize=(320, 320), mode="dilate")
```

