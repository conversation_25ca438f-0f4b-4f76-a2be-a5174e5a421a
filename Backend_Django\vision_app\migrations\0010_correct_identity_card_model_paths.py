# Generated by Django 5.2.1 on 2025-05-21 05:57

from django.db import migrations


def correct_identity_card_model_file_paths(apps, schema_editor):
    AIModel = apps.get_model('vision_app', 'AIModel')
    db_alias = schema_editor.connection.alias

    # 任务名称就是希望保留的名称
    task_name = 'identity_card_number_cn'

    # 更新身份证检测模型记录的 model_file 路径
    try:
        # 查找名称为 task_name 的记录
        det_model = AIModel.objects.using(db_alias).get(
            name=task_name,
            model_type='ocr',
            ocr_role='detection',
            is_system_model=True
        )
        # 设置正确的 model_file 路径
        det_model.model_file.name = 'ocr/Identity_card_number_ch/Identity_card_det_model'
        det_model.save()
        # 更新打印信息
        print(f"Corrected model_file path for detection model '{task_name}' (ID: {det_model.id}) to '{det_model.model_file.name}'")
    except AIModel.DoesNotExist:
        print(f"Identity card detection model record with name '{task_name}' not found, cannot correct path.")
    except Exception as e:
        print(f"Error correcting identity card detection model path: {e}")


    # 更新身份证识别模型记录的 model_file 路径
    try:
        # 查找名称为 task_name 的记录
        rec_model = AIModel.objects.using(db_alias).get(
            name=task_name,
            model_type='ocr',
            ocr_role='recognition',
            is_system_model=True
        )
        # 设置正确的 model_file 路径
        rec_model.model_file.name = 'ocr/Identity_card_number_ch/Identity_card_rec_model'
        rec_model.save()
        # 更新打印信息
        print(f"Corrected model_file path for recognition model '{task_name}' (ID: {rec_model.id}) to '{rec_model.model_file.name}'")
    except AIModel.DoesNotExist:
        print(f"Identity card recognition model record with name '{task_name}' not found, cannot correct path.")
    except Exception as e:
        print(f"Error correcting identity card recognition model path: {e}")


class Migration(migrations.Migration):

    dependencies = [
        ("vision_app", "0009_populate_identity_card_models"),
    ]

    operations = [
        migrations.RunPython(correct_identity_card_model_file_paths),
    ]
