# AI 视觉应用 (web_ai_vision_app)

![项目标志](https://img.shields.io/badge/AI%20Vision-App-blue)
![前端](https://img.shields.io/badge/Frontend-React%20%7C%20TypeScript-61DAFB)
![后端](https://img.shields.io/badge/Backend-Django%20%7C%20Python-44B78B)
![状态](https://img.shields.io/badge/Status-Production%20Ready-green)
![AI修复](https://img.shields.io/badge/AI%20Restore-Available-brightgreen)
![Docker](https://img.shields.io/badge/Docker-Supported-2496ED)

本项目是一个功能强大的 AI 视觉应用程序，提供直观的网页界面进行图像处理、AI 模型管理和视觉分析。通过集成多种先进的 AI 视觉技术，为用户提供条码检测、OCR 文字识别等功能，并支持自定义模型上传和管理。

---

## 功能特点

### 已实现功能

- **图像管理与交互**
  - 支持多种格式图像加载 (JPG, PNG, BMP, WEBP, AVIF)
  - 图像平移、缩放和 ROI 区域选择
  - 图像历史记录和导出功能

- **条码检测**
  - 基于 YOLO 的高精度条码检测
  - 支持全图检测和 ROI 区域检测
  - 可视化显示检测结果（边界框、类别、置信度）

- **模型管理系统**
  - 系统内置模型和用户自定义模型管理
  - 模型上传、列表查询和分类功能
  - 按模型类型和范围筛选

- **OCR 文字识别**
  - 基于 PaddleOCR 的文字识别功能
  - 支持多种 OCR 任务类型
  - 结果可视化展示

- **AI 条码图像修复**
  - 基于 ONNX Runtime 的图像修复功能
  - 支持条码图像修复任务
  - 高质量图像修复效果

### 计划中功能

- **更多 AI 视觉功能**
  - 语义分割
  - 目标跟踪

---

## 技术架构

### 前端 (Frontend)

- **核心框架**: React 18.2.0, TypeScript 5.7.2
- **构建工具**: Vite 6.3.1
- **UI 库**: Ant Design 5.24.8
- **状态管理**: React Context API, TanStack React Query 5.74.11
- **HTTP 客户端**: Axios 1.9.0
- **特殊功能库**:
  - `react-zoom-pan-pinch` 3.7.0 (图像交互)
  - `allotment` 1.20.3 (可调整布局)

### 后端 (Backend_Django)

- **Web 框架**: Django 5.2.1, Django REST Framework
- **数据库**: SQLite (开发环境)
- **AI 推理引擎**:
  - Ultralytics YOLO 8.3.0 (目标检测)
  - PaddlePaddle 3.0.0 (OCR 识别)
  - ONNX Runtime 1.20.1 (AI图像修复)
- **图像处理**: OpenCV *********
- **跨域支持**: django-cors-headers

### 容器化部署 (Docker)

- **编排工具**: Docker Compose (多种配置)
- **前端容器**: Nginx + React 静态文件
- **后端容器**: Python + Django + AI 模型
- **部署模式**:
  - 标准生产部署 (`docker-compose.yml`)
  - 热重载开发部署 (`docker-compose.hotreload.yml`)
  - 简化测试部署 (`docker-compose.simple.yml`)
- **数据持久化**: 绑定挂载 (data/ 目录)
- **网络配置**: 桥接网络，支持局域网访问
- **镜像优化**:
  - 国内镜像源配置 (Docker Hub, PyPI, npm)
  - 多阶段构建减少镜像体积
  - 健康检查和自动重启
- **管理工具**: 完整的PowerShell脚本工具集

---

## 项目结构

```
web_ai_vision_app/
├── Backend_Django/          # 后端服务代码
│   ├── backend_project/     # Django 项目配置
│   ├── vision_app/          # 核心 AI 视觉功能应用
│   ├── libs/                # 第三方库和自定义模块
│   │   ├── proj_ai_roi_det/ # 目标检测模块
│   │   └── proj_ai_ocr_license_plate/ # OCR 识别模块
│   ├── models/              # AI 模型文件存储
│   │   ├── custom_models/   # 用户上传的自定义模型
│   │   └── system_models/   # 系统内置模型
│   ├── manage.py            # Django 项目管理脚本
│   └── README.md            # 后端详细文档
├── Frontend/                # 前端代码
│   ├── public/              # 静态资源
│   ├── src/                 # 源代码
│   │   ├── assets/          # 图片、图标等资源
│   │   ├── components/      # UI 组件
│   │   │   ├── ImageDisplay/  # 图像显示相关组件
│   │   │   └── parameterPanels/ # 功能参数面板
│   │   ├── contexts/        # React Context 状态管理
│   │   ├── pages/           # 页面组件
│   │   ├── services/        # API 服务
│   │   └── utils/           # 工具函数
│   ├── package.json         # 依赖配置
│   └── README.md            # 前端详细文档
├── docker/                  # Docker 容器化部署
│   ├── docker-compose.*.yml # 多种部署配置文件
│   ├── Dockerfile.frontend  # 前端镜像构建文件
│   ├── Dockerfile.backend   # 后端镜像构建文件
│   ├── nginx*.conf          # Nginx 配置文件
│   ├── pip.conf             # Python 包管理器配置
│   ├── daemon.json          # Docker 守护进程配置
│   ├── scripts/             # 完整的PowerShell脚本工具集
│   │   ├── build-images.ps1 # 构建Docker镜像
│   │   ├── deploy-hotreload.ps1 # 热重载服务管理
│   │   ├── start-container.ps1  # 标准容器启动
│   │   ├── export-images.ps1    # 镜像导出
│   │   ├── import-images.ps1    # 镜像导入
│   │   ├── package-for-server.ps1 # 服务器部署打包
│   │   ├── setup-code-sharing-simple.ps1 # 网络共享设置
│   │   ├── sync-from-dev.ps1 # 代码同步工具
│   │   ├── start-backend.sh  # 后端容器启动脚本
│   │   └── check/           # 诊断工具
│   ├── docs/                # 详细文档
│   ├── data/                # 数据持久化目录
│   └── README.md            # Docker 部署文档
├── data/                    # 数据持久化目录（Docker 挂载）
│   ├── db/                  # 数据库文件
│   ├── models/              # AI 模型文件
│   ├── media/               # 媒体文件
│   └── logs/                # 日志文件
├── start-docker.ps1         # 一键启动脚本（PowerShell）
├── start-docker.bat         # 一键启动脚本（批处理）
├── README.md                # 项目总体说明
└── version.md               # 版本更新日志
```

---

## 系统架构

![系统架构](./docs/pic/web_ai_vision_app架构图.png)

本应用采用前后端分离架构：

1. **前端**：负责用户界面和交互，通过 RESTful API 与后端通信
   - 三栏式布局：左侧功能树、中间图像显示区、右侧参数配置区
   - 基于 Context API 的状态管理
   - 组件化设计，提高代码复用性

2. **后端**：提供 RESTful API，处理 AI 模型管理和推理请求
   - 基于 Django REST Framework 的 API 设计
   - 模型管理系统，支持系统模型和用户自定义模型
   - AI 推理服务，支持多种视觉任务

3. **数据流**：
   - 用户上传图像 → 前端预处理 → 发送至后端 → AI 模型推理 → 返回结果 → 前端可视化展示

---

## 快速开始

### 部署方式选择

本项目支持两种部署方式：

1. **🐳 Docker 容器化部署（推荐）**：一键启动，环境隔离，适合生产环境
2. **💻 本地开发环境部署**：适合开发调试，可以实时修改代码

### 方式一：Docker 容器化部署（推荐）

#### 环境要求
- **Docker**: Docker Desktop 4.0+
- **系统**: Windows 10+, macOS 10.14+, Ubuntu 18.04+
- **内存**: 4GB+ 可用内存（推荐8GB+）
- **磁盘**: 20GB+ 可用空间（包含AI模型文件）

#### 快速启动（推荐）
```powershell
# 进入Docker目录
cd docker

# 构建镜像
.\scripts\build-images.ps1

# 启动热重载服务（开发推荐）
.\scripts\deploy-hotreload.ps1 -Mode start

# 或启动标准服务（生产推荐）
.\scripts\start-container.ps1
```

#### 传统启动方式
```bash
# 进入 Docker 目录
cd docker

# 构建镜像
docker-compose build

# 启动服务
docker-compose up -d
```

#### 访问地址
- **前端应用**: http://localhost:8080 或 http://你的IP:8080
- **后端API**: http://localhost:8000/api/vision/models/
- **局域网访问**: 自动检测并显示局域网IP地址

#### 部署模式选择
- **标准生产部署**: 稳定运行，适合生产环境
- **热重载开发部署**: 代码热重载，适合开发调试
- **简化测试部署**: 快速验证，适合功能测试

> 详细的Docker部署说明请参考：[Docker部署文档](./docker/README.md)

### 方式二：本地开发环境部署

#### 环境要求
- **前端**: Node.js (v18.x+), npm/yarn
- **后端**: Python (3.10+), pip

#### 后端设置

1. **克隆仓库并进入后端目录**:
   ```bash
   git clone https://github.com/yourusername/web_ai_vision_app.git
   cd web_ai_vision_app/Backend_Django
   ```

2. **创建并激活 Python 虚拟环境**:
   ```bash
   # Windows
   python -m venv venv
   .\venv\Scripts\activate

   # Linux/macOS
   python3 -m venv venv
   source venv/bin/activate
   ```

3. **安装依赖**:
   ```bash
   pip install -r requirements.txt
   ```

4. **执行数据库迁移**:
   ```bash
   python manage.py makemigrations vision_app
   python manage.py migrate
   ```

5. **运行开发服务器**:
   ```bash
   python manage.py runserver 0.0.0.0:8000
   ```
   服务将在 `http://localhost:8000/` 上运行

#### 前端设置

1. **进入前端目录**:
   ```bash
   cd ../Frontend
   ```

2. **安装依赖**:
   ```bash
   npm install
   # 或
   yarn install
   ```

3. **配置环境变量** (可选):
   - 复制 `.env.example` 为 `.env`
   - 设置 `VITE_API_BASE_URL` 指向后端地址

4. **启动开发服务器**:
   ```bash
   npm run dev
   # 或
   yarn dev
   ```
   前端将在 `http://localhost:5173` 上运行

---

## 使用指南

### 访问应用

**Docker 部署**：访问 `http://localhost:8080`
**本地开发**：访问 `http://localhost:5173`

### 操作流程

1. **加载图像**：
   - 点击顶部菜单栏的"打开文件"按钮
   - 或直接将图像拖放到中央区域

2. **选择 AI 功能**：
   - 从左侧功能树中选择所需功能（如"条码检测"）
   - 右侧面板将显示相应的参数配置选项

3. **执行 AI 处理**：
   - 配置参数（如选择模型、调整置信度阈值等）
   - 点击"开始检测"按钮
   - 结果将直接在图像上显示

4. **结果操作**：
   - 可以平移和缩放查看详细结果
   - 使用"另存为"功能保存带有检测结果的图像

### 功能特色

- **智能检测**：切换图片时自动执行AI推理
- **批量处理**：支持多图像批量处理
- **ROI选择**：支持感兴趣区域选择检测
- **结果导出**：支持检测结果和处理图像导出

---

## 开发指南

详细的开发指南请参考：
- [前端开发文档](./Frontend/README.md)
- [后端开发文档](./Backend_Django/README.md)
- [Docker部署文档](./docker/README.md)

---

## 部署选项

### 🐳 Docker 容器化部署
- **优势**: 环境隔离、一键部署、生产就绪
- **适用场景**: 生产环境、演示部署、快速体验
- **特点**:
  - 自动配置国内镜像源，提升构建速度
  - 数据持久化，支持模型和数据保存
  - 支持局域网访问，便于团队使用
  - 完整的服务管理和故障排除指南
  - 多种部署模式：标准、热重载、简化

### 🔥 Docker 热重载部署（推荐开发使用）
- **优势**: 快速更新、无需重构建、实时同步
- **适用场景**: 开发阶段、频繁代码变更、局域网部署
- **特点**:
  - ⚡ 前端变更需重新构建，后端变更5-15秒生效
  - 🔄 无需重启容器，支持Django自动重载
  - 📱 支持局域网多设备访问和测试
  - 🚀 支持跨电脑代码同步开发

**快速开始**:
```powershell
# 启动热重载开发环境
cd docker
.\scripts\deploy-hotreload.ps1 -Mode start

# 跨电脑同步开发
# A电脑: .\scripts\setup-code-sharing-simple.ps1 setup
# B电脑: .\scripts\sync-from-dev.ps1 -DevMachineIP "A电脑IP" -Mode watch
```

### 💻 本地开发环境
- **优势**: 实时调试、代码热重载、开发便利
- **适用场景**: 功能开发、代码调试、学习研究
- **特点**:
  - 前后端独立启动，便于调试
  - 支持代码实时修改和热重载
  - 完整的开发工具链支持

### 📱 Electron 桌面应用
- **状态**: 规划中
- **特点**: 跨平台桌面应用，离线使用

---

## 功能演示

### 条码检测 🔍 
- **功能**: 基于YOLO的高精度条码检测
- **支持格式**: QR码、条形码等多种格式
- **特点**: 实时检测、结果可视化

### OCR 文字识别 📄
- **功能**: 基于PaddleOCR的文字识别
- **支持语言**: 中文、英文等多种语言
- **特点**: 高精度识别、结构化输出

### AI 图像修复 ✨
- **功能**: 基于ONNX Runtime的图像修复
- **支持任务**: 条码图像复原（QR、DM）
- **特点**: 高质量修复效果、快速处理

---

## 技术支持 

### 文档资源
- [前端开发文档](./Frontend/README.md)
- [后端开发文档](./Backend_Django/README.md)
- [Docker部署文档](./docker/README.md)
- [Docker脚本指南](./docker/docs/scripts-guide.md)
- [故障排除指南](./docker/docs/troubleshooting.md)

### 获取帮助
如果遇到问题，请按以下顺序排查：
1. 查看相关文档
2. 运行诊断脚本：`.\docker\scripts\check\check-docker-env.ps1`
3. 检查日志：`docker-compose logs -f`
4. 重新构建：`.\docker\scripts\build-images.ps1 -NoCache`

---
