# Generated by Django 5.0.4 on 2025-05-09 03:20
# (The above generated line might be slightly different, that's okay)
# Replace YYYY-MM-DD HH:MM with the actual timestamp if you prefer, or leave as is.

from django.db import migrations

def create_initial_barcode_model(apps, schema_editor):
    """
    Creates an initial AIModel entry for the default barcode detection model.
    """
    AIModel = apps.get_model('vision_app', 'AIModel')
    db_alias = schema_editor.connection.alias

    # Define the properties of the barcode model
    model_name = 'AI_ROI_Dete_NCHW_1x1x320x320_V*******.pt'
    model_description = '通用条码检测模型 (Ultralytics YOLO based)'
    # IMPORTANT: This path must be relative to the 'system_models' directory
    # and must match the actual file path including subdirectories.
    model_file_path = 'barcode/AI_ROI_Dete_NCHW_1x1x320x320_V*******.pt'
    model_version = '*******'
    model_type = 'barcode' # Crucial for filtering

    # Check if a model with this name already exists to prevent duplicates if migration is run multiple times
    # or if it was manually added.
    # Using update_or_create to be idempotent.
    AIModel.objects.using(db_alias).update_or_create(
        name=model_name,
        defaults={
            'description': model_description,
            'model_file': model_file_path,
            'version': model_version,
            'model_type': model_type
        }
    )
    print(f"Ensured AIModel '{model_name}' (type: {model_type}) exists with model_file: '{model_file_path}'.")

def remove_initial_barcode_model(apps, schema_editor):
    """
    Removes the AIModel entry for the default barcode detection model.
    This is the reverse operation.
    """
    AIModel = apps.get_model('vision_app', 'AIModel')
    db_alias = schema_editor.connection.alias
    model_name = 'AI_ROI_Dete_NCHW_1x1x320x320_V*******.pt'
    
    try:
        model_instance = AIModel.objects.using(db_alias).get(name=model_name, model_type='barcode')
        model_instance.delete()
        print(f"Deleted AIModel '{model_name}' (type: barcode).")
    except AIModel.DoesNotExist:
        print(f"AIModel '{model_name}' (type: barcode) not found, no need to delete.")


class Migration(migrations.Migration):

    dependencies = [
        ('vision_app', '0002_delete_uploadedimage_remove_aimodel_is_active_and_more'), # Ensure this matches your previous migration
    ]

    operations = [
        migrations.RunPython(create_initial_barcode_model, remove_initial_barcode_model),
    ]
