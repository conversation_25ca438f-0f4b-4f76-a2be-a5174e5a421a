// Frontend/src/components/ImageDisplay/hooks/useMouseCoordinates.ts
import { useCallback, RefObject } from 'react';
import { ReactZoomPanPinchRef } from 'react-zoom-pan-pinch';
import { ImageInfo } from '../../../contexts/ImageWorkspaceContext'; // Assuming ImageInfo is exported

interface UseMouseCoordinatesProps {
  currentImageInfo: ImageInfo | null;
  setMouseImageCoordinates: (coords: { x: number; y: number } | null) => void;
  transformWrapperRef: RefObject<ReactZoomPanPinchRef | null>;
  imageContainerRef: RefObject<HTMLDivElement | null>;
}

export const useMouseCoordinates = ({
  currentImageInfo,
  setMouseImageCoordinates,
  transformWrapperRef,
  imageContainerRef,
}: UseMouseCoordinatesProps) => {
  const getCoordinatesRelativeToImage = useCallback(
    (event: React.MouseEvent<HTMLDivElement> | MouseEvent): { x: number; y: number } | null => {
      if (!currentImageInfo || !transformWrapperRef.current?.instance || !imageContainerRef.current) {
        return null;
      }

      const { instance } = transformWrapperRef.current;
      if (!instance.contentComponent) return null; // contentComponent is the direct child that is panned/zoomed

      const containerRect = imageContainerRef.current.getBoundingClientRect();
      const mouseXViewport = event.clientX;
      const mouseYViewport = event.clientY;

      const { scale, positionX: panX, positionY: panY } = instance.transformState;

      // The wrapperComponent is the direct parent of contentComponent, its rect is the viewport for pan/zoom
      const wrapperRect = instance.wrapperComponent
        ? instance.wrapperComponent.getBoundingClientRect()
        : containerRect; // Fallback, though wrapperComponent should exist

      const mouseRelativeToWrapperX = mouseXViewport - wrapperRect.left;
      const mouseRelativeToWrapperY = mouseYViewport - wrapperRect.top;

      // Coordinates relative to the original image
      let originalX = (mouseRelativeToWrapperX - panX) / scale;
      let originalY = (mouseRelativeToWrapperY - panY) / scale;

      // Clamp coordinates to be within the image boundaries
      originalX = Math.max(0, Math.min(originalX, currentImageInfo.width));
      originalY = Math.max(0, Math.min(originalY, currentImageInfo.height));

      return { x: originalX, y: originalY };
    },
    [currentImageInfo, transformWrapperRef, imageContainerRef]
  );

  const handleMouseMoveForCoords = useCallback(
    (event: React.MouseEvent<HTMLDivElement>) => {
      if (!currentImageInfo) {
        // If there's no image, ensure coordinates are cleared
        setMouseImageCoordinates(null);
        return;
      }
      const coords = getCoordinatesRelativeToImage(event);
      setMouseImageCoordinates(coords);
    },
    [currentImageInfo, getCoordinatesRelativeToImage, setMouseImageCoordinates]
  );

  const handleMouseLeaveForCoords = useCallback(() => {
    setMouseImageCoordinates(null);
  }, [setMouseImageCoordinates]);

  return {
    getCoordinatesRelativeToImage, // It might be useful for other hooks or components too
    handleMouseMoveForCoords,
    handleMouseLeaveForCoords,
  };
};