# 脚本功能详解

本文档详细介绍 `docker/scripts/` 目录下所有脚本的功能和使用方法。

---

## 🔨 **核心部署脚本**

### `build-images.ps1` - Docker 镜像构建
**功能**: 构建前端和后端 Docker 镜像，支持选择性构建和缓存控制

```powershell
# 构建所有镜像
.\scripts\build-images.ps1

# 选择性构建
.\scripts\build-images.ps1 -SkipBackend    # 只构建前端
.\scripts\build-images.ps1 -SkipFrontend   # 只构建后端

# 无缓存构建
.\scripts\build-images.ps1 -NoCache

# 查看构建结果
docker images | findstr web_ai_vision_app
```

### `deploy-hotreload.ps1` - 热重载服务管理 ⭐
**功能**: 管理热重载开发环境，支持实时代码修改，自动检测本机IP地址

```powershell
# 基本操作
.\scripts\deploy-hotreload.ps1 -Mode start     # 启动服务
.\scripts\deploy-hotreload.ps1 -Mode stop      # 停止服务
.\scripts\deploy-hotreload.ps1 -Mode restart   # 重启服务
.\scripts\deploy-hotreload.ps1 -Mode status    # 查看状态
.\scripts\deploy-hotreload.ps1 -Mode logs      # 查看日志

# 高级操作
.\scripts\deploy-hotreload.ps1 -Mode restart -Service frontend
.\scripts\deploy-hotreload.ps1 -Mode start -Build
```

**特性**: 自动IP检测、代码热重载、智能网络配置

### `start-container.ps1` - 标准容器启动
**功能**: 启动标准生产环境容器

```powershell
.\scripts\start-container.ps1
```

**特性**: 自动镜像检查、数据目录创建、访问地址显示

### `stop-container.ps1` - 容器停止
**功能**: 停止所有相关容器服务

```powershell
.\scripts\stop-container.ps1
```

---

## 📦 **镜像管理脚本**

### `package-for-server.ps1` - 服务器部署打包
**功能**: 创建完整的服务器部署包

```powershell
.\scripts\package-for-server.ps1

# 生成 server-deployment-package/ 目录
# 包含所有必要的部署文件
```

### `export-images.ps1` - 镜像导出
**功能**: 导出 Docker 镜像到 .tar 文件，用于离线部署

```powershell
.\scripts\export-images.ps1

# 生成文件:
# web_ai_vision_app_frontend_latest.tar
# web_ai_vision_app_backend_latest.tar
```

### `import-images.ps1` - 镜像导入
**功能**: 从 .tar 文件导入 Docker 镜像

```powershell
.\scripts\import-images.ps1

# 自动检测并导入当前目录下的 .tar 文件
```

---

## 🔄 **开发工作流脚本**

### `setup-code-sharing-simple.ps1` - A电脑网络共享设置 ⭐
**功能**: 在开发机（A电脑）上设置网络共享，供其他电脑访问代码

```powershell
# 设置网络共享（需要管理员权限）
.\scripts\setup-code-sharing-simple.ps1 setup

# 查看共享状态
.\scripts\setup-code-sharing-simple.ps1 status

# 移除网络共享
.\scripts\setup-code-sharing-simple.ps1 remove
```

**特性**:
- ✅ 自动创建共享用户和权限
- ✅ 智能IP地址检测
- ✅ 防火墙配置提醒
- ✅ 连接信息自动生成

### `sync-from-dev.ps1` - B电脑代码同步工具 ⭐
**功能**: 在部署机（B电脑）上从开发机实时同步代码

```powershell
# 基本同步
.\scripts\sync-from-dev.ps1 -DevMachineIP "*************" -Mode test  # 测试连接
.\scripts\sync-from-dev.ps1 -DevMachineIP "*************" -Mode once  # 单次同步

# 实时监控同步（推荐）
.\scripts\sync-from-dev.ps1 -DevMachineIP "*************" -Mode watch

# 自定义同步间隔
.\scripts\sync-from-dev.ps1 -DevMachineIP "*************" -Mode watch -WatchInterval 5

# 断开连接
.\scripts\sync-from-dev.ps1 -DevMachineIP "*************" -Mode disconnect
```

**特性**:
- ✅ 实时文件变化检测
- ✅ 自动网络重连
- ✅ 前后端代码分别同步
- ✅ Docker 服务自动重载

---

## 🔧 **系统配置脚本**

### `enable-auto-start.ps1` - 容器自动启动配置 ⭐
**功能**: 设置Docker容器在系统重启后自动启动，适用于生产服务器和无人值守部署

```powershell
# 设置容器自动启动
.\scripts\enable-auto-start.ps1

# 验证设置是否生效
docker inspect ai-vision-backend-hotreload --format '{{.HostConfig.RestartPolicy.Name}}'
docker inspect ai-vision-frontend-hotreload --format '{{.HostConfig.RestartPolicy.Name}}'
# 应该显示: unless-stopped
```

**特性**:
- ✅ 一键设置容器自动重启策略
- ✅ 自动检测Docker服务状态
- ✅ 显示当前容器状态和配置结果
- ✅ 提供详细的验证命令和说明

**使用场景**:
- 生产服务器部署后的自动启动配置
- 无人值守环境的服务保障
- 系统重启后服务自动恢复

### `setup-firewall.bat` - Windows 防火墙配置
**功能**: 自动配置 Windows 防火墙规则，允许 Docker 服务通过防火墙

```batch
# 以管理员身份运行
.\scripts\setup-firewall.bat

# 自动添加端口规则:
# - 8080 (前端服务)
# - 8000 (后端服务)
# - 5173 (前端开发服务器)
```

### `health.json` - 健康检查配置
**功能**: Docker 容器健康检查的配置文件

```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T00:00:00Z",
  "services": {
    "frontend": "running",
    "backend": "running"
  }
}
```

### `start-backend.sh` - 后端容器启动脚本
**功能**: Docker容器内的后端服务启动和初始化脚本

**主要功能**:
- 创建必要的目录结构 (`/app/db`, `/app/models`, `/app/media`, `/app/logs`)
- 运行Django数据库迁移
- 收集静态文件
- 自动创建超级用户 (admin/admin123)
- 启动Django开发服务器

**使用场景**: 在Dockerfile.backend中自动调用，无需手动执行

---

## 🔍 **诊断工具**

### `check/check-docker-env.ps1` - Docker 环境检查
**功能**: 全面检查 Docker 环境配置和状态

```powershell
.\scripts\check\check-docker-env.ps1

# 检查项目:
# - Docker Desktop 安装状态
# - Docker 服务运行状态
# - Docker 版本信息
# - 可用镜像列表
# - 容器运行状态
# - 磁盘空间使用情况
```

### `check/check-lan-access.ps1` - 局域网访问检查
**功能**: 检查局域网访问配置和连通性

```powershell
.\scripts\check\check-lan-access.ps1

# 检查项目:
# - 本机IP地址检测
# - 防火墙规则状态
# - 端口占用情况
# - 网络接口配置
# - 局域网连通性测试
```

### `check/check-network.ps1` - 网络配置检查
**功能**: 详细的网络配置诊断和故障排除

```powershell
.\scripts\check\check-network.ps1

# 检查项目:
# - 网络适配器状态
# - IP地址配置
# - DNS 配置
# - 路由表信息
# - Docker 网络配置
# - 端口监听状态
```

**特性**:
- ✅ 自动化诊断流程
- ✅ 详细的错误报告
- ✅ 修复建议提供
- ✅ 彩色输出显示

---

## 📝 使用建议

### 日常开发推荐流程
1. **启动**: `.\scripts\deploy-hotreload.ps1 -Mode start`
2. **开发**: 修改代码后自动热重载
3. **测试**: 前端 localhost:8080，后端API localhost:8080/api
4. **停止**: `.\scripts\deploy-hotreload.ps1 -Mode stop`

### 团队协作推荐流程
1. **A电脑**: 设置网络共享，正常开发
2. **B电脑**: 启动同步监控，实时获取代码更新
3. **测试**: B电脑提供稳定的测试环境
4. **部署**: 使用部署包进行生产部署

### 生产服务器部署流程
1. **镜像准备**: `.\scripts\build-images.ps1` 构建镜像
2. **服务启动**: `.\scripts\deploy-hotreload.ps1 -Mode start` 或 `.\scripts\start-container.ps1`
3. **自动启动**: `.\scripts\enable-auto-start.ps1` 设置开机自启
4. **验证测试**: 重启服务器验证容器自动启动
5. **监控维护**: 定期检查服务状态和日志

### 故障排除流程
1. **环境检查**: `.\scripts\check\check-docker-env.ps1`
2. **网络检查**: `.\scripts\check\check-network.ps1`
3. **重新构建**: `.\scripts\build-images.ps1 -NoCache`
4. **清理重启**: `docker system prune -a` 然后重新部署
