<mxfile host="Electron" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/26.2.15 Chrome/134.0.6998.205 Electron/35.2.1 Safari/537.36" version="26.2.15">
  <diagram name="第 1 页" id="_kfzjtLxOzjFRdamWCVF">
    <mxGraphModel dx="2431" dy="1011" grid="0" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1654" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="HvIb-9ZoJ27vfS6wcMD8-1" value="" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="393" y="47" width="1075" height="842" as="geometry" />
        </mxCell>
        <mxCell id="wlKLeiQbs4uNkoReZR8E-1" value="User" style="shape=umlLifeline;perimeter=lifelinePerimeter;whiteSpace=wrap;container=1;dropTarget=0;collapsible=0;recursiveResize=0;outlineConnect=0;portConstraint=eastwest;newEdgeStyle={&quot;edgeStyle&quot;:&quot;elbowEdgeStyle&quot;,&quot;elbow&quot;:&quot;vertical&quot;,&quot;curved&quot;:0,&quot;rounded&quot;:0};size=65;fillColor=#f5f5f5;fontColor=#333333;strokeColor=#666666;fontStyle=1;fontSize=20;" parent="1" vertex="1">
          <mxGeometry x="451" y="85" width="150" height="784" as="geometry" />
        </mxCell>
        <mxCell id="wlKLeiQbs4uNkoReZR8E-2" value="Frontend" style="shape=umlLifeline;perimeter=lifelinePerimeter;whiteSpace=wrap;container=1;dropTarget=0;collapsible=0;recursiveResize=0;outlineConnect=0;portConstraint=eastwest;newEdgeStyle={&quot;edgeStyle&quot;:&quot;elbowEdgeStyle&quot;,&quot;elbow&quot;:&quot;vertical&quot;,&quot;curved&quot;:0,&quot;rounded&quot;:0};size=65;fillColor=#dae8fc;strokeColor=#6c8ebf;fontStyle=1;fontSize=20;" parent="1" vertex="1">
          <mxGeometry x="651" y="85" width="150" height="784" as="geometry" />
        </mxCell>
        <mxCell id="wlKLeiQbs4uNkoReZR8E-3" value="API" style="shape=umlLifeline;perimeter=lifelinePerimeter;whiteSpace=wrap;container=1;dropTarget=0;collapsible=0;recursiveResize=0;outlineConnect=0;portConstraint=eastwest;newEdgeStyle={&quot;edgeStyle&quot;:&quot;elbowEdgeStyle&quot;,&quot;elbow&quot;:&quot;vertical&quot;,&quot;curved&quot;:0,&quot;rounded&quot;:0};size=65;fillColor=#d5e8d4;strokeColor=#82b366;fontStyle=1;fontSize=20;" parent="1" vertex="1">
          <mxGeometry x="851" y="85" width="150" height="784" as="geometry" />
        </mxCell>
        <mxCell id="wlKLeiQbs4uNkoReZR8E-4" value="ModelHandler" style="shape=umlLifeline;perimeter=lifelinePerimeter;whiteSpace=wrap;container=1;dropTarget=0;collapsible=0;recursiveResize=0;outlineConnect=0;portConstraint=eastwest;newEdgeStyle={&quot;edgeStyle&quot;:&quot;elbowEdgeStyle&quot;,&quot;elbow&quot;:&quot;vertical&quot;,&quot;curved&quot;:0,&quot;rounded&quot;:0};size=65;fillColor=#ffe6cc;strokeColor=#d79b00;fontStyle=1;fontSize=20;" parent="1" vertex="1">
          <mxGeometry x="1051" y="85" width="150" height="784" as="geometry" />
        </mxCell>
        <mxCell id="wlKLeiQbs4uNkoReZR8E-5" value="Database" style="shape=umlLifeline;perimeter=lifelinePerimeter;whiteSpace=wrap;container=1;dropTarget=0;collapsible=0;recursiveResize=0;outlineConnect=0;portConstraint=eastwest;newEdgeStyle={&quot;edgeStyle&quot;:&quot;elbowEdgeStyle&quot;,&quot;elbow&quot;:&quot;vertical&quot;,&quot;curved&quot;:0,&quot;rounded&quot;:0};size=65;fillColor=#fff2cc;strokeColor=#d6b656;fontStyle=1;fontSize=20;" parent="1" vertex="1">
          <mxGeometry x="1251" y="85" width="150" height="784" as="geometry" />
        </mxCell>
        <mxCell id="wlKLeiQbs4uNkoReZR8E-6" value="发起请求" style="verticalAlign=bottom;edgeStyle=elbowEdgeStyle;elbow=vertical;curved=0;rounded=0;endArrow=block;fontStyle=1;fontSize=20;" parent="1" source="wlKLeiQbs4uNkoReZR8E-1" target="wlKLeiQbs4uNkoReZR8E-2" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="635" y="192" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="wlKLeiQbs4uNkoReZR8E-7" value="发送API请求" style="verticalAlign=bottom;edgeStyle=elbowEdgeStyle;elbow=vertical;curved=0;rounded=0;endArrow=block;fontStyle=1;fontSize=20;" parent="1" source="wlKLeiQbs4uNkoReZR8E-2" target="wlKLeiQbs4uNkoReZR8E-3" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="835" y="244" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="wlKLeiQbs4uNkoReZR8E-8" value="查询用户数据" style="verticalAlign=bottom;edgeStyle=elbowEdgeStyle;elbow=vertical;curved=0;rounded=0;endArrow=block;fontStyle=1;fontSize=20;" parent="1" source="wlKLeiQbs4uNkoReZR8E-3" target="wlKLeiQbs4uNkoReZR8E-5" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1135" y="296" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="wlKLeiQbs4uNkoReZR8E-9" value="返回查询结果" style="verticalAlign=bottom;edgeStyle=elbowEdgeStyle;elbow=vertical;curved=0;rounded=0;dashed=1;dashPattern=2 3;endArrow=block;fontStyle=1;fontSize=20;" parent="1" source="wlKLeiQbs4uNkoReZR8E-5" target="wlKLeiQbs4uNkoReZR8E-3" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1138" y="348" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="wlKLeiQbs4uNkoReZR8E-10" value="返回用户信息" style="verticalAlign=bottom;edgeStyle=elbowEdgeStyle;elbow=vertical;curved=0;rounded=0;dashed=1;dashPattern=2 3;endArrow=block;fontStyle=1;fontSize=20;" parent="1" source="wlKLeiQbs4uNkoReZR8E-3" target="wlKLeiQbs4uNkoReZR8E-2" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="838" y="400" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="wlKLeiQbs4uNkoReZR8E-11" value="显示用户信息" style="verticalAlign=bottom;edgeStyle=elbowEdgeStyle;elbow=vertical;curved=0;rounded=0;endArrow=block;fontStyle=1;fontSize=20;" parent="1" source="wlKLeiQbs4uNkoReZR8E-2" target="wlKLeiQbs4uNkoReZR8E-1" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="638" y="452" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="wlKLeiQbs4uNkoReZR8E-12" value="请求模型推理" style="verticalAlign=bottom;edgeStyle=elbowEdgeStyle;elbow=vertical;curved=0;rounded=0;endArrow=block;fontStyle=1;fontSize=20;" parent="1" source="wlKLeiQbs4uNkoReZR8E-1" target="wlKLeiQbs4uNkoReZR8E-2" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="635" y="504" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="wlKLeiQbs4uNkoReZR8E-13" value="发送推理请求" style="verticalAlign=bottom;edgeStyle=elbowEdgeStyle;elbow=vertical;curved=0;rounded=0;endArrow=block;fontStyle=1;fontSize=20;" parent="1" source="wlKLeiQbs4uNkoReZR8E-2" target="wlKLeiQbs4uNkoReZR8E-3" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="835" y="556" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="wlKLeiQbs4uNkoReZR8E-14" value="执行模型推理" style="verticalAlign=bottom;edgeStyle=elbowEdgeStyle;elbow=vertical;curved=0;rounded=0;endArrow=block;fontStyle=1;fontSize=20;" parent="1" source="wlKLeiQbs4uNkoReZR8E-3" target="wlKLeiQbs4uNkoReZR8E-4" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1035" y="608" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="wlKLeiQbs4uNkoReZR8E-15" value="返回推理结果" style="verticalAlign=bottom;edgeStyle=elbowEdgeStyle;elbow=vertical;curved=0;rounded=0;dashed=1;dashPattern=2 3;endArrow=block;fontStyle=1;fontSize=20;" parent="1" source="wlKLeiQbs4uNkoReZR8E-4" target="wlKLeiQbs4uNkoReZR8E-3" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1038" y="660" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="wlKLeiQbs4uNkoReZR8E-16" value="返回推理结果" style="verticalAlign=bottom;edgeStyle=elbowEdgeStyle;elbow=vertical;curved=0;rounded=0;dashed=1;dashPattern=2 3;endArrow=block;fontStyle=1;fontSize=20;" parent="1" source="wlKLeiQbs4uNkoReZR8E-3" target="wlKLeiQbs4uNkoReZR8E-2" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="838" y="712" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="wlKLeiQbs4uNkoReZR8E-17" value="显示推理结果" style="verticalAlign=bottom;edgeStyle=elbowEdgeStyle;elbow=vertical;curved=0;rounded=0;endArrow=block;fontStyle=1;fontSize=20;" parent="1" source="wlKLeiQbs4uNkoReZR8E-2" target="wlKLeiQbs4uNkoReZR8E-1" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="638" y="764" />
            </Array>
          </mxGeometry>
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
