# AI修复合成视图多次修复问题修复文档

## 问题描述

在AI修复面板中，当用户选择"合成视图"模式并对同一张图像进行多次修复时，会出现图像被拉伸的问题。

### 问题原因

1. **尺寸计算错误**：多次修复时，`currentImageInfo` 可能是合成图（宽度=原图×2），使用合成图尺寸创建新合成图导致比例失调
2. **状态管理时序问题**：`restoreOriginalImageDisplay()` 调用后，状态更新可能有延迟
3. **图像源一致性问题**：API调用使用正确的原始文件，但合成图创建使用了错误的尺寸信息

## 修复方案

### 1. 核心问题修复：setCompositeImageDisplay逻辑

**文件**: `Frontend/src/contexts/ImageWorkspaceContext.tsx`

#### 修复前问题代码
```typescript
// 问题：每次都保存当前图像信息，导致originalImageInfo被合成图覆盖
if (currentImageInfo) {
  setOriginalImageInfoState({ ...currentImageInfo }); // ❌ 可能保存合成图信息
}
```

#### 修复后代码
```typescript
// 解决方案：只在首次设置时保存原始图像信息
if (currentImageInfo) {
  if (!originalImageInfo) {
    setOriginalImageInfoState({ ...currentImageInfo }); // ✅ 仅首次保存原始图像
  } else {
    // ✅ 多次设置时保持originalImageInfo不变
  }
}
```

### 2. 新增专用状态恢复函数

**文件**: `Frontend/src/contexts/ImageWorkspaceContext.tsx`

#### 新增函数
```typescript
// 专门用于AI修复的临时状态恢复，不清空originalImageInfo
const restoreOriginalForAiRestore = useCallback(async () => {
  if (originalImageInfo) {
    setCurrentImageInfoState(originalImageInfo);
    // 关键：不清空originalImageInfo，保持原始图像信息用于后续修复
    setDetectionResultsState(null);
    // ... 其他清理操作
  }
}, [originalImageInfo]);
```

### 3. 单图修复逻辑优化

**文件**: `Frontend/src/components/parameterPanels/AiRestorePanel.tsx`

#### 修复前问题代码
```typescript
// 问题：使用可能是合成图的currentImageInfo尺寸
const compositeDataUrl = await createAiRestoreCompositeImage(
    imageFileToProcess,
    response.image_base64,
    currentImageInfo.width,  // ❌ 可能是合成图宽度（原图×2）
    currentImageInfo.height  // ❌ 可能不是原图高度
);
```

#### 修复后代码
```typescript
// 解决方案1：确保使用原始图像信息
const originalInfo = originalImageInfo || currentImageInfo;
const compositeDataUrl = await createAiRestoreCompositeImage(
    imageFileToProcess,
    response.image_base64,
    originalInfo.width,  // ✅ 使用原始图像宽度
    originalInfo.height  // ✅ 使用原始图像高度
);

// 解决方案2：使用专用恢复函数
if (originalImageInfo) {
    await restoreOriginalForAiRestore(); // ✅ 不清空originalImageInfo
}
```

### 2. 状态恢复优化

#### 增强状态验证
```typescript
// 额外验证：检查当前图像名称是否包含合成视图标识
if (currentImageInfo?.name.includes('(Composite AI Restore)') || 
    currentImageInfo?.name.includes('(Restored)')) {
    console.warn('[AiRestorePanel] 检测到临时视图状态，强制恢复原始图像');
    await restoreOriginalImageDisplay();
    await new Promise(resolve => setTimeout(resolve, 300));
}
```

#### 增加等待时间
```typescript
// 从200ms增加到300ms，确保状态更新完成
await new Promise(resolve => setTimeout(resolve, 300));
```

### 3. 调试日志增强

添加详细的调试日志，便于问题排查：

```typescript
console.log('[AiRestorePanel] 使用图像尺寸:', {
    width: originalInfo.width,
    height: originalInfo.height,
    source: originalImageInfo ? 'originalImageInfo' : 'currentImageInfo'
});
```

## 修复效果验证

### 测试场景
1. **单图多次修复**：选择合成视图模式，对同一张图像连续执行多次AI修复
2. **批量处理**：验证批量处理中的合成视图渲染是否正常
3. **模式切换**：在合成视图和仅修复图模式之间切换测试

### 预期结果
- ✅ 多次修复后合成视图左侧始终显示原始图像
- ✅ 合成视图右侧显示最新的修复结果
- ✅ 图像比例保持正确，无拉伸变形
- ✅ 状态切换流畅，无异常

## 技术细节

### 关键修改点

1. **第292行**：使用 `originalImageInfo || currentImageInfo` 确保获取原始图像信息
2. **第305-306行**：合成图创建时使用 `originalInfo.width/height`
3. **第310-311行**：设置合成图显示时使用正确的尺寸计算
4. **第268-273行**：增加额外的状态验证逻辑
5. **第265行**：增加状态恢复等待时间

### 批量处理优化

批量处理中的 `currentImgInfo` 是从文件直接获取的原始信息，因此保持原有逻辑，但增加了调试日志。

## 相关文件

- `Frontend/src/components/parameterPanels/AiRestorePanel.tsx` - 主要修复文件
- `Frontend/src/contexts/ImageWorkspaceContext.tsx` - 图像状态管理
- `Frontend/src/contexts/AiRestoreDetectionContext.tsx` - AI修复状态管理

## 版本信息

- **修复版本**: V0.24.0.0
- **修复类型**: fix(frontend)
- **影响范围**: AI修复合成视图功能
