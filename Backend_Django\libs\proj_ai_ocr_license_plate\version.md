# 更新日志

记录更新内容。

## 2025年05月19日

**V1.0.2.0** @HuangJP 备份V1.0.2.0模型。

备份AI_OCR_Rec_CHNLP_V1.0.2.0模型，更新如下：
- 更新数据增强工具至V1.0.17。
- 数据增强脚本新增“添加花纹”步骤，用于模拟临时车牌背景中的花纹，增加模型对于临时车牌识别准确率。

更新后的识别模型性能如下：
- acc: 97.14%
- norm_edit_dis: 99.43%

## 2024年12月09日

**V1.0.1.1** @HuangJP 提交V1.0.1.0版本识别模型文件。

**V1.0.1.0** @HuangJP 发布模型版本V1.0.1，功能变更如下（V1.0.1.0）：

- V1.0.1更新了识别模型的输入尺寸，从48x320调整为了64x320，目的是提升警车车牌中“警”字的识别准确率。
    - 48x320尺寸下，由于输入张量高度只有48，字符缩放到这个尺寸后会丢失较多细节，导致诸如“警”这样笔画较多、较为复杂的字符识别出错。
    - 尺寸调整为64x320后，该现象得到了缓解。
- 更新后的识别模型表现如下：
    - acc: 97.74%
    - norm_edit_dis: 99.67%

## 2024年11月26日

**V1.0.0.0** @HuangJP 发布模型版本V1.0.0，功能变更如下（V1.0.0.0）：

- V1.0.0是国内车牌识别系列模型的第一个版本，其包含了检测和识别两个模型，其中：
    - 检测模型用于获取车牌在图片中的蒙版区域，在测试集上检测模型的表现如下：
        - precision: 79.97%
        - recall: 98.15%
        - hmean(F1score): 88.13%
    - 识别模型用于从蒙版区域识别文字字符，在测试集上识别模型的表现如下：
        - acc: 97.58%
        - norm_edit_dis: 99.62%
- 模型实验可通过dvc复现，复现命令为：
    - 检测模型: dvc repro train_det
    - 识别模型: dvc repro train_rec
- 模型训练完成后，若需要更新性能指标，可执行：
    - 检测模型: dvc repro evaluate_det
    - 识别模型: dvc repro evaluate_rec
- 模型训练完成后，可通过以下命令导出onnx格式模型（需要克隆Paddle2ONNX仓库到"/home/<USER>"目录）：
    - 检测模型: ./export_det.sh
    - 识别模型: ./export_rec.sh
- 模型导出完毕后，可通过以下命令在测试集上可视化模型效果：
    - ./predict.sh

## 2024年11月19日

**V1.0.1** @HuangJP 创建国内车牌识别开发分支

- 创建分支: dev/huangjp/android_v1.0_chnlp.

**V1.0.0** @HuangJP 初始化车牌识别派生仓库

- 初始化dvc项目。
- 重置版本号。