#ifndef _AI_ENGINE_SSC377_H
#define _AI_ENGINE_SSC377_H

//-----------------------------------------------------------------------------
//  Includes

// STL
#include <cstddef>
#include <cstdint>
#include <dirent.h>
#include <memory>
#include <new>
#include <vector>
#include <string>
#include <unordered_map>

// SDK
#include "Log.hpp"
#include "mi_ipu.h"
#include "mi_ipu_datatype.h"
#include "mi_sys.h"
#include "mi_common_datatype.h"

// AIEngine
#include "AIEngineCommon.h"
#include "Postprocess.hpp"
#include "Preprocess.hpp"
#include "NIU.hpp"
#include "ModelLoader.hpp"
#include "autoconf.h"
#include "test/DataTransfer.hpp"
#include "utils/InputImage.hpp"
//-----------------------------------------------------------------------------
//  Definitions

// 自适应子位深度提取范围功能参考区域尺寸
#ifndef MACR_ADAP_SBER_REF_SIZE
#define MACR_ADAP_SBER_REF_SIZE                 (100)
#endif

// 离线模型存储路径
#ifndef MACR_OFFLINE_MODEL_PATH
#define MACR_OFFLINE_MODEL_PATH                 "./"
#endif
//-----------------------------------------------------------------------------
//  Declarations

namespace AIEngineSSC377{
/**
 * @brief    
 *           ModelLoader(release版本)
 *           
 * @date     2024-02-29 Created by HuangJP
 */
class ModelLoader : public BaseModelLoader
{
public:
    /**
     * @brief    
     *           获取ModelLoader实例，该实例具备唯一性
     *           
     * @retval   指向ModelLoader实例的指针
     *           
     * @date     2024-02-29 Created by HuangJP
     */
    static ModelLoader *Instance(void)
    {
        static ModelLoader model_loader;
        return &model_loader;
    }

    /**
     * @brief    
     *           获取解释器
     *           
     * @param    model_tag:     模型标签
     *           
     * @retval   指向解释器的指针，获取失败时返回空指针
     *           
     * @date     2024-02-29 Created by HuangJP
     */
    std::shared_ptr<MI_U32> Get_Interpreter(std::string model_tag)
    {
        // 尝试获取解释器
        if (interpreter_map.find(model_tag) == interpreter_map.end())
        {
            // 获取失败时，尝试创建解释器
            const ModelData *model = Get_Model_Data(model_tag);  // 尝试获取模型数据
            if (model == nullptr)
            {
                // 获取模型数据失败，返回空指针
                return nullptr;
            }

            // 创建IPU设备
            if (this->is_ipu_create == false)
            {
                int rev = this->Create_IPU_Device();
                if (rev != AIENGINE_NO_ERROR)
                {
                    return nullptr;
                }

                this->is_ipu_create = true;
            }

            // 尝试获取模型文件
            std::string model_path(MACR_OFFLINE_MODEL_PATH); // 模型存放路径
            std::vector<std::string> model_list = this->_get_offline_model_list(model_path); // 获取离线模型列表
            std::string model_file;
            for (auto filename: model_list)
            {
                // 遍历离线模型列表，找到目标模型文件
                if (filename.find(model_tag) != std::string::npos)
                {
                    // 找到模型文件，停止遍历离线模型列表，并记录模型文件路径
                    model_file.assign(model_path+filename);
                    break;
                }
            }

            // 判断模型文件是否获取成功
            if (model_file.empty())
            {
                LOGE("The corresponding model was not found in the specified path.");
                LOGD("An attempt was made to locate model `%s` from `%s`, but it failed.", model_tag.c_str(), MACR_OFFLINE_MODEL_PATH);
                return nullptr;
            }

            // 创建解释器（创建IPU通道）
            std::shared_ptr<MI_U32> u32ChannelID = std::shared_ptr<MI_U32>(new (std::nothrow)MI_U32);
            MI_IPUChnAttr_t stChnAttr = {0x00};
            int batch_size = 1; // 默认batch_size为1
            stChnAttr.u32InputBufDepth = batch_size; // 输入张量buffer深度
            stChnAttr.u32OutputBufDepth = batch_size; // 输出张量buffer深度
            MI_S32 ret = MI_IPU_CreateCHN(u32ChannelID.get(), &stChnAttr, nullptr, const_cast<char *>(model_file.c_str()));
            if (ret != MI_SUCCESS)
            {
                LOGE("Failed to create IPU channel.");
                LOGD("Error code: %d", ret);
                return nullptr;
            }

            // 保存解释器
            this->interpreter_map[model_tag] = u32ChannelID;
        }

        return this->interpreter_map[model_tag];
    }

protected:
    // 模型标签(string)映射到解释器的数据类型
    typedef std::unordered_map<std::string, std::shared_ptr<MI_U32>> interpreter_map_t;
    interpreter_map_t interpreter_map;
    bool is_ipu_create;

    // 析构函数
    ~ModelLoader() {}
    // 构造函数
    ModelLoader(): BaseModelLoader()
    {
        // 初始化变量
        is_ipu_create = false;
    }

private:
    /**
     * @brief    
     *           获取离线模型列表
     *           
     * @param    path:      模型存放路径
     *           
     * @retval   离线模型存放路径列表
     *           
     * @date     2024-07-29 Created by HuangJP
     */
    std::vector<std::string> _get_offline_model_list(std::string path)
    {
        std::vector<std::string> offline_models;
        DIR *dir; // 打开路径
        struct dirent *dr;

        // 打开模型存放路径
        dir = opendir(path.c_str());
        if (dir == nullptr)
        {
            return offline_models; // 打开目标路径失败，返回空的模型列表
        }

        // 逐个读目录下的文件
        while ((dr = readdir(dir)) != nullptr)
        {
            // 获取文件后缀
            std::string filename(dr->d_name);
            size_t pos = filename.find_last_of('.');
            if (pos == std::string::npos)
            {
                continue; // 获取后缀分隔符失败，跳过当前文件
            }

            // 判断当前文件是否为模型文件
            std::string suffix = filename.substr(pos+1);
            if (suffix != "img")
            {
                continue; // 当前文件不是模型文件，跳过当前文件
            }

            offline_models.emplace_back(filename); // 返回模型文件名
        }

        closedir(dir); // 关闭打开的路径

        return offline_models; // 返回离线模型列表
    }

    /**
     * @brief    
     *           创建IPU设备
     *           
     * @retval   错误码
     *           
     * @date     2024-07-29 Created by HuangJP
     */
    int Create_IPU_Device(void)
    {
        MI_SYS_Init(0); // 初始化MI系统

        // 获取离线模型文件列表
        std::string model_path(MACR_OFFLINE_MODEL_PATH);
        std::vector<std::string> model_list = this->_get_offline_model_list(model_path);
        if (model_list.size() == 0)
        {
            LOGE("Failed to get the offline model list. Please check the path where the models are saved.");
            LOGD("Try to load model from: `%s`", MACR_OFFLINE_MODEL_PATH);
            return AIENGINE_INVALID_PARAM;
        }

        // 通过离线模型文件信息，确认模型内部Tensor使用的最大memory大小
        MI_IPU_DevAttr_t stDevAttr; // 设备属性
        stDevAttr.u32MaxVariableBufSize = 0; // 模型内部Tensor使用的最大memory大小
        for (auto filename: model_list)
        {
            MI_IPU_OfflineModelStaticInfo_t info;
            MI_S32 ret = MI_IPU_GetOfflineModeStaticInfo(nullptr, const_cast<char *>((model_path+filename).c_str()), &info);
            if (ret != MI_SUCCESS)
            {
                LOGE("Failed to get the offline model static info.");
                LOGD("Tried to get static info from `%s`, but encountered an error with code `%d`.", filename.c_str(), ret);
                continue;
            }

            // 设置模型内部Tensor使用的最大memory大小
            stDevAttr.u32MaxVariableBufSize = std::max(stDevAttr.u32MaxVariableBufSize, info.u32VariableBufferSize);
        }

        // 判断是否成功读取最大memory大小
        if (stDevAttr.u32MaxVariableBufSize == 0)
        {
            LOGE("Invalid model buffer size. Please check whether the model static info has been read successfully or not.");
            return AIENGINE_INVALID_PARAM;
        }

        // 创建IPU设备
        MI_S32 ret = MI_IPU_CreateDevice(&stDevAttr, nullptr, nullptr, 0);
        if (ret != MI_SUCCESS)
        {
            LOGE("Failed to create IPU device.");
            LOGD("Error code: %d", ret);
            return AIENGINE_CONSTRUCT_ERROR;
        }

        return AIENGINE_NO_ERROR;
    }
};

/**
 * @brief    
 *           前处理模块
 *           
 * @date     2024-07-30 Created by HuangJP
 */
class Preprocess : public BasePreprocess
{
public:
    /**
     * @brief    
     *           前处理模块构造函数（生产函数）
     *           
     * @param    interpreter:   指向解释器（IPU通道ID）的共享指针
     * @param    session:       会话（输入输出张量信息）
     * @param    model_data:    模型数据
     *           
     * @retval   指向前处理模块对象的指针，构造失败时返回空指针
     *           
     * @date     2024-07-31 Created by HuangJP
     */
    static Preprocess *Construct(std::shared_ptr<MI_U32> interpreter, MI_IPU_SubNet_InputOutputDesc_t *session, const BaseModelLoader::ModelData *model_data)
    {
        // 检查传入参数是否合法
        if ((interpreter == nullptr)
            || (session == nullptr)
            || (model_data == nullptr))
        {
            LOGE("The interpreter and session does not allow setting it to nullptr.");
            return nullptr;
        }

        // 构造前处理模块
        Preprocess *pre = new (std::nothrow)Preprocess(interpreter, session, model_data);

        // 判断构造是否成功
        if (pre == nullptr)
        {
            LOGE("Failed to allocate memory for the Preprocess module.");
            return nullptr;
        }

        if (pre->is_ready == false)
        {
            // 前处理模块未准备好，释放已申请的内存
            LOGE("Failed to construct preprocess module.");
            delete pre;
            pre = nullptr;
            return nullptr;
        }

        return pre; // 返回前处理模块
    }

    /**
     * @brief    
     *           前处理模块销毁函数
     *           
     * @param    pre:       指向前处理模块的指针
     *           
     * @date     2024-07-30 Created by HuangJP
     */
    static void Destruct(Preprocess *pre)
    {
        if (pre != nullptr)
        {
            delete pre;
        }
    }

    /**
     * @brief    
     *           获取IPU输入张量
     *           
     * @retval   IPU输入张量
     *           
     * @date     2024-07-31 Created by HuangJP
     */
    MI_IPU_TensorVector_t *Get_IPU_Input_Tensor(void)
    {
        return &this->ipu_input_tensor;
    }

protected:
    /**
     * @brief    
     *           获取输入图片工具实例
     *           
     * @param    color_format:      输入图片颜色格式
     * @param    rsn:               右移位数
     * @param    memory_format:     内存存储格式
     *           
     * @retval   指向输入图片工具实例的指针
     *           
     * @date     2024-10-31 Created by HuangJP
     */
    InputImage *Get_Input_Image_Instance(bit_depth_t bit_depth, color_format_t color_format) override
    {
        return _Get_Input_Image_Instance<MI_U8, false>(bit_depth, color_format, this->_model_data->memory_format);
    }

    /**
     * @brief    
     *           前处理数据构造函数
     *           
     * @param    index:     输入张量索引
     * @param    data:      指向前处理数据的指针
     *           
     * @retval   错误码
     *           
     * @date     2024-07-30 Created by HuangJP
     */
    int Construct_Preprocessed_Data(void *index, PreprocessedData *data) override
    {
        tensor_index_t idx = (tensor_index_t)reinterpret_cast<intptr_t>(index);

        // 判断传入参数是否合法
        if ((idx >= (tensor_index_t)this->_session->u32InputTensorCount)
            || (data == nullptr))
        {
            LOGE("Unable to construct preprocessed data; an invalid parameter has been detected.");
            return AIENGINE_INVALID_PARAM;
        }

        // 设置输入张量信息
        if (this->_model_data->memory_format == BaseModelLoader::AIENGINE_MEMORY_FORMAT_NHWC)
        {
            data->resize_n = this->_session->astMI_InputTensorDescs[idx].u32TensorShape[0];
            data->resize_h = this->_session->astMI_InputTensorDescs[idx].u32TensorShape[1];
            data->resize_w = this->_session->astMI_InputTensorDescs[idx].u32TensorShape[2];
            data->resize_c = this->_session->astMI_InputTensorDescs[idx].u32TensorShape[3];
        }
        else
        {
            LOGE("Unsupported input tensor memory format.");
            return AIENGINE_INVALID_PARAM;
        }

        data->input_tensor_ptr = (tensor_t *)this->ipu_input_tensor.astArrayTensors[idx].ptTensorData[0];

        // 记录输入张量信息
        input_tensor_map[idx] = (input_tensor_t){
            .data = (tensor_t *)this->ipu_input_tensor.astArrayTensors[idx].ptTensorData[0],
            .data_size = this->_session->astMI_InputTensorDescs[idx].u32TensorShape[0]
                            * this->_session->astMI_InputTensorDescs[idx].u32TensorShape[1]
                            * this->_session->astMI_InputTensorDescs[idx].u32TensorShape[2]
                            * this->_session->astMI_InputTensorDescs[idx].u32TensorShape[3],
        };

        return AIENGINE_NO_ERROR;
    }

    /**
     * @brief    
     *           同步输入张量数据到内存
     *           
     * @param    index:     输入张量索引
     *           
     * @retval   错误码
     *           
     * @date     2024-07-30 Created by HuangJP
     */
    int Input_Tensor_Flush_Memory(void *index) override
    {
        tensor_index_t idx = (tensor_index_t)reinterpret_cast<intptr_t>(index);

        // 尝试获取输入张量
        if (input_tensor_map.find(idx) == input_tensor_map.end())
        {
            LOGE("Unable to find the input tensor by index.");
        }
        input_tensor_t input_tensor = input_tensor_map[idx];

        // 同步输入张量数据
        MI_SYS_FlushInvCache(input_tensor.data, input_tensor.data_size);

        return AIENGINE_NO_ERROR;
    }

private:
    typedef MI_U32 tensor_index_t;
    typedef MI_U8 tensor_t;

    // 输入张量信息结构体
    typedef struct {
        tensor_t *data;
        size_t data_size;
    }input_tensor_t;

    bool is_ready; // 是否准备好
    MI_IPU_TensorVector_t ipu_input_tensor; // IPU输入张量
    const std::shared_ptr<MI_U32> _interpreter; // 解释器（IPU通道ID）
    const MI_IPU_SubNet_InputOutputDesc_t *_session; // 会话（输入输出张量信息）
    std::unordered_map<tensor_index_t, input_tensor_t> input_tensor_map; // 输入张量索引列表

    /**
     * @brief    
     *           Preprocess析构函数
     *           
     * @date     2024-07-30 Created by HuangJP
     */
    ~Preprocess()
    {

    }

    /**
     * @brief    
     *           Preprocess构造函数
     *           
     * @param    interpreter:       指向解释器（IPU通道ID）的共享指针
     * @param    session:           会话（输入输出张量信息）
     * @param    model_data:        模型数据
     *           
     * @date     2024-07-30 Created by HuangJP
     */
    Preprocess(std::shared_ptr<MI_U32> interpreter, MI_IPU_SubNet_InputOutputDesc_t *session, const BaseModelLoader::ModelData *model_data):
        BasePreprocess(MACR_ADAP_SBER_REF_SIZE, model_data),
        _interpreter(interpreter),
        _session(session)
    {
        // 初始化变量
        is_ready = false;

        // 获取输入张量
        MI_S32 ret = MI_IPU_GetInputTensors(*_interpreter, &this->ipu_input_tensor);
        if (ret != MI_SUCCESS)
        {
            LOGE("Failed to get ipu input tensor.");
            return;
        }

        is_ready = true; // 通知模块已准备好
    }
};

/**
 * @brief    
 *           后处理模块
 *           
 * @date     2024-07-31 Created by HuangJP
 */
class Postprocess : public BasePostprocess
{
public:
    /**
     * @brief    
     *           后处理模块构造函数（生产函数）
     *           
     * @param    interpreter:   指向解释器（IPU通道ID）的共享指针
     * @param    session:       会话（输入输出张量信息）
     * @param    model_data:    模型数据
     *           
     * @retval   指向后处理模块对象的指针，构造失败时返回空指针
     *           
     * @date     2024-07-31 Created by HuangJP
     */
    static Postprocess *Construct(std::shared_ptr<MI_U32> interpreter, MI_IPU_SubNet_InputOutputDesc_t *session, const BaseModelLoader::ModelData *model_data)
    {
        // 检查传入参数是否合法
        if ((interpreter == nullptr)
            || (session == nullptr)
            || (model_data == nullptr))
        {
            LOGE("The interpreter and session does not allow setting it to nullptr.");
            return nullptr;
        }

        // 构造后处理模块
        Postprocess *post = new (std::nothrow)Postprocess(interpreter, session, model_data);

        // 判断构造是否成功
        if (post == nullptr)
        {
            LOGE("Failed to allocate memory for the Postprocess module.");
            return nullptr;
        }

        // 判断后处理模块是否准备好
        if (post->is_ready == false)
        {
            // 后处理模块未准备好，释放已申请的内存
            LOGE("Failed to construct postprocess module.");
            delete post;
            post = nullptr;
            return nullptr;
        }

        return post;
    }

    /**
     * @brief    
     *           后处理模块销毁函数
     *           
     * @param    post:  指向后处理模块的指针
     *           
     * @date     2024-07-31 Created by HuangJP
     */
    static void Destruct(Postprocess *post)
    {
        if (post != nullptr)
        {
            delete post;
        }
    }

    /**
     * @brief    
     *           获取IPU输出张量
     *           
     * @retval   指向IPU输出张量的指针
     *           
     * @date     2024-07-31 Created by HuangJP
     */
    MI_IPU_TensorVector_t *Get_IPU_Output_Tensor(void)
    {
        return &this->ipu_output_tensor;
    }

private:
    typedef MI_U32 tensor_index_t;

    bool is_ready; // 是否准备好
    MI_IPU_TensorVector_t ipu_output_tensor; // IPU输出张量
    const std::shared_ptr<MI_U32> _interpreter; // 解释器（IPU通道ID）
    const MI_IPU_SubNet_InputOutputDesc_t *_session; // 会话（输入输出张量信息）
    std::unordered_map<tensor_index_t, output_tensor_t> output_tensor_map; // 输出张量索引

    /**
     * @brief    
     *           获取输出张量
     *           
     * @param    index:     输出张量索引
     *           
     * @retval   指向输出张量组的指针，获取失败时返回空指针
     *           
     * @date     2024-10-30 Created by HuangJP
     */
    output_tensor_t *Get_Output_Tensor(void *index) override
    {
        tensor_index_t idx = (tensor_index_t)reinterpret_cast<intptr_t>(index);

        // 尝试根据索引获取输出张量
        if (output_tensor_map.find(idx) == output_tensor_map.end())
        {
            // 获取失败时，尝试创建输出张量
            // 判断传入参数是否合法
            if (idx >= (tensor_index_t)this->_session->u32OutputTensorCount)
            {
                LOGE("Unable to create output tensor; an invalid tensor index has been detected.");
                return nullptr;
            }

            // 获取输出张量形状
            std::vector<int> shape;
            const int dim = sizeof(this->_session->astMI_OutputTensorDescs[idx].u32TensorShape) / sizeof(this->_session->astMI_OutputTensorDescs[idx].u32TensorShape[0]);
            for (int i = 0; i < dim; i++)
            {
                shape.emplace_back(this->_session->astMI_OutputTensorDescs[idx].u32TensorShape[i]);
            }

            // 创建输出张量
            output_tensor_map[idx] = (output_tensor_t){
                .data = (void *)this->ipu_output_tensor.astArrayTensors[idx].ptTensorData[0],
                .shape = shape,
            };
        }

        // 返回输出张量
        return &output_tensor_map[idx];
    }

    /**
     * @brief    
     *           后处理模块析构函数
     *           
     * @date     2024-07-31 Created by HuangJP
     */
    ~Postprocess()
    {

    }

    /**
     * @brief    
     *           后处理模块构造函数
     *           
     * @param    interpreter:       指向解释器（IPU通道ID）的共享指针
     * @param    session:           会话（输入输出张量信息）
     * @param    model_data:        模型数据
     *           
     * @date     2024-07-31 Created by HuangJP
     */
    Postprocess(std::shared_ptr<MI_U32> interpreter, MI_IPU_SubNet_InputOutputDesc_t *session, const BaseModelLoader::ModelData *model_data):
        BasePostprocess(model_data),
        _interpreter(interpreter),
        _session(session)
    {
        // 初始化参数
        is_ready = false;

        // 获取IPU输出张量
        MI_S32 ret = MI_IPU_GetOutputTensors(*_interpreter, &this->ipu_output_tensor);
        if (ret != MI_SUCCESS)
        {
            LOGE("Failed to get ipu output tensor.");
            return;
        }

        is_ready = true;
    }
};

/**
 * @brief    
 *           神经网络推理单元
 *           
 * @date     2024-07-31 Created by HuangJP
 */
class NIU : public BaseNIU
{
public:
    /**
     * @brief    
     *           神经网络推理单元构造函数（生成函数）
     *           
     * @param    model_tag:     模型标签
     *           
     * @retval   指向NIU的指针，构造失败时返回空指针
     *           
     * @date     2024-07-31 Created by HuangJP
     */
    static NIU *Construct(std::string model_tag)
    {
        // 构造NIU
        NIU *niu = new (std::nothrow)NIU(model_tag);
        if (niu == nullptr)
        {
            LOGE("Failed to allocate memory for the NIU.");
            return nullptr;
        }

        // 判断NIU是否未准备好
        if (niu->is_ready == false)
        {
            // NIU未准备好，释放已申请的内存
            delete niu;
            niu = nullptr;
            return nullptr;
        }

        return niu;
    }

    /**
     * @brief    
     *           神经网络推理单元销毁函数
     *           
     * @date     2024-07-31 Created by HuangJP
     */
    static void Destruct(NIU *niu)
    {
        if (niu != nullptr)
        {
            delete niu;
        }
    }

    /**
     * @brief    
     *           获取前处理模块接口
     *           
     * @retval   指向前处理模块的指针
     *           
     * @date     2024-07-31 Created by HuangJP
     */
    class Preprocess *Preprocess(void) override
    {
        return this->pre.get();
    }

    /**
     * @brief    
     *           获取后处理模块接口
     *           
     * @retval   指向后处理模块的指针
     *           
     * @date     2024-07-31 Created by HuangJP
     */
    class BasePostprocess *Postprocess(void) override
    {
        return this->post.get();
    }

    /**
     * @brief    
     *           推理接口
     *           
     * @retval   错误码
     *           
     * @date     2024-07-31 Created by HuangJP
     */
    int Inference(void) override
    {
        // 调用推理
        MI_S32 ret = MI_IPU_Invoke(*this->interpreter, this->pre->Get_IPU_Input_Tensor(), this->post->Get_IPU_Output_Tensor());
        if (ret != MI_SUCCESS)
        {
            LOGE("Failed to invoke IPU inference.");
            return AIENGINE_INFERENCE_FAILED;
        }

        return AIENGINE_NO_ERROR;
    }

    /**
     * @brief    
     *           获取输入张量索引列表
     *           
     * @retval   指向输入张量索引列表的指针
     *           
     * @date     2024-07-31 Created by HuangJP
     */
    const std::vector<void *> *Get_Input_Tensor_Index_List(void) override
    {
        return &model->input_tensor_index_list;
    }

    /**
     * @brief    
     *           获取输出张量索引列表
     *           
     * @retval   指向输出张量索引列表的指针
     *           
     * @date     2024-07-31 Created by HuangJP
     */
    const std::vector<void *> *Get_Output_Tensor_Index_List(void) override
    {
        return &model->output_tensor_index_list;
    }

private:
    bool is_ready; // 是否准备好
    std::shared_ptr<MI_U32> interpreter; // 解释器（IPU通道ID）
    MI_IPU_SubNet_InputOutputDesc_t session; // 会话（输入输出张量信息）
    const BaseModelLoader::ModelData *model; // 模型数据
    std::shared_ptr<class Preprocess> pre; // 前处理模块
    std::shared_ptr<class Postprocess> post; // 后处理模块

    /**
     * @brief    
     *           神经网络推理单元析构函数
     *           
     * @date     2024-07-31 Created by HuangJP
     */
    ~NIU()
    {
        
    }

    /**
     * @brief    
     *           神经网络推理单元构造函数
     *           
     * @param    model_tag:     模型标签
     *           
     * @date     2024-07-31 Created by HuangJP
     */
    NIU(std::string model_tag):
        BaseNIU(model_tag)
    {
        // 变量初始化
        this->is_ready = false;
        this->interpreter = nullptr;
        this->model = nullptr;
        this->pre = nullptr;
        this->post = nullptr;

        // 获取解释器
        this->interpreter = ModelLoader::Instance()->Get_Interpreter(model_tag);
        if (this->interpreter == nullptr)
        {
            return;
        }

        // 尝试获取模型数据
        this->model = ModelLoader::Get_Model_Data(model_tag);
        if (this->model == nullptr)
        {
            return;
        }

        // 配置推理后端
        if (this->model->backend != BaseModelLoader::AIENGINE_BACKEND_IPU)
        {
            // SSC377仅支持IPU作为推理后端
            LOGW("SSC377 only support IPU inference backend.");
        }

        // 获取会话（输入输出张量信息）
        MI_S32 ret = MI_IPU_GetInOutTensorDesc(*this->interpreter, &session);
        if (ret != MI_SUCCESS)
        {
            LOGE("Failed to get input/output tensor info.");
            return;
        }

        // 构建前处理模块
        this->pre = std::shared_ptr<class Preprocess>(
            Preprocess::Construct(this->interpreter, &session, this->model), 
            Preprocess::Destruct);
        if (pre == nullptr)
        {
            LOGE("Failed to construct preprocess module.");
            return;
        }

        // 构造后处理模块
        this->post = std::shared_ptr<class Postprocess>(
            Postprocess::Construct(this->interpreter, &session, this->model), 
            Postprocess::Destruct);
        if (pre == nullptr)
        {
            LOGE("Failed to construct postprocess module.");
            return;
        }

        // 通知NIU创建成功
        this->is_ready = true;
    }
};

} /* namespace AIEngineSSC377 */
#endif
//-----------------------------------------------------------------------------
//  End of file