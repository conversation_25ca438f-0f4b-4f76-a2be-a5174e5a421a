# AI Vision App - Docker 部署文档

本目录包含 AI Vision App 的完整 Docker 部署解决方案，支持标准生产部署和开发热重载模式。

## 📁 目录结构

```
docker/
├── 📄 Docker 配置文件
│   ├── docker-compose.yml              # 标准生产环境配置
│   ├── docker-compose.hotreload.yml    # 热重载开发配置
│   ├── docker-compose.simple.yml       # 简化部署配置
│   ├── docker-compose.dev.yml          # 开发环境配置
│   ├── Dockerfile.frontend             # 前端镜像构建文件
│   ├── Dockerfile.backend              # 后端镜像构建文件
│   ├── nginx.conf                      # Nginx 配置（含API代理）
│   ├── nginx-hotreload.conf            # 热重载模式 Nginx 配置
│   ├── pip.conf                        # Python 包国内镜像源配置
│   └── daemon.json                     # Docker 国内镜像源配置
├── 🛠️ 管理脚本
│   └── scripts/
│       ├── 🔨 核心部署脚本
│       ├── 📦 镜像管理脚本
│       ├── 🔄 开发工作流脚本
│       ├── 🔧 系统配置脚本
│       └── 🔍 诊断工具
├── 💾 数据目录
│   └── data/                           # 持久化数据存储
└── 🔄 热重载目录
    └── code-sync/                      # 热重载代码同步
```

## 🚀 快速开始

### 🎯 **选择部署方式**

#### 方式一：标准生产部署
**适用场景**: 生产环境、演示环境、稳定测试

```powershell
# 1. 构建 Docker 镜像
.\scripts\build-images.ps1

# 2. 启动标准服务
.\scripts\start-container.ps1

# 3. 访问应用
# 前端: http://localhost:8080 或 http://你的IP:8080
# 后端: http://localhost:8000 或 http://你的IP:8000
```

#### 方式二：完整开发工作流 (推荐)
**适用场景**: 本地开发、前后端联调、功能测试

```powershell
# 1. 构建 Docker 镜像
.\scripts\build-images.ps1

# 2. 启动完整开发环境
.\scripts\dev-workflow.ps1 -Mode full-dev

# 3. 开发环境访问
# 前端开发: http://localhost:5173 (热重载)
# 后端API: http://localhost:8080/api (Docker)
# 生产预览: http://localhost:8080
```

#### 方式三：热重载部署
**适用场景**: 服务器开发、代码同步、远程开发

```powershell
# 1. 构建镜像并准备代码
.\scripts\build-images.ps1
.\scripts\frontend-dev.ps1 -Mode build
.\scripts\frontend-dev.ps1 -Mode sync

# 2. 启动热重载服务
.\scripts\deploy-hotreload.ps1 -Mode start

# 3. 访问应用
# 前端: http://localhost:8080 或 http://你的IP:8080
# 后端: http://localhost:8000 或 http://你的IP:8000
```

#### 方式四：跨电脑同步开发
**适用场景**: A电脑开发，B电脑部署，实时同步

```powershell
# A电脑（开发机）设置
.\scripts\setup-code-sharing.ps1 setup

# B电脑（部署机）同步
.\scripts\sync-from-dev.ps1 -DevMachineIP "A电脑IP" -Mode watch
```

## 🌐 访问地址

### 标准部署访问地址
- **前端应用**: http://localhost:8080 或 http://你的IP:8080
- **后端API**: http://localhost:8000 或 http://你的IP:8000
- **API模型列表**: http://localhost:8000/api/vision/models/
- **健康检查**: http://localhost:8080/health

### 热重载部署访问地址
- **前端应用**: http://localhost:8080 或 http://你的IP:8080
- **后端API**: 通过nginx代理访问 http://localhost:8080/api/
- **直接后端**: http://localhost:8000 (仅调试用)

### 局域网访问
脚本会自动检测并显示局域网IP，其他设备可通过以下方式访问：
- **手机/平板**: http://192.168.1.xxx:8080
- **其他电脑**: http://192.168.1.xxx:8080

## 📚 详细文档

- **[脚本功能详解](./scripts-guide.md)** - 所有脚本的详细使用说明
- **[Docker配置说明](./docker-configs.md)** - Docker配置文件详解
- **[部署指南](./deployment-guide.md)** - 完整的部署工作流指南
- **[故障排除](./troubleshooting.md)** - 常见问题和解决方案
- **[网络访问配置](./network-access.md)** - 网络配置和局域网访问

## 📊 系统要求

### 💻 最低要求
- **操作系统**: Windows 10+ / macOS 10.14+ / Ubuntu 18.04+
- **Docker**: Docker Desktop 4.0+
- **内存**: 4GB+ 可用内存
- **磁盘**: 20GB+ 可用空间
- **网络**: 稳定的互联网连接（首次构建）

### 🚀 推荐配置
- **内存**: 8GB+ 可用内存
- **磁盘**: 50GB+ 可用空间（包含AI模型文件）
- **CPU**: 4核心以上（AI推理性能更佳）
- **网络**: 千兆局域网（多设备访问）

## 🆘 获取帮助

如果遇到问题，请按以下顺序排查：

1. **查看日志**: `docker-compose logs -f`
2. **运行诊断**: `.\scripts\check\check-docker-env.ps1`
3. **检查文档**: 查看相关详细文档
4. **重新构建**: `.\scripts\build-images.ps1 -NoCache`
5. **清理重启**: `docker system prune -a` 然后重新部署

---

**🎉 恭喜！您已成功配置 AI Vision App 的 Docker 部署环境！**
