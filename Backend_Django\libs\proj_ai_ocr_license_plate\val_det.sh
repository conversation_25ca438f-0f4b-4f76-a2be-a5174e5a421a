# 检测模型
model="output/ccpd_det_ppocr_v4"
eval_log="${model}/evaluate.log"
metrics_dir="mindeo/metrics"
metric="${metrics_dir}/det.json"

# 评估模型
python3.10 tools/eval.py \
-c ${model}/config.yml \
-o Global.checkpoints="${model}/best_model/model.pdparams"\
 Eval.dataset.data_dir="./mindeo/data_processed/Det/Train/test" \
 Eval.dataset.label_file_list="[./mindeo/data_processed/Det/Train/test/Label.txt]" > ${eval_log}

# 获取评估结果
precision=$(cat ${eval_log} | grep "INFO: precision:" | awk -F'precision:' '{print $2}')
recall=$(cat ${eval_log} | grep "INFO: recall:" | awk -F'recall:' '{print $2}')
hmean=$(cat ${eval_log} | grep "INFO: hmean:" | awk -F'hmean:' '{print $2}')

# 保存结果
mkdir -p ${metrics_dir}
echo '{' > ${metric}
echo '    "evaluate": {' >> ${metric}
echo '        "precision": ' "${precision}," >> ${metric}
echo '        "recall": ' "${recall}," >> ${metric}
echo '        "hmean": ' "${hmean}" >> ${metric}
echo '    }' >> ${metric}
echo '}' >> ${metric}