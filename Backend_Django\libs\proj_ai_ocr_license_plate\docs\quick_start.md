---
comments: true
hide:
  - navigation
---

- 在线免费体验：
    - PP-OCRv4 在线体验地址：<https://aistudio.baidu.com/community/app/91660>
    - SLANet 在线体验地址：<https://aistudio.baidu.com/community/app/91661>
    - PP-ChatOCRv3-doc 在线体验地址：<https://aistudio.baidu.com/community/app/182491>
    - PP-ChatOCRv2-common 在线体验地址：<https://aistudio.baidu.com/community/app/91662>
    - PP-ChatOCRv2-doc 在线体验地址：<https://aistudio.baidu.com/community/app/70303>

- [一键调用17个PaddleOCR核心模型](https://paddlepaddle.github.io/PaddleOCR/latest/paddlex/quick_start.html)
- 一行命令快速使用：[文本检测识别（中英文/多语言）](https://paddlepaddle.github.io/PaddleOCR/latest/ppocr/overview.html)
- 一行命令快速使用：[文档分析](https://paddlepaddle.github.io/PaddleOCR/latest/ppstructure/overview.html)
