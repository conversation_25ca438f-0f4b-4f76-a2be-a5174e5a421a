[build-system]
requires = ["setuptools==72.1.0", "wheel", "setuptools_scm"]
build-backend = "setuptools.build_meta"

[project]
name = "paddleocr"
# After each version release, the version number needs to be incremented
dynamic = ["version"]
description = "Awesome OCR toolkits based on PaddlePaddle(8.6M ultra-lightweight pre-trained model, support training and deployment among server, mobile, embedded and IoT devices)"
authors = [
    {name = "PaddlePaddle", email = "<EMAIL>"},
]
maintainers = [
    {name = "PaddlePaddle", email = "<EMAIL>"},
]
readme = "README.md"
requires-python = ">=3.8"
keywords = [
    "ocr",
    "textdetection",
    "textrecognition",
    "paddleocr",
    "crnn",
    "east",
    "star-net",
    "rosetta",
    "ocrlite",
    "db",
    "chineseocr",
    "chinesetextdetection",
    "chinesetextrecognition",
]
license = {text = "Apache License 2.0"}
classifiers = [
    "Intended Audience :: Developers",
    "Operating System :: OS Independent",
    "Natural Language :: Chinese (Simplified)",
    "Programming Language :: Python :: 3",
    "Topic :: Utilities",
]
dependencies = [
    "shapely",
    "scikit-image",
    "imgaug",
    "pyclipper",
    "lmdb",
    "tqdm",
    "numpy<2.0",
    "rapidfuzz",
    "opencv-python",
    "opencv-contrib-python",
    "cython",
    "Pillow",
    "pyyaml",
    "python-docx",
    "beautifulsoup4",
    "fonttools>=4.24.0",
    "fire>=0.3.0",
    "requests",
    "albumentations==1.4.10",
    "albucore==0.0.13"
]

[project.urls]
homepage = "https://github.com/PaddlePaddle/PaddleOCR"
documentation = "https://github.com/PaddlePaddle/PaddleOCR/blob/main/README.md"
repository = "https://github.com/PaddlePaddle/PaddleOCR.git"
issues = "https://github.com/PaddlePaddle/PaddleOCR/issues"

[project.scripts]
paddleocr = "paddleocr.paddleocr:main"

[tool.setuptools]
packages = ["paddleocr"]
package-dir = { "paddleocr" = "" }
include-package-data = true

[tool.setuptools_scm]
version_scheme = 'release-branch-semver'
