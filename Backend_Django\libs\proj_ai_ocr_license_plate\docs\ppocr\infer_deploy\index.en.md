---
comments: true
typora-copy-images-to: images
---

# PP-OCR Deployment

## Paddle Deployment Introduction

Paddle provides a variety of deployment schemes to meet the deployment requirements of different scenarios. Please choose according to the actual situation:

![img](./images/deployment_en.jpg)

PP-OCR has supported muti deployment schemes. Click the link to get the specific tutorial.

- [Python Inference](./python_infer.en.md)
- [C++ Inference](./cpp_infer.en.md)
- [Serving (Python/C++)](./paddle_server.en.md)
- [Paddle-Li<PERSON> (ARM CPU/OpenCL ARM GPU)](./lite.en.md)
- [Paddle.js](./paddle_js.en.md)
- [Jetson Inference](./Jetson_infer.en.md)
- [Paddle2ONNX](./paddle2onnx.en.md)

If you need the deployment tutorial of academic algorithm models other than PP-OCR, please directly enter the main page of corresponding algorithms, [entrance](../../algorithm/overview.en.md)。
