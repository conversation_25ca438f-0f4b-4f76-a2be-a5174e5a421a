# Generated by Django 5.2.1 on 2025-07-04 05:15

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("vision_app", "0022_remove_exampleimage_folder_and_more"),
    ]

    operations = [
        migrations.AlterUniqueTogether(
            name="aimodel",
            unique_together=set(),
        ),
        migrations.AddField(
            model_name="aimodel",
            name="feature_matching_role",
            field=models.CharField(
                blank=True,
                choices=[
                    ("extractor", "Extractor Model"),
                    ("matcher", "Matcher Model"),
                ],
                help_text="Role of the Feature Matching model (e.g., extractor, matcher). Null if not a Feature Matching model.",
                max_length=20,
                null=True,
            ),
        ),
        migrations.AlterUniqueTogether(
            name="aimodel",
            unique_together={
                (
                    "name",
                    "model_type",
                    "is_system_model",
                    "ocr_role",
                    "feature_matching_role",
                )
            },
        ),
    ]
