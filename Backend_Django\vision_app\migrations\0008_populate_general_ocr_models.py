from django.db import migrations
from django.utils import timezone

def populate_general_ocr_models(apps, schema_editor):
    AIModel = apps.get_model('vision_app', 'AIModel')
    db_alias = schema_editor.connection.alias

    # 通用OCR检测模型 (中文)
    AIModel.objects.using(db_alias).create(
        name='general_ocr_ch', # Corresponds to frontend value
        description='General Chinese OCR detection model (PaddleOCR)',
        # Path relative to SYSTEM_MODELS_ROOT. Predictor will append '/inference'
        model_file='ocr/general_ocr_ch/official_det_model', 
        version='1.0', # Adjust version as needed
        model_type='ocr',
        is_system_model=True,
        ocr_role='detection',
        uploaded_at=timezone.now()
    )

    # 通用OCR识别模型 (中文)
    AIModel.objects.using(db_alias).create(
        name='general_ocr_ch', # Corresponds to frontend value
        description='General Chinese OCR recognition model (PaddleOCR)',
        # Path relative to SYSTEM_MODELS_ROOT. Predictor will append '/inference'
        model_file='ocr/general_ocr_ch/official_rec_model',
        version='1.0', # Adjust version as needed
        model_type='ocr',
        is_system_model=True,
        ocr_role='recognition',
        uploaded_at=timezone.now()
    )

class Migration(migrations.Migration):

    dependencies = [
        # Ensure this points to your latest existing migration file for AIModel
        ('vision_app', '0007_populate_initial_ocr_models'), 
    ]

    operations = [
        migrations.RunPython(populate_general_ocr_models),
    ]
