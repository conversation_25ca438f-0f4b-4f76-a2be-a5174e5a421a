"""
网络配置模块

统一管理网络相关配置，包括端口、IP地址、CORS等。
支持环境变量覆盖和动态配置。
"""

import os
from typing import Dict, Any, List, Union
from pathlib import Path


class NetworkConfigManager:
    """
    网络配置管理器
    
    统一管理所有网络相关配置，支持环境变量覆盖。
    """
    
    def __init__(self):
        """初始化网络配置管理器"""
        self._frontend_ports = None
        self._backend_ports = None
        self._cors_origins = None
        self._allowed_hosts = None
        self._websocket_config = None
    
    @property
    def frontend_ports(self) -> Dict[str, int]:
        """获取前端端口配置"""
        if self._frontend_ports is None:
            self._frontend_ports = {
                'dev_server': int(os.getenv('FRONTEND_DEV_PORT', '5173')),
                'production': int(os.getenv('FRONTEND_PROD_PORT', '8080')),
                'preview': int(os.getenv('FRONTEND_PREVIEW_PORT', '4173')),
            }
        return self._frontend_ports
    
    @property
    def backend_ports(self) -> Dict[str, int]:
        """获取后端端口配置"""
        if self._backend_ports is None:
            django_port = int(os.getenv('BACKEND_PORT', '8000'))
            self._backend_ports = {
                'django': django_port,
                'websocket': int(os.getenv('WEBSOCKET_PORT', str(django_port + 1))),
            }
        return self._backend_ports
    
    @property
    def cors_origins(self) -> List[str]:
        """获取CORS允许的源地址列表"""
        if self._cors_origins is None:
            # 从环境变量获取自定义CORS源（逗号分隔）
            custom_origins = os.getenv('CORS_ORIGINS', '').strip()
            if custom_origins:
                self._cors_origins = [origin.strip() for origin in custom_origins.split(',')]
            else:
                # 默认CORS配置
                self._cors_origins = self._get_default_cors_origins()
        return self._cors_origins
    
    @property
    def allowed_hosts(self) -> List[str]:
        """获取Django允许的主机列表"""
        if self._allowed_hosts is None:
            # 从环境变量获取允许的主机（逗号分隔）
            custom_hosts = os.getenv('ALLOWED_HOSTS', '').strip()
            if custom_hosts:
                self._allowed_hosts = [host.strip() for host in custom_hosts.split(',')]
            else:
                # 默认允许的主机
                self._allowed_hosts = self._get_default_allowed_hosts()
        return self._allowed_hosts
    
    @property
    def websocket_config(self) -> Dict[str, Any]:
        """获取WebSocket配置"""
        if self._websocket_config is None:
            self._websocket_config = {
                'path': os.getenv('WEBSOCKET_PATH', '/ws/'),
                'scanner_path': os.getenv('WEBSOCKET_SCANNER_PATH', '/ws/scanner/'),
                'capacity': int(os.getenv('WEBSOCKET_CAPACITY', '300')),
                'expiry': int(os.getenv('WEBSOCKET_EXPIRY', '1')),
            }
        return self._websocket_config
    
    def _get_default_cors_origins(self) -> List[str]:
        """获取默认的CORS源地址列表"""
        origins = []
        
        # 本地开发地址
        for port in [self.frontend_ports['dev_server'], self.frontend_ports['production']]:
            origins.extend([
                f"http://localhost:{port}",
                f"http://127.0.0.1:{port}",
            ])
        
        # 从环境变量获取局域网IP
        lan_ips = self._get_lan_ips()
        for ip in lan_ips:
            for port in [self.frontend_ports['dev_server'], self.frontend_ports['production']]:
                origins.append(f"http://{ip}:{port}")
        
        return origins
    
    def _get_default_allowed_hosts(self) -> List[str]:
        """获取默认的允许主机列表"""
        hosts = ['localhost', '127.0.0.1', '*']
        
        # 添加局域网IP
        hosts.extend(self._get_lan_ips())
        
        return hosts
    
    def _get_lan_ips(self) -> List[str]:
        """从环境变量获取局域网IP地址列表"""
        lan_ips_env = os.getenv('LAN_IPS', '').strip()
        if lan_ips_env:
            return [ip.strip() for ip in lan_ips_env.split(',')]
        
        # 默认常用的局域网IP段（可以通过环境变量覆盖）
        return []
    
    def get_frontend_url(self, environment: str = 'development') -> str:
        """
        获取前端访问URL
        
        Args:
            environment: 环境类型 ('development', 'production', 'docker')
            
        Returns:
            前端访问URL
        """
        if environment == 'development':
            port = self.frontend_ports['dev_server']
            host = os.getenv('FRONTEND_DEV_HOST', 'localhost')
        elif environment == 'docker':
            port = self.frontend_ports['production']
            host = os.getenv('FRONTEND_DOCKER_HOST', 'localhost')
        else:  # production
            port = self.frontend_ports['production']
            host = os.getenv('FRONTEND_PROD_HOST', 'localhost')
        
        protocol = 'https' if os.getenv('USE_HTTPS', 'false').lower() == 'true' else 'http'
        return f"{protocol}://{host}:{port}"
    
    def get_backend_url(self, environment: str = 'development') -> str:
        """
        获取后端访问URL
        
        Args:
            environment: 环境类型 ('development', 'production', 'docker')
            
        Returns:
            后端访问URL
        """
        port = self.backend_ports['django']
        
        if environment == 'docker':
            host = os.getenv('BACKEND_DOCKER_HOST', 'backend')  # Docker容器名
        else:
            host = os.getenv('BACKEND_HOST', 'localhost')
        
        protocol = 'https' if os.getenv('USE_HTTPS', 'false').lower() == 'true' else 'http'
        return f"{protocol}://{host}:{port}"
    
    def get_websocket_url(self, environment: str = 'development') -> str:
        """
        获取WebSocket连接URL
        
        Args:
            environment: 环境类型 ('development', 'production', 'docker')
            
        Returns:
            WebSocket连接URL
        """
        backend_url = self.get_backend_url(environment)
        ws_protocol = 'wss' if backend_url.startswith('https') else 'ws'
        base_url = backend_url.replace('http://', '').replace('https://', '')
        
        return f"{ws_protocol}://{base_url}{self.websocket_config['scanner_path']}"

    def get_django_bind_address(self, environment: str = 'development') -> str:
        """
        获取Django服务器绑定地址

        Args:
            environment: 环境类型 ('development', 'production', 'docker')

        Returns:
            Django绑定地址 (host:port)
        """
        port = self.backend_ports['django']

        # 检查是否强制使用localhost
        force_localhost = os.getenv('DJANGO_BIND_LOCALHOST', 'false').lower() == 'true'

        if force_localhost or environment == 'development':
            # 开发环境或强制localhost时，只绑定到本地
            host = '127.0.0.1'
        elif environment == 'docker':
            # Docker环境绑定到所有接口
            host = '0.0.0.0'
        else:
            # 生产环境根据配置决定
            host = os.getenv('DJANGO_BIND_HOST', '0.0.0.0')

        return f"{host}:{port}"
    
    def get_django_network_configs(self) -> Dict[str, Any]:
        """
        获取Django网络相关配置
        
        Returns:
            Django网络配置字典
        """
        return {
            'ALLOWED_HOSTS': self.allowed_hosts,
            'CORS_ALLOWED_ORIGINS': self.cors_origins,
            'CSRF_TRUSTED_ORIGINS': self.cors_origins,
            'CORS_ALLOW_CREDENTIALS': True,
            'CORS_ALLOW_ALL_ORIGINS': os.getenv('CORS_ALLOW_ALL_ORIGINS', 'False').lower() == 'true',
            
            # WebSocket配置
            'CHANNEL_LAYERS': {
                "default": {
                    "BACKEND": "channels.layers.InMemoryChannelLayer",
                    "CONFIG": {
                        "capacity": self.websocket_config['capacity'],
                        "expiry": self.websocket_config['expiry'],
                    },
                }
            },
        }


    def validate_config(self) -> Dict[str, Any]:
        """
        验证网络配置的有效性

        Returns:
            验证结果字典
        """
        issues = []
        warnings = []

        # 检查端口冲突
        all_ports = list(self.frontend_ports.values()) + list(self.backend_ports.values())
        if len(all_ports) != len(set(all_ports)):
            issues.append("端口配置存在冲突")

        # 检查CORS配置
        if not self.cors_origins:
            warnings.append("CORS源地址列表为空，可能影响跨域访问")

        # 检查允许的主机
        if '*' in self.allowed_hosts and os.getenv('DJANGO_ENV', 'development') == 'production':
            warnings.append("生产环境不建议使用通配符主机配置")

        return {
            'valid': len(issues) == 0,
            'issues': issues,
            'warnings': warnings,
            'config_summary': {
                'frontend_ports': self.frontend_ports,
                'backend_ports': self.backend_ports,
                'cors_origins_count': len(self.cors_origins),
                'allowed_hosts_count': len(self.allowed_hosts),
            }
        }

    def get_config_info(self) -> Dict[str, Any]:
        """
        获取配置信息摘要

        Returns:
            配置信息字典
        """
        return {
            'frontend_urls': {
                'development': self.get_frontend_url('development'),
                'production': self.get_frontend_url('production'),
                'docker': self.get_frontend_url('docker'),
            },
            'backend_urls': {
                'development': self.get_backend_url('development'),
                'production': self.get_backend_url('production'),
                'docker': self.get_backend_url('docker'),
            },
            'websocket_urls': {
                'development': self.get_websocket_url('development'),
                'production': self.get_websocket_url('production'),
                'docker': self.get_websocket_url('docker'),
            },
            'ports': {
                'frontend': self.frontend_ports,
                'backend': self.backend_ports,
            },
            'cors_origins': self.cors_origins,
            'allowed_hosts': self.allowed_hosts,
        }


    def validate_config(self) -> Dict[str, Any]:
        """
        验证网络配置的有效性

        Returns:
            验证结果字典
        """
        issues = []
        warnings = []

        # 检查端口冲突
        all_ports = list(self.frontend_ports.values()) + list(self.backend_ports.values())
        if len(all_ports) != len(set(all_ports)):
            issues.append("端口配置存在冲突")

        # 检查CORS配置
        if not self.cors_origins:
            warnings.append("CORS源地址列表为空，可能影响跨域访问")

        # 检查允许的主机
        if '*' in self.allowed_hosts and os.getenv('DJANGO_ENV', 'development') == 'production':
            warnings.append("生产环境不建议使用通配符主机配置")

        return {
            'valid': len(issues) == 0,
            'issues': issues,
            'warnings': warnings,
            'config_summary': {
                'frontend_ports': self.frontend_ports,
                'backend_ports': self.backend_ports,
                'cors_origins_count': len(self.cors_origins),
                'allowed_hosts_count': len(self.allowed_hosts),
            }
        }


# 全局网络配置管理器实例
network_config = NetworkConfigManager()


def get_network_configs() -> Dict[str, Any]:
    """
    获取网络配置的便捷函数

    Returns:
        网络配置字典
    """
    return network_config.get_django_network_configs()


def validate_network_config() -> Dict[str, Any]:
    """
    验证网络配置的便捷函数

    Returns:
        验证结果字典
    """
    return network_config.validate_config()


def get_network_info() -> Dict[str, Any]:
    """
    获取网络配置信息的便捷函数

    Returns:
        网络配置信息字典
    """
    return {
        'frontend_urls': {
            'development': network_config.get_frontend_url('development'),
            'production': network_config.get_frontend_url('production'),
            'docker': network_config.get_frontend_url('docker'),
        },
        'backend_urls': {
            'development': network_config.get_backend_url('development'),
            'production': network_config.get_backend_url('production'),
            'docker': network_config.get_backend_url('docker'),
        },
        'websocket_urls': {
            'development': network_config.get_websocket_url('development'),
            'production': network_config.get_websocket_url('production'),
            'docker': network_config.get_websocket_url('docker'),
        },
        'ports': {
            'frontend': network_config.frontend_ports,
            'backend': network_config.backend_ports,
        },
        'cors_origins': network_config.cors_origins,
        'allowed_hosts': network_config.allowed_hosts,
    }


def validate_network_config() -> Dict[str, Any]:
    """
    验证网络配置的便捷函数

    Returns:
        验证结果字典
    """
    return network_config.validate_config()


def get_network_info() -> Dict[str, Any]:
    """
    获取网络配置信息的便捷函数

    Returns:
        网络配置信息字典
    """
    return network_config.get_config_info()
