Global:
  use_gpu: true
  epoch_num: 5
  log_smooth_window: 20
  print_batch_step: 20
  save_model_dir: ./output/rec/rec_r31_robustscanner/
  save_epoch_step: 1
  # evaluation is run every 2000 iterations
  eval_batch_step: [0, 2000]
  cal_metric_during_train: True
  pretrained_model:
  checkpoints:
  save_inference_dir:
  use_visualdl: False
  infer_img: ./inference/rec_inference
  # for data or label process
  character_dict_path: ppocr/utils/dict90.txt
  max_text_length: &max_text_length 40
  infer_mode: False
  use_space_char: False
  rm_symbol: True
  save_res_path: ./output/rec/predicts_robustscanner.txt

Optimizer:
  name: <PERSON>
  beta1: 0.9
  beta2: 0.999
  lr:
    name: Piecewise
    decay_epochs: [3, 4]
    values: [0.001, 0.0001, 0.00001] 
  regularizer:
    name: 'L2'
    factor: 0

Architecture:
  model_type: rec
  algorithm: RobustScanner
  Transform:
  Backbone:
    name: ResNet31
    init_type: KaimingNormal
  Head:
    name: RobustScannerHead
    enc_outchannles: 128
    hybrid_dec_rnn_layers: 2
    hybrid_dec_dropout: 0
    position_dec_rnn_layers: 2
    start_idx: 91
    mask: True
    padding_idx: 92
    encode_value: False
    max_text_length: *max_text_length

Loss:
  name: SARLoss

PostProcess:
  name: SARLabelDecode

Metric:
  name: RecMetric
  is_filter: True


Train:
  dataset:
    name: SimpleDataSet
    data_dir: ./train_data/ic15_data/
    label_file_list: ["./train_data/ic15_data/rec_gt_train.txt"]
    transforms:
      - DecodeImage: # load image
          img_mode: BGR
          channel_first: False
      - SARLabelEncode: # Class handling label
      - RobustScannerRecResizeImg:
          image_shape: [3, 48, 48, 160] # h:48 w:[48,160]
          width_downsample_ratio: 0.25
          max_text_length: *max_text_length
      - KeepKeys:
          keep_keys: ['image', 'label', 'valid_ratio', 'word_positons'] # dataloader will return list in this order
  loader:
    shuffle: True
    batch_size_per_card: 16
    drop_last: True
    num_workers: 0
    use_shared_memory: False

Eval:
  dataset:
    name: SimpleDataSet
    data_dir: ./train_data/ic15_data
    label_file_list: ["./train_data/ic15_data/rec_gt_test.txt"]
    transforms:
      - DecodeImage: # load image
          img_mode: BGR
          channel_first: False
      - SARLabelEncode: # Class handling label
      - RobustScannerRecResizeImg:
          image_shape: [3, 48, 48, 160]
          max_text_length: *max_text_length
          width_downsample_ratio: 0.25
      - KeepKeys:
          keep_keys: ['image', 'label', 'valid_ratio', 'word_positons'] # dataloader will return list in this order
  loader:
    shuffle: False
    drop_last: False
    batch_size_per_card: 16
    num_workers: 0
    use_shared_memory: False
  
