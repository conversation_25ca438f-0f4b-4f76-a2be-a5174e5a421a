# 生成Nginx配置文件脚本
# 根据环境变量生成nginx配置

param(
    [string]$Template = "nginx-hotreload.conf.template",
    [string]$Output = "nginx-hotreload.conf",
    [string]$BackendHost = $env:BACKEND_DOCKER_HOST,
    [string]$BackendPort = $env:BACKEND_PORT,
    [switch]$Help
)

if ($Help) {
    Write-Host @"
生成Nginx配置文件

用法:
  .\generate-nginx-config.ps1 [参数]

参数:
  -Template <文件>     模板文件路径 (默认: nginx-hotreload.conf.template)
  -Output <文件>       输出文件路径 (默认: nginx-hotreload.conf)
  -BackendHost <主机>  后端主机名 (默认: 从环境变量BACKEND_DOCKER_HOST获取)
  -BackendPort <端口>  后端端口 (默认: 从环境变量BACKEND_PORT获取)
  -Help               显示此帮助信息

环境变量:
  BACKEND_DOCKER_HOST  Docker环境后端主机名 (默认: backend)
  BACKEND_PORT         后端端口 (默认: 8000)

示例:
  .\generate-nginx-config.ps1
  .\generate-nginx-config.ps1 -BackendHost "backend" -BackendPort "8000"
  .\generate-nginx-config.ps1 -Template "nginx.conf.template" -Output "nginx.conf"
"@
    exit 0
}

# 设置默认值
if (-not $BackendHost) {
    $BackendHost = if ($env:BACKEND_DOCKER_HOST) { $env:BACKEND_DOCKER_HOST } else { "backend" }
}

if (-not $BackendPort) {
    $BackendPort = if ($env:BACKEND_PORT) { $env:BACKEND_PORT } else { "8000" }
}

# 获取脚本所在目录
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$DockerDir = Split-Path -Parent $ScriptDir

# 构建完整路径
$TemplatePath = Join-Path $DockerDir $Template
$OutputPath = Join-Path $DockerDir $Output

Write-Host "=== 生成Nginx配置文件 ===" -ForegroundColor Green
Write-Host "模板文件: $TemplatePath" -ForegroundColor Cyan
Write-Host "输出文件: $OutputPath" -ForegroundColor Cyan
Write-Host "后端主机: $BackendHost" -ForegroundColor Yellow
Write-Host "后端端口: $BackendPort" -ForegroundColor Yellow

# 检查模板文件是否存在
if (-not (Test-Path $TemplatePath)) {
    Write-Host "错误: 模板文件不存在: $TemplatePath" -ForegroundColor Red
    exit 1
}

try {
    # 读取模板文件
    $templateContent = Get-Content $TemplatePath -Raw -Encoding UTF8
    
    # 替换环境变量
    $configContent = $templateContent -replace '\$\{BACKEND_HOST\}', $BackendHost
    $configContent = $configContent -replace '\$\{BACKEND_PORT\}', $BackendPort
    
    # 写入输出文件
    $configContent | Out-File -FilePath $OutputPath -Encoding UTF8 -NoNewline
    
    Write-Host "✅ 配置文件生成成功: $OutputPath" -ForegroundColor Green
    
    # 显示配置摘要
    Write-Host "`n配置摘要:" -ForegroundColor Yellow
    Write-Host "  上游服务器: $BackendHost`:$BackendPort" -ForegroundColor White
    Write-Host "  API代理: /api/ -> http://$BackendHost`:$BackendPort" -ForegroundColor White
    Write-Host "  WebSocket: /ws/ -> http://$BackendHost`:$BackendPort" -ForegroundColor White
    Write-Host "  媒体文件: /media/ -> http://$BackendHost`:$BackendPort" -ForegroundColor White
    
} catch {
    Write-Host "错误: 生成配置文件失败: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host "`n提示: 重启nginx容器以应用新配置" -ForegroundColor Cyan
