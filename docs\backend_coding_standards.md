# 后端代码规范 (Python/FastAPI)

本文档旨在为 AI 视觉应用后端开发提供一套统一的代码规范和最佳实践，以确保代码质量、一致性和可维护性。所有后端开发人员应遵循此规范。

## 1. 语言与环境

*   **语言版本**: Python 3.9 或更高版本。
*   **虚拟环境**: 强制使用虚拟环境 (如 `venv`) 管理项目依赖。
*   **依赖管理**: 使用 `pip` 和 `requirements.txt`。运行 `pip freeze > requirements.txt` 定期更新。考虑未来迁移到 `poetry` 或 `pdm`。

## 2. 代码风格与格式化

*   **基础**: 严格遵循 [PEP 8](https://peps.python.org/pep-0008/) 规范。
*   **格式化**: 强制使用 `black` 进行代码自动格式化。
*   **Linting**: 使用 `ruff` (推荐，集成 linter 和 formatter) 或 `flake8` 进行代码质量检查。配置应包含在项目根目录 (如 `pyproject.toml` 或 `.flake8`)。
*   **导入顺序**: 使用 `isort` (或 `ruff` 内置功能) 对 import 语句进行排序和分组。

## 3. 命名规范

*   **变量/函数/方法/模块**: 小写字母，单词间用下划线分隔 (`snake_case`)。
*   **类名**: 每个单词首字母大写 (`PascalCase`)。
*   **常量**: 全部大写字母，单词间用下划线分隔 (`UPPER_SNAKE_CASE`)。
*   **私有成员**: 以单下划线 `_` 开头 (非强制，更多是约定)。
*   **避免单字母变量名** (循环变量 `i`, `j`, `k` 除外)，使用有意义的名称。

## 4. API 设计 (FastAPI)

*   **RESTful 原则**: 遵循 RESTful 设计原则进行 API 设计。
*   **URL 命名**:
    *   使用名词复数表示资源集合 (e.g., `/users`, `/models`)。
    *   使用 `/resource/{id}` 获取特定资源。
    *   URL 使用小写字母，单词间用连字符 `-` (kebab-case) 或下划线 `_` (snake_case)，推荐 `snake_case` 与 Python 代码保持一致。
    *   API 版本控制 (可选，初期可省略): `/api/v1/...`
*   **请求方法**: 正确使用 HTTP 方法 (GET, POST, PUT, DELETE, PATCH)。
*   **请求/响应模型**:
    *   强制使用 Pydantic 模型 (`schemas` 模块) 定义 API 的请求体和响应体。
    *   清晰定义模型字段类型和验证规则。
    *   响应体应包含明确的成功/失败状态和数据/错误信息。
*   **状态码**: 合理使用 HTTP 状态码 (200, 201, 400, 401, 403, 404, 500 等)。
*   **文档**: 利用 FastAPI 自动生成的 Swagger UI 和 ReDoc，确保所有 API 端点都有清晰的描述 (`description`)、摘要 (`summary`) 和参数/响应说明。

## 5. 类型提示

*   **强制使用**: 所有函数、方法签名和关键变量必须添加类型提示 (Type Hinting)。
*   **类型检查**: 考虑使用 `mypy` 进行静态类型检查。

## 6. 数据库交互 (SQLAlchemy)

*   **ORM/Core**: 使用 SQLAlchemy Core 或 ORM 进行数据库操作。
*   **模型定义**: 数据库表结构在 `models` 模块中定义。
*   **CRUD 操作**: 数据库的增删改查逻辑应封装在 `crud` 模块的函数中，避免在 API 路由处理函数中直接编写复杂的数据库查询。
*   **会话管理**: 使用 FastAPI 的依赖注入管理数据库会话 (`Session`) 的生命周期。
*   **异步**: 优先使用异步数据库操作 (如 `databases` 库或 SQLAlchemy 2.0+ 的异步支持) 配合 FastAPI 的异步特性。

## 7. 安全

*   **密码存储**: 绝不允许存储明文密码。使用 `passlib` 配合 `bcrypt` 对密码进行哈希存储和验证。
*   **认证**: 使用 JWT (JSON Web Tokens) 进行用户认证。密钥 (`SECRET_KEY`) 必须保密，并通过环境变量配置。
*   **授权**: 实现基于角色的访问控制 (RBAC)，确保用户只能访问其权限范围内的资源和操作。
*   **输入验证**: 严格验证所有来自客户端的输入数据 (通过 Pydantic 模型)。
*   **依赖安全**: 定期检查项目依赖是否存在已知的安全漏洞。

## 8. 错误处理与日志

*   **异常处理**: 使用 FastAPI 的异常处理机制 (`@app.exception_handler`) 捕获和处理应用中的异常，返回统一格式的错误响应。
*   **日志**:
    *   使用 Python 内置 `logging` 模块。
    *   推荐使用**结构化日志** (如 JSON 格式)，方便后续处理和分析。库如 `python-json-logger` 或 `structlog`。
    *   日志级别: 合理使用 `DEBUG`, `INFO`, `WARNING`, `ERROR`, `CRITICAL`。
    *   记录内容: API 请求/响应概要、关键业务逻辑步骤、数据库操作、耗时操作、异常信息及堆栈跟踪。
    *   配置日志输出到控制台或文件，考虑与 Docker/集中式日志系统集成。

## 9. 测试

*   **框架**: 使用 `pytest` 作为测试框架。
*   **覆盖率**: 追求高测试覆盖率，特别是核心业务逻辑和 API 端点。
*   **测试类型**: 编写单元测试 (针对独立函数/类) 和集成测试 (针对 API 端点和数据库交互)。
*   **数据库测试**: 测试时使用独立的测试数据库或 Mock 数据。

## 10. 项目结构 (建议)

```
Backend/
├── app/
│   ├── api/              # API 路由模块 (e.g., auth.py, models.py)
│   ├── core/             # 核心配置、安全相关 (e.g., config.py, security.py)
│   ├── crud/             # 数据库 CRUD 操作 (e.g., crud_user.py)
│   ├── db/               # 数据库连接、会话管理 (e.g., session.py)
│   ├── models/           # SQLAlchemy 模型 (e.g., user.py)
│   ├── schemas/          # Pydantic 模型 (e.g., user.py, token.py)
│   ├── services/         # (可选) 复杂业务逻辑层
│   └── __init__.py
├── tests/                # 测试代码
├── venv/                 # 虚拟环境 (加入 .gitignore)
├── .env                  # 环境变量 (加入 .gitignore)
├── .env.example          # 环境变量模板
├── .gitignore
├── main.py               # FastAPI 应用入口
├── requirements.txt
└── README.md             # 后端项目说明
```

## 11. 文档与注释

*   **代码注释**: 对复杂或不直观的代码段添加必要的注释。
*   **Docstrings**: 为所有公共模块、类、函数和方法编写清晰的 Docstrings (遵循 PEP 257，推荐 Google 或 NumPy 风格)。
*   **README**: 维护 `Backend/README.md`，说明如何设置、运行和测试后端服务。

---

*本文档应随着项目进展和技术演进持续更新。* 