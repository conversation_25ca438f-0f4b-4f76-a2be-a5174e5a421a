---
description: Explore the OBBValidator for YOLO, an advanced class for oriented bounding boxes (OBB). Learn initialization, processes, and evaluation methods.
keywords: Ultralytics, YOLO, OBBValidator, Oriented Bounding Boxes, DetectionValidator, validation, Python, deep learning
---

# Reference for `ultralytics/models/yolo/obb/val.py`

!!! note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/yolo/obb/val.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/yolo/obb/val.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/models/yolo/obb/val.py) 🛠️. Thank you 🙏!

<br>

## ::: ultralytics.models.yolo.obb.val.OBBValidator

<br><br>
