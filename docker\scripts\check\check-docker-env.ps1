# AI Vision App Environment Check Script (English)

Write-Host "=== AI Vision App Environment Check ===" -ForegroundColor Green
Write-Host ""

# Check Docker version
Write-Host "Docker Version:" -ForegroundColor Yellow
docker --version

# Check image status
Write-Host ""
Write-Host "Docker Images Status:" -ForegroundColor Yellow
$frontendImage = docker images -q web_ai_vision_app-frontend:latest
$backendImage = docker images -q web_ai_vision_app-backend:latest

if ($frontendImage) {
    Write-Host "✓ Frontend image exists: $frontendImage" -ForegroundColor Green
} else {
    Write-Host "✗ Frontend image not found" -ForegroundColor Red
}

if ($backendImage) {
    Write-Host "✓ Backend image exists: $backendImage" -ForegroundColor Green
} else {
    Write-Host "✗ Backend image not found" -ForegroundColor Red
}

# Check container status
Write-Host ""
Write-Host "Container Status:" -ForegroundColor Yellow
docker ps -a | Select-String "ai-vision"

# Check docker-compose file
Write-Host ""
Write-Host "Docker-compose Configuration Check:" -ForegroundColor Yellow

# Get the docker directory path (two levels up from scripts/check/)
$dockerDir = Split-Path (Split-Path $PSScriptRoot -Parent) -Parent
$composeFile = Join-Path $dockerDir "docker-compose.yml"
$hotreloadComposeFile = Join-Path $dockerDir "docker-compose.hotreload.yml"

if (Test-Path $composeFile) {
    Write-Host "✓ docker-compose.yml exists" -ForegroundColor Green
    Push-Location $dockerDir
    docker-compose config --quiet 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ docker-compose.yml configuration is valid" -ForegroundColor Green
    } else {
        Write-Host "✗ docker-compose.yml configuration has errors" -ForegroundColor Red
    }
    Pop-Location
} else {
    Write-Host "✗ docker-compose.yml not found at: $composeFile" -ForegroundColor Red
}

if (Test-Path $hotreloadComposeFile) {
    Write-Host "✓ docker-compose.hotreload.yml exists" -ForegroundColor Green
} else {
    Write-Host "✗ docker-compose.hotreload.yml not found" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "Environment check completed!" -ForegroundColor Green
