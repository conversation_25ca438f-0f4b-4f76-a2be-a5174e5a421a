from user_utils.proj_offline_data_augmentation.OCR import ocrDet
from pathlib import Path

if __name__ == "__main__":
    det = ocrDet(suffix=".png")
    # 分配数据集
    det.Distribute_Data_Set({
        "mindeo/data/common/CCPD2019/ccpd_base" : 1.0,
        "mindeo/data/common/CCPD2019/ccpd_blur" : 1.0,
        "mindeo/data/common/CCPD2019/ccpd_challenge" : 1.0,
        "mindeo/data/common/CCPD2019/ccpd_db" : 1.0,
        "mindeo/data/common/CCPD2019/ccpd_fn" : 1.0,
        "mindeo/data/common/CCPD2019/ccpd_rotate" : 1.0,
        "mindeo/data/common/CCPD2019/ccpd_tilt" : 1.0,
        "mindeo/data/common/CCPD2019/ccpd_weather" : 1.0,
        "mindeo/data/common/CCPD2020/ccpd_green/test" : 1.0,
        "mindeo/data/common/CCPD2020/ccpd_green/train" : 1.0,
        "mindeo/data/common/CCPD2020/ccpd_green/val" : 1.0,
        }, 0.75, 0.2, "mindeo/data_processed/Det_Tmp/DataSet", seed=20241030, only_label=True)

    # 减少特定数据的比例
    classify_func = lambda x: x[0:1]
    for data_type in ["train", "val", "test"]:
        classified_labels_dict = det.Classify_Data([
            f"mindeo/data_processed/Det_Tmp/DataSet/{data_type}",
            ], classify_func)

        # print(data_type)
        # for id in classified_labels_dict:
        #     print(id, len(classified_labels_dict[id]))

        det.Write_Classified_Data({
            "皖": 0.01,
        }, classified_labels_dict, f"mindeo/data_processed/Det_Tmp/Classify/{data_type}", only_label=True)

    # 对图片做仿射变换
    for data_type in ["train", "val", "test"]:
        det.Affine_Transform([
            f"mindeo/data_processed/Det_Tmp/Classify/{data_type}",
            ], 1, f"mindeo/data_processed/Det_Tmp/Affine/{data_type}", angle_range=(-90, 90))

    # 添加噪声
    for data_type in ["train", "val", "test"]:
        det.Add_Noise([
            f"mindeo/data_processed/Det_Tmp/Classify/{data_type}",
            ], 0.2, f"mindeo/data_processed/Det_Tmp/Add_Noise/{data_type}")

    # 添加阴影
    for data_type in ["train", "val", "test"]:
        det.Add_Shadow([
            f"mindeo/data_processed/Det_Tmp/Affine/{data_type}",
            ], 1, f"mindeo/data_processed/Det_Tmp/Add_Shadow/{data_type}", num_circles=10, max_radius=120)

    # 裁剪图像
    for data_type in ["train", "val", "test"]:
        det.Crop_Image([
            f"mindeo/data_processed/Det_Tmp/Add_Shadow/{data_type}",
            ], [480, 480], f"mindeo/data_processed/Det_Tmp/Crop/{data_type}")

    # 缩放图像
    for data_type in ["train", "val", "test"]:
        det.Resize_Image([
            f"mindeo/data_processed/Det_Tmp/Crop/{data_type}",
            f"mindeo/data_processed/Det_Tmp/Add_Shadow/{data_type}",
            f"mindeo/data_processed/Det_Tmp/Add_Noise/{data_type}",
            ], [320, 320], f"mindeo/data_processed/Det/Train/{data_type}", keep_ratio=False)