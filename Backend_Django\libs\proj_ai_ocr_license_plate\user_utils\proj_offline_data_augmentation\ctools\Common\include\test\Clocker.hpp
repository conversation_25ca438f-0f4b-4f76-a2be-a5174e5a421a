#ifndef _CLOCKER_H
#define _CL<PERSON>KER_H

//-----------------------------------------------------------------------------
//  Includes

#include <time.h>

#include <vector>
#include <fstream>

#include "AIEngineCommon.h"
#include "Log.hpp"
//-----------------------------------------------------------------------------
//  Definitions


//-----------------------------------------------------------------------------
//  Declarations

/**
 * @brief    
 *           计时工具类
 *           
 * @date     2024-03-13 Created by <PERSON><PERSON>
 */
class Clocker {
public:
    /**
    * @brief    
    *           Clocker构造函数
    *           
    * @date     2024-03-13 Created by <PERSON><PERSON>
    */
    Clock<PERSON>(bool incremental_mode) : _incremental_mode(incremental_mode)
    {
        clock_gettime(CLOCK_REALTIME, &time_base); // 记录基准时间
    }

    /**
    * @brief    
    *           Clocker析构函数
    *           
    * @date     2024-03-13 Created by HuangJP
    */
    ~Clocker(){}

    /**
     * @brief    
     *           重置计时基准时间
     *           
     * @date     2024-03-13 Created by HuangJP
     */
    void Reset(void)
    {
        clock_gettime(CLOCK_REALTIME, &time_base); // 重置基准时间
        elapsed_time_list.clear(); // 清空计时列表
    }

    /**
     * @brief    
     *           记录经过时间，时间单位：毫秒
     *           
     * @date     2024-03-13 Created by HuangJP
     */
    void Record(void)
    {
        // 获取当前时间
        struct timespec now;
        clock_gettime(CLOCK_REALTIME, &now);

        // 计算经过时间
        long seconds = now.tv_sec - time_base.tv_sec;
        long nanoseconds = now.tv_nsec - time_base.tv_nsec;
        float elapsed = seconds * (1e+3) + nanoseconds * (1e-6);

        // 记录经过时间
        elapsed_time_list.push_back(elapsed);

        // 非递增模式下更新基准时间
        if (_incremental_mode == false)
        {
            time_base = now;
        }
    }

    /**
     * @brief    
     *           获取计时列表
     *           
     * @retval   计时列表
     *           
     * @date     2024-04-18 Created by HuangJP
     */
    std::vector<float> Get(void)
    {
        return elapsed_time_list;
    }

    /**
     * @brief    
     *           将计时列表数据写入到文件
     *           
     * @param    file_path:     记录文件路径
     *           
     * @retval   错误码
     *           
     * @date     2024-03-13 Created by HuangJP
     */
    int Write(const char *file_path)
    {
        std::ofstream fs;
        
        // 判断是否有数据需要写入
        if (elapsed_time_list.empty())
        {
            LOGI("Have no recorded data");
            return AIENGINE_NO_ERROR;
        }

        // 打开文件
        fs.open(file_path, std::ios_base::app);
        if (!fs.is_open())
        {
            LOGE("Failed to write recorded data, can't open file");
            return AIENGINE_OPEN_FILE_ERROR;
        }

        // 写入耗时数据
        auto elapsed_time = elapsed_time_list.begin();
        fs << *elapsed_time;
        while (++elapsed_time != elapsed_time_list.end())
        {
            fs << "," << *elapsed_time;
        }
        fs << std::endl;
        fs.close();

        return AIENGINE_NO_ERROR;
    }

private:
    const bool _incremental_mode;
    struct timespec time_base; // 基准时间
    std::vector<float> elapsed_time_list; // 计时列表
};
#endif
//-----------------------------------------------------------------------------
//  End of file