# Generated by Django 5.2.1 on 2025-06-18 01:28

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("vision_app", "0019_populate_ocr_collection_names"),
    ]

    operations = [
        migrations.CreateModel(
            name="ExampleImage",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        help_text="文件名，例如 'image.jpg'", max_length=255
                    ),
                ),
                (
                    "path",
                    models.CharField(
                        help_text="文件在分类目录下的相对路径，例如 'folder1/image.jpg'",
                        max_length=1024,
                    ),
                ),
                (
                    "category",
                    models.CharField(
                        choices=[
                            ("barcode", "条码"),
                            ("ocr", "OCR"),
                            ("ai_restored", "AI修复"),
                        ],
                        max_length=50,
                    ),
                ),
                (
                    "display_order",
                    models.IntegerField(
                        default=0, help_text="用于排序的字段，数值越小越靠前"
                    ),
                ),
                (
                    "description",
                    models.CharField(blank=True, max_length=512, null=True),
                ),
                (
                    "file_size",
                    models.PositiveIntegerField(
                        default=0, help_text="文件大小（字节）"
                    ),
                ),
                ("width", models.PositiveIntegerField(default=0)),
                ("height", models.PositiveIntegerField(default=0)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "ordering": ["display_order", "name"],
                "unique_together": {("category", "path")},
            },
        ),
    ]
