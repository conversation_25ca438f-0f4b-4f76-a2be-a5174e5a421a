# Ultralytics YOLO 🚀, AGPL-3.0 license
# YOLOv8 object detection model with P3-P5 outputs. For Usage examples see https://docs.ultralytics.com/tasks/detect

# Parameters
nc: 4 # number of classes
ch: 1
scales: # model compound scaling constants, i.e. 'model=yolov8n.yaml' will call yolov8.yaml with scale 'n'
  # [depth, width, max_channels]
  n: [0.25, 0.075, 512] # 进一步降低深度和宽度因子

# YOLOv8.0n backbone
backbone:
  # [from, repeats, module, args]
  - [-1, 1, Conv, [48, 3, 2]]           # 0-P1/2  # 降低初始通道
  - [-1, 1, GhostConv, [96, 3, 2]]      # 1-P2/4
  - [-1, 1, C3k2, [96, True]]           # 2
  - [-1, 1, Ghost<PERSON>onv, [192, 3, 2]]     # 3-P3/8
  - [-1, 1, C3k2, [192, True]]          # 4
  - [-1, 1, SCDown, [384, 3, 2]]        # 5-P4/16
  - [-1, 1, C3k2, [384, True]]          # 6
  - [-1, 1, SCDown, [512, 3, 2]]        # 7-P5/32  # 限制最大通道数为512
  - [-1, 1, C3k2, [512, True]]          # 8
  - [-1, 1, SPPF, [512, 5]]             # 9

# YOLOv8.0n head
head:
  - [-1, 1, nn.Upsample, [None, 2, "nearest"]]    # 10
  - [[-1, 6], 1, Concat, [1]]                     # cat backbone P4  # 11
  - [-1, 1, C3k2, [256]]                          # 12

  - [-1, 1, nn.Upsample, [None, 2, "nearest"]]    # 13
  - [[-1, 4], 1, Concat, [1]]                     # cat backbone P3  # 14
  - [-1, 1, C3k2, [128]]                          # 15 (P3/8-small)

  - [-1, 1, Conv, [128, 3, 2]]                    # 16
  - [[-1, 12], 1, Concat, [1]]                    # cat head P4  # 17
  - [-1, 1, C3k2, [256]]                          # 18 (P4/16-medium)

  - [-1, 1, SCDown, [256, 3, 2]]                  # 19
  - [[-1, 9], 1, Concat, [1]]                     # cat head P5  # 20
  - [-1, 1, C3k2, [512]]                          # 21 (P5/32-large)

  - [[15, 18, 21], 1, Detect, [nc]]               # Detect(P3, P4, P5)
