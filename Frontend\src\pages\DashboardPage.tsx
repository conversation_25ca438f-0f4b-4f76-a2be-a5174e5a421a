import React from 'react';
import { Layout } from 'antd';
import { Allotment } from "allotment"; // 导入 Allotment
import "allotment/dist/style.css"; // 导入 Allotment 的基础样式
import MenuBar from '../components/MenuBar'; // 调整路径
import StatusBar from '../components/StatusBar'; // 调整路径
import FunctionTree from '../components/FunctionTree'; // 调整路径
import ParameterPanel from '../components/ParameterPanel'; // 调整路径
import ImageDisplay from '../components/ImageDisplay'; // 调整路径
import ImageInfoPanel from '../components/ImageInfoPanel'; // 调整路径
import { ImageWorkspaceProvider } from '../contexts/ImageWorkspaceContext'; // Import the provider
import { FunctionPanelProvider } from '../contexts/FunctionPanelContext'; // Import the new provider
import { BarcodeDetectionProvider } from '../contexts/BarcodeDetectionContext'; // Import BarcodeDetectionProvider
import { OcrDetectionProvider } from '../contexts/OcrDetectionContext'; // 导入OcrDetectionProvider
import { FeatureMatchingTraditionalProvider } from '../contexts/FeatureMatchingTraditionalContext'; // 导入新功能的Provider
import { FeatureMatchingModelProvider } from '../contexts/FeatureMatchingModelContext'; // 导入模型匹配Provider
// import '../App.css'; // App.css 可能包含全局样式，可以在 App.tsx 或 index.css 引入

const { Content } = Layout;

// 这个组件现在包含你原来的主操作界面布局
const DashboardPage: React.FC = () => {
  return (
    <ImageWorkspaceProvider>
      <FunctionPanelProvider>
        <BarcodeDetectionProvider>
          <OcrDetectionProvider>
            <FeatureMatchingTraditionalProvider>
              <FeatureMatchingModelProvider>
                <Layout className="app-layout"> {/* 可以保留或修改类名 */}
                  <MenuBar />
                  <Content>
                  <Allotment>
                    {/* Left panel */}
                    {/* Increased minSize for FunctionTree */}
                    <Allotment.Pane preferredSize={230} minSize={230} maxSize={300}>
                      <FunctionTree />
                    </Allotment.Pane>

                    {/* Middle panel - Added minSize */}
                    <Allotment.Pane minSize={300}>
                      <Allotment vertical>
                        {/* Top pane for ImageDisplay - doesn't usually need minSize */}
                        <Allotment.Pane>
                          <ImageDisplay />
                        </Allotment.Pane>
                        {/* Bottom pane for ImageInfoPanel - check if 120 is enough vertically */}
                        <Allotment.Pane preferredSize={260} minSize={260} maxSize={260}>
                          <ImageInfoPanel />
                        </Allotment.Pane>
                      </Allotment>
                    </Allotment.Pane>

                    {/* Right panel - Increased minSize for ParameterPanel */}
                    <Allotment.Pane preferredSize={450} minSize={450} maxSize={600}>
                      <ParameterPanel />
                    </Allotment.Pane>
                  </Allotment>
                </Content>
                <StatusBar /> {/* StatusBar放在Layout的最后 */}
                </Layout>
              </FeatureMatchingModelProvider>
            </FeatureMatchingTraditionalProvider>
          </OcrDetectionProvider>
        </BarcodeDetectionProvider>
      </FunctionPanelProvider>
    </ImageWorkspaceProvider>
  );
};

export default DashboardPage; 