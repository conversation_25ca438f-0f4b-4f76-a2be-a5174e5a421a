"""
网络配置检查管理命令

用于验证和显示当前的网络配置设置。
"""

from django.core.management.base import BaseCommand
from django.conf import settings
import json
from backend_project.config.network_configs import network_config, validate_network_config, get_network_info


class Command(BaseCommand):
    help = '检查和显示网络配置'

    def add_arguments(self, parser):
        parser.add_argument(
            '--validate',
            action='store_true',
            help='验证网络配置的有效性',
        )
        parser.add_argument(
            '--info',
            action='store_true',
            help='显示详细的网络配置信息',
        )
        parser.add_argument(
            '--format',
            choices=['table', 'json'],
            default='table',
            help='输出格式 (默认: table)',
        )

    def handle(self, *args, **options):
        """处理命令执行"""
        
        if options['validate']:
            self.validate_config(options['format'])
        elif options['info']:
            self.show_config_info(options['format'])
        else:
            self.show_basic_config(options['format'])

    def validate_config(self, format_type):
        """验证网络配置"""
        self.stdout.write(self.style.HTTP_INFO('=== 网络配置验证 ==='))
        
        validation_result = validate_network_config()
        
        if format_type == 'json':
            self.stdout.write(json.dumps(validation_result, indent=2, ensure_ascii=False))
            return
        
        # 表格格式输出
        if validation_result['valid']:
            self.stdout.write(self.style.SUCCESS('✅ 配置验证通过'))
        else:
            self.stdout.write(self.style.ERROR('❌ 配置验证失败'))
            
        if validation_result['issues']:
            self.stdout.write(self.style.ERROR('\n问题:'))
            for issue in validation_result['issues']:
                self.stdout.write(f"  • {issue}")
                
        if validation_result['warnings']:
            self.stdout.write(self.style.WARNING('\n警告:'))
            for warning in validation_result['warnings']:
                self.stdout.write(f"  • {warning}")
        
        # 显示配置摘要
        summary = validation_result['config_summary']
        self.stdout.write(self.style.HTTP_INFO('\n配置摘要:'))
        self.stdout.write(f"  前端端口: {summary['frontend_ports']}")
        self.stdout.write(f"  后端端口: {summary['backend_ports']}")
        self.stdout.write(f"  CORS源数量: {summary['cors_origins_count']}")
        self.stdout.write(f"  允许主机数量: {summary['allowed_hosts_count']}")

    def show_config_info(self, format_type):
        """显示详细配置信息"""
        self.stdout.write(self.style.HTTP_INFO('=== 详细网络配置信息 ==='))
        
        config_info = get_network_info()
        
        if format_type == 'json':
            self.stdout.write(json.dumps(config_info, indent=2, ensure_ascii=False))
            return
        
        # 表格格式输出
        self.stdout.write(self.style.SUCCESS('\n前端访问地址:'))
        for env, url in config_info['frontend_urls'].items():
            self.stdout.write(f"  {env:12}: {url}")
            
        self.stdout.write(self.style.SUCCESS('\n后端访问地址:'))
        for env, url in config_info['backend_urls'].items():
            self.stdout.write(f"  {env:12}: {url}")
            
        self.stdout.write(self.style.SUCCESS('\nWebSocket地址:'))
        for env, url in config_info['websocket_urls'].items():
            self.stdout.write(f"  {env:12}: {url}")
            
        self.stdout.write(self.style.SUCCESS('\n端口配置:'))
        self.stdout.write(f"  前端端口: {config_info['ports']['frontend']}")
        self.stdout.write(f"  后端端口: {config_info['ports']['backend']}")
        
        self.stdout.write(self.style.SUCCESS('\nCORS允许源:'))
        for origin in config_info['cors_origins']:
            self.stdout.write(f"  • {origin}")
            
        self.stdout.write(self.style.SUCCESS('\n允许的主机:'))
        for host in config_info['allowed_hosts']:
            self.stdout.write(f"  • {host}")

    def show_basic_config(self, format_type):
        """显示基本配置信息"""
        self.stdout.write(self.style.HTTP_INFO('=== 当前网络配置 ==='))
        
        if format_type == 'json':
            config_data = {
                'frontend_ports': network_config.frontend_ports,
                'backend_ports': network_config.backend_ports,
                'cors_origins': network_config.cors_origins,
                'allowed_hosts': network_config.allowed_hosts,
                'websocket_config': network_config.websocket_config,
            }
            self.stdout.write(json.dumps(config_data, indent=2, ensure_ascii=False))
            return
        
        # 表格格式输出
        self.stdout.write(self.style.SUCCESS('端口配置:'))
        self.stdout.write(f"  前端开发端口: {network_config.frontend_ports['dev_server']}")
        self.stdout.write(f"  前端生产端口: {network_config.frontend_ports['production']}")
        self.stdout.write(f"  后端端口: {network_config.backend_ports['django']}")
        
        self.stdout.write(self.style.SUCCESS('\nCORS配置:'))
        self.stdout.write(f"  允许源数量: {len(network_config.cors_origins)}")
        if len(network_config.cors_origins) <= 10:
            for origin in network_config.cors_origins:
                self.stdout.write(f"    • {origin}")
        else:
            for origin in network_config.cors_origins[:5]:
                self.stdout.write(f"    • {origin}")
            self.stdout.write(f"    ... 还有 {len(network_config.cors_origins) - 5} 个")
        
        self.stdout.write(self.style.SUCCESS('\n主机配置:'))
        self.stdout.write(f"  允许主机数量: {len(network_config.allowed_hosts)}")
        for host in network_config.allowed_hosts[:5]:
            self.stdout.write(f"    • {host}")
        if len(network_config.allowed_hosts) > 5:
            self.stdout.write(f"    ... 还有 {len(network_config.allowed_hosts) - 5} 个")
        
        self.stdout.write(self.style.SUCCESS('\nWebSocket配置:'))
        self.stdout.write(f"  扫描器路径: {network_config.websocket_config['scanner_path']}")
        self.stdout.write(f"  容量: {network_config.websocket_config['capacity']}")
        self.stdout.write(f"  过期时间: {network_config.websocket_config['expiry']}秒")
        
        # 显示环境变量提示
        self.stdout.write(self.style.HTTP_INFO('\n环境变量配置:'))
        self.stdout.write('可通过以下环境变量自定义配置:')
        self.stdout.write('  FRONTEND_DEV_PORT, FRONTEND_PROD_PORT, BACKEND_PORT')
        self.stdout.write('  CORS_ORIGINS, ALLOWED_HOSTS, LAN_IPS')
        self.stdout.write('  WEBSOCKET_CAPACITY, WEBSOCKET_EXPIRY')
