﻿# AI模型管理系统开发指南

## 目录

1. [系统架构概述](#1-系统架构概述)
2. [数据库设计详解](#2-数据库设计详解)
3. [后端实现机制](#3-后端实现机制)
4. [前端集成方式](#4-前端集成方式)
5. [添加新模型的完整流程](#5-添加新模型的完整流程)
6. [文件结构说明](#6-文件结构说明)
7. [最佳实践和注意事项](#7-最佳实践和注意事项)

---

## 1. 系统架构概述

### 1.1 整体架构

AI模型管理系统采用前后端分离架构，通过RESTful API进行通信：

```
┌─────────────────┐    HTTP/JSON    ┌─────────────────┐
│   前端 (React)   │ ◄──────────────► │  后端 (Django)   │
│                 │                 │                 │
│ • 模型选择界面   │                 │ • 模型管理API    │
│ • 参数配置面板   │                 │ • 文件存储管理   │
│ • 推理结果展示   │                 │ • AI推理服务     │
└─────────────────┘                 └─────────────────┘
                                            │
                                            ▼
                                    ┌─────────────────┐
                                    │   文件系统       │
                                    │                 │
                                    │ • 系统模型目录   │
                                    │ • 用户模型目录   │
                                    │ • 数据库文件     │
                                    └─────────────────┘
```

### 1.2 数据流架构

模型管理的核心数据流：

1. **模型注册流程**：
   - 系统启动 → 数据库迁移 → 系统模型自动注册
   - 用户上传 → 文件验证 → 数据库记录创建 → 文件存储

2. **模型加载流程**：
   - 前端请求模型列表 → 后端查询数据库 → 返回分组模型数据
   - 用户选择模型 → 前端发送推理请求 → 后端加载模型文件 → 执行推理

3. **模型分类管理**：
   - **系统模型** (`is_system_model=True`)：预置模型，存储在 `models/system_models/`
   - **用户模型** (`is_system_model=False`)：用户上传，存储在 `models/custom_models/`

### 1.3 支持的模型类型

| 模型类型 | 描述 | 文件格式 | 推理引擎 |
|---------|------|----------|----------|
| `barcode` | 条码检测模型 | `.pt` | Ultralytics YOLO |
| `ocr` | OCR文字识别模型 | PaddleOCR格式 | PaddlePaddle |
| `ai_restored` | AI图像修复模型 | `.onnx` | ONNX Runtime |

---

## 2. 数据库设计详解

### 2.1 AIModel 数据模型

核心数据模型 `AIModel` 位于 `Backend_Django/vision_app/models.py`：

```python
class AIModel(models.Model):
    # 基础字段
    name = models.CharField(max_length=255)  # 模型名称
    description = models.TextField(blank=True, null=True)  # 模型描述
    version = models.CharField(max_length=50, blank=True, null=True)  # 版本号

    # 模型分类
    model_type = models.CharField(max_length=50)  # barcode, ocr, ai_restored
    is_system_model = models.BooleanField(default=False)  # 系统/用户模型标识

    # 文件管理
    model_file = models.FileField(
        upload_to=get_model_upload_path,
        max_length=255,
        blank=True,
        null=True
    )

    # OCR专用字段
    OCR_ROLE_CHOICES = [
        ('detection', 'Detection Model'),
        ('recognition', 'Recognition Model'),
    ]
    ocr_role = models.CharField(
        max_length=20,
        choices=OCR_ROLE_CHOICES,
        blank=True,
        null=True
    )

    # 时间戳
    uploaded_at = models.DateTimeField(default=timezone.now)

    class Meta:
        unique_together = [['name', 'model_type', 'is_system_model', 'ocr_role']]
```

### 2.2 字段详解

#### 2.2.1 核心字段

- **name**: 模型名称，用于前端显示和API调用
- **model_type**: 模型类型分类，决定模型的用途和加载方式
- **is_system_model**: 区分系统预置模型和用户上传模型
- **version**: 版本号，支持同一模型的多版本管理

#### 2.2.2 文件路径管理

`model_file` 字段的处理逻辑：

```python
def get_model_upload_path(instance, filename):
    """
    自定义上传路径函数
    返回: <model_type>/<filename>
    最终路径: MEDIA_ROOT/<model_type>/<filename>
    """
    return os.path.join(instance.model_type, filename)
```

- **系统模型**: `model_file` 存储文件名，实际路径由 `SYSTEM_MODELS_ROOT` + `model_type` + `filename` 构成
- **用户模型**: `model_file` 存储相对路径，Django自动管理完整路径

#### 2.2.3 OCR模型角色

OCR模型需要检测和识别两个组件：

- **detection**: 文字检测模型，定位文字区域
- **recognition**: 文字识别模型，识别文字内容
- 同一OCR任务需要两个角色的模型配对使用

#### 2.2.4 唯一性约束

```python
unique_together = [['name', 'model_type', 'is_system_model', 'ocr_role']]
```

确保以下组合唯一：
- 相同名称 + 相同类型 + 相同系统状态 + 相同OCR角色

### 2.3 数据库迁移历史

#### 关键迁移文件：

1. **0001_initial.py**: 初始模型创建
2. **0006_alter_aimodel_unique_together_aimodel_ocr_role_and_more.py**: 添加OCR角色字段
3. **0007_populate_initial_ocr_models.py**: 初始化车牌识别模型
4. **0013_update_ai_restorer_model_v1017.py**: AI修复模型多版本支持

#### 系统模型初始化示例：

```python
# 0013_update_ai_restorer_model_v1017.py
def add_multiple_ai_restorer_models(apps, schema_editor):
    AIModel = apps.get_model('vision_app', 'AIModel')

    models_to_add = [
        {
            'name': 'AI_Restorer_V*******',
            'description': 'AI图像修复模型 - 版本******* (稳定版)',
            'model_file': 'AI_Restorer_NCHW_1x1x256x256_V*******.onnx',
            'version': '*******'
        },
        {
            'name': 'AI_Restorer_V*******',
            'description': 'AI图像修复模型 - 版本******* (最新版)',
            'model_file': 'AI_Restorer_NCHW_1x1x256x256_V*******.onnx',
            'version': '*******'
        }
    ]

    for model_info in models_to_add:
        AIModel.objects.using(db_alias).update_or_create(
            name=model_info['name'],
            model_type='ai_restored',
            is_system_model=True,
            defaults={
                'description': model_info['description'],
                'model_file': model_info['model_file'],
                'version': model_info['version'],
                'uploaded_at': timezone.now()
            }
        )
```

---

## 3. 后端实现机制

### 3.1 API端点设计

#### 3.1.1 模型列表API

**端点**: `GET /api/vision/models/`

**查询参数**:
- `model_scope`: `system` | `custom` | `all` (默认: `system`)
- `model_type_filter`: 按模型类型过滤 (可选)

**实现代码** (`Backend_Django/vision_app/views.py`):

```python
@api_view(['GET'])
def list_models(request):
    """获取AI模型列表，并按模型类型分组"""
    model_scope = request.query_params.get('model_scope', 'system').lower()
    model_type_filter = request.query_params.get('model_type_filter', None)

    queryset = AIModel.objects.all()

    # 按范围过滤
    if model_scope == 'system':
        queryset = queryset.filter(is_system_model=True).order_by('name')
    elif model_scope == 'custom':
        queryset = queryset.filter(is_system_model=False).order_by('-uploaded_at')
    elif model_scope == 'all':
        queryset = queryset.order_by('-is_system_model', 'name')

    # 按类型过滤
    if model_type_filter:
        queryset = queryset.filter(model_type=model_type_filter)

    # 序列化并分组
    serializer = AIModelSerializer(queryset, many=True)
    grouped_models = defaultdict(list)
    for model_data in serializer.data:
        model_type_key = model_data.get('model_type', 'unknown')
        grouped_models[model_type_key].append(model_data)

    return Response(grouped_models, status=status.HTTP_200_OK)
```

**响应格式**:
```json
{
  "barcode": [
    {
      "id": 1,
      "name": "AI_ROI_Dete_NCHW_1x1x320x320_V*******",
      "version": "*******",
      "model_type": "barcode",
      "file_path": "AI_ROI_Dete_NCHW_1x1x320x320_V*******.pt",
      "is_system_model": true,
      "uploaded_at": "2025-05-29T10:30:00Z",
      "description": "系统内置条码检测模型"
    }
  ],
  "ai_restored": [
    {
      "id": 2,
      "name": "AI_Restorer_V*******",
      "version": "*******",
      "model_type": "ai_restored",
      "file_path": "AI_Restorer_NCHW_1x1x256x256_V*******.onnx",
      "is_system_model": true,
      "uploaded_at": "2025-05-29T10:30:00Z",
      "description": "AI图像修复模型 - 版本******* (最新版)"
    }
  ]
}
```

#### 3.1.2 模型上传API

**端点**: `POST /api/vision/models/upload/`

**请求格式**: `multipart/form-data`

**参数**:
- `model_file`: 模型文件 (必需)
- `name`: 模型名称 (必需)
- `model_type`: 模型类型 (必需)
- `version`: 版本号 (可选)
- `description`: 描述信息 (可选)

**实现代码**:

```python
class AIModelUploadView(APIView):
    parser_classes = (MultiPartParser, FormParser)

    def post(self, request, *args, **kwargs):
        upload_serializer = AIModelUploadSerializer(data=request.data)
        if upload_serializer.is_valid():
            validated_data = upload_serializer.validated_data

            # 创建AIModel实例
            ai_model_instance = AIModel(
                name=validated_data['name'],
                model_type=validated_data['model_type'],
                version=validated_data.get('version'),
                description=validated_data.get('description'),
                model_file=validated_data['model_file'],
                is_system_model=False
            )
            ai_model_instance.save()

            response_serializer = AIModelSerializer(ai_model_instance)
            return Response(response_serializer.data, status=status.HTTP_201_CREATED)
        else:
            return Response(upload_serializer.errors, status=status.HTTP_400_BAD_REQUEST)
```

### 3.2 模型加载机制

#### 3.2.1 系统模型加载

系统模型的路径构建逻辑：

```python
# 系统模型路径构建
if ai_model_instance.is_system_model:
    model_filename = os.path.basename(ai_model_instance.model_file.name)
    model_path_to_load = os.path.join(
        settings.SYSTEM_MODELS_ROOT,
        ai_model_instance.model_type,
        model_filename
    )
```

**配置示例** (`Backend_Django/backend_project/settings.py`):

```python
# 系统模型根目录
SYSTEM_MODELS_ROOT = BASE_DIR / 'models' / 'system_models'

# 用户模型根目录 (MEDIA_ROOT)
MEDIA_ROOT = BASE_DIR / 'models' / 'custom_models'
```

#### 3.2.2 用户模型加载

用户模型使用Django FileField的path属性：

```python
# 用户模型路径获取
if not ai_model_instance.is_system_model:
    model_path_to_load = ai_model_instance.model_file.path
```

#### 3.2.3 模型文件验证

在加载前进行文件存在性检查：

```python
# 文件存在性验证
if not os.path.exists(model_path_to_load):
    logger.error(f"Model file not found at path: {model_path_to_load}")
    return Response({
        'error': f'模型文件未找到: {model_path_to_load}'
    }, status=status.HTTP_404_NOT_FOUND)
```

### 3.3 序列化器设计

#### 3.3.1 模型序列化器

**文件**: `Backend_Django/vision_app/serializers.py`

```python
class AIModelSerializer(serializers.ModelSerializer):
    file_path = serializers.CharField(source='model_file', read_only=True)

    class Meta:
        model = AIModel
        fields = [
            'id', 'name', 'version', 'model_type', 'ocr_role',
            'file_path', 'uploaded_at', 'is_system_model', 'description'
        ]
```

#### 3.3.2 上传验证序列化器

```python
class AIModelUploadSerializer(serializers.Serializer):
    model_file = serializers.FileField()
    name = serializers.CharField(max_length=255)
    model_type = serializers.CharField(max_length=50)
    version = serializers.CharField(max_length=50, required=False, allow_blank=True)
    description = serializers.CharField(required=False, allow_blank=True)

    def validate_model_file(self, value):
        if not value.name.endswith('.pt'):
            raise serializers.ValidationError("File must be a .pt file.")
        return value

    def validate(self, data):
        # 检查同名模型是否已存在
        if AIModel.objects.filter(
            name=data.get('name'),
            model_type=data.get('model_type'),
            is_system_model=False
        ).exists():
            raise serializers.ValidationError(
                f"A custom model with the name '{data.get('name')}' "
                f"and type '{data.get('model_type')}' already exists."
            )
        return data
```

### 3.4 推理服务集成

#### 3.4.1 条码检测推理

**端点**: `POST /api/vision/detect/barcode/ultralytics/`

**模型加载流程**:

```python
@api_view(['POST'])
def detect_barcode_ultralytics(request):
    model_name_param = request.POST.get('model_name')

    # 查找模型记录
    ai_model_instance = AIModel.objects.get(
        name=model_name_param,
        model_type='barcode'
    )

    # 构建模型路径
    if ai_model_instance.is_system_model:
        model_filename = os.path.basename(ai_model_instance.model_file.name)
        model_path = os.path.join(
            settings.SYSTEM_MODELS_ROOT,
            'barcode',
            model_filename
        )
    else:
        model_path = ai_model_instance.model_file.path

    # 加载YOLO模型
    model = YOLO(model_path)

    # 执行推理
    results = model.predict(source=temp_image_path, conf=conf_threshold)

    # 处理结果...
```

#### 3.4.2 OCR推理

OCR需要同时加载检测和识别模型：

```python
@api_view(['POST'])
def detect_ocr_paddle_view(request):
    ocr_task_name = request.POST.get('ocr_task_name')

    # 查找检测模型
    det_model_instance = AIModel.objects.get(
        name=ocr_task_name,
        model_type='ocr',
        ocr_role='detection',
        is_system_model=True
    )

    # 查找识别模型
    rec_model_instance = AIModel.objects.get(
        name=ocr_task_name,
        model_type='ocr',
        ocr_role='recognition',
        is_system_model=True
    )

    # 构建模型路径
    det_model_dir = os.path.join(
        settings.SYSTEM_MODELS_ROOT,
        det_model_instance.model_file.name,
        'inference'
    )
    rec_model_dir = os.path.join(
        settings.SYSTEM_MODELS_ROOT,
        rec_model_instance.model_file.name,
        'inference'
    )

    # 初始化预测器
    predictor = PaddleOCRSystemPredictor(
        det_model_dir=det_model_dir,
        rec_model_dir=rec_model_dir,
        **predictor_params
    )

    # 执行推理...
```

#### 3.4.3 AI图像修复推理

**端点**: `POST /api/vision/restore/image/`

**模型选择机制**:

```python
class AIImageRestoreView(APIView):
    def post(self, request, *args, **kwargs):
        model_name = request.data.get('model_name', 'AI_Restorer_V*******')

        # 查找AI修复模型
        ai_model = AIModel.objects.get(
            name=model_name,
            model_type='ai_restored',
            is_system_model=True
        )

        # 构建模型路径
        if ai_model.model_file:
            model_file_relative_path = str(ai_model.model_file)
            model_file_path = os.path.join(
                settings.BASE_DIR,
                'models', 'system_models', 'ai_restored',
                model_file_relative_path
            )

        # 初始化预测器
        predictor = AIRestoredOnnxRuntimePredictor(model_path=model_file_path)

        # 执行推理...
```

---

## 4. 前端集成方式

### 4.1 API服务层

#### 4.1.1 模型获取函数

**文件**: `Frontend/src/services/api.ts`

```typescript
// 模型数据类型定义
export interface VisionModel {
  id: number;
  name: string;
  model_type: string;
  version: string;
  description?: string | null;
  file_path: string;
  is_system_model: boolean;
  uploaded_at: string;
}

export interface GroupedVisionModelsApiResponse {
  [modelType: string]: VisionModel[];
}

// 获取分组模型列表
export const getGroupedVisionModels = async (
  modelScope?: 'system' | 'custom' | 'all'
): Promise<GroupedVisionModelsApiResponse> => {
  let apiUrl = 'vision/models/';
  if (modelScope) {
    apiUrl += `?model_scope=${modelScope}`;
  }

  const response = await apiClient.get<GroupedVisionModelsApiResponse>(apiUrl);
  return response.data;
};

// 获取特定类型模型
export const getAIRestoredModels = async (): Promise<VisionModel[]> => {
  const response = await apiClient.get<VisionModel[] | GroupedVisionModelsApiResponse>(
    'vision/models/?model_type_filter=ai_restored'
  );

  if (Array.isArray(response.data)) {
    return response.data as VisionModel[];
  } else if (response.data && 'ai_restored' in response.data) {
    return response.data['ai_restored'] || [];
  }
  return [];
};
```

#### 4.1.2 模型上传函数

```typescript
export interface UploadCustomModelParams {
  model_file: File;
  name: string;
  model_type: string;
  version?: string;
  description?: string;
}

export const uploadCustomModel = async (
  params: UploadCustomModelParams
): Promise<VisionModel> => {
  const formData = new FormData();
  formData.append('model_file', params.model_file);
  formData.append('name', params.name);
  formData.append('model_type', params.model_type);

  if (params.version) {
    formData.append('version', params.version);
  }
  if (params.description) {
    formData.append('description', params.description);
  }

  const response = await apiClient.post<VisionModel>(
    'vision/models/upload/',
    formData
  );
  return response.data;
};
```

### 4.2 状态管理

#### 4.2.1 条码检测状态管理

**文件**: `Frontend/src/contexts/BarcodeDetectionContext.tsx`

```typescript
interface BarcodeDetectionContextType {
  // 模型相关状态
  systemBarcodeModels: VisionModel[];
  customBarcodeModels: VisionModel[];
  modelsLoading: boolean;

  // 模型选择状态
  modelSelectionType: 'system' | 'custom';
  setModelSelectionType: (type: 'system' | 'custom') => void;

  // 模型加载函数
  fetchBarcodeModels: (trigger: 'initial' | 'manual') => Promise<void>;
}

const BarcodeDetectionProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [systemBarcodeModels, setSystemBarcodeModels] = useState<VisionModel[]>([]);
  const [customBarcodeModels, setCustomBarcodeModels] = useState<VisionModel[]>([]);
  const [modelsLoading, setModelsLoading] = useState(false);

  const fetchBarcodeModels = useCallback(async (trigger: 'initial' | 'manual') => {
    setModelsLoading(true);
    try {
      // 获取系统模型
      const systemModelsResponse = await getGroupedVisionModels('system');
      const systemBarcodeList = systemModelsResponse['barcode'] || [];
      setSystemBarcodeModels(systemBarcodeList);

      // 获取用户模型
      const customModelsResponse = await getGroupedVisionModels('custom');
      const customBarcodeList = customModelsResponse['barcode'] || [];
      setCustomBarcodeModels(customBarcodeList);

    } catch (error) {
      console.error('获取条码模型失败:', error);
    } finally {
      setModelsLoading(false);
    }
  }, []);

  return (
    <BarcodeDetectionContext.Provider value={{
      systemBarcodeModels,
      customBarcodeModels,
      modelsLoading,
      modelSelectionType,
      setModelSelectionType,
      fetchBarcodeModels
    }}>
      {children}
    </BarcodeDetectionContext.Provider>
  );
};
```

#### 4.2.2 AI修复状态管理

**文件**: `Frontend/src/contexts/AiRestoreDetectionContext.tsx`

```typescript
interface AiRestoreDetectionContextType {
  // AI修复模型状态
  aiRestoredModels: VisionModel[];
  aiRestoredModelsLoading: boolean;
  selectedAiRestoredModel: string;
  setSelectedAiRestoredModel: (modelName: string) => void;

  // 模型加载函数
  fetchAiRestoredModels: () => Promise<void>;
}

const AiRestoreDetectionProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [aiRestoredModels, setAiRestoredModels] = useState<VisionModel[]>([]);
  const [selectedAiRestoredModel, setSelectedAiRestoredModel] = useState<string>('AI_Restorer_V*******');

  const fetchAiRestoredModels = useCallback(async () => {
    setAiRestoredModelsLoading(true);
    try {
      const models = await getAIRestoredModels();
      setAiRestoredModels(models);

      // 设置默认选中模型
      if (models.length > 0 && !models.some(m => m.name === selectedAiRestoredModel)) {
        setSelectedAiRestoredModel(models[0].name);
      }
    } catch (error) {
      console.error('获取AI修复模型失败:', error);
    } finally {
      setAiRestoredModelsLoading(false);
    }
  }, [selectedAiRestoredModel]);

  return (
    <AiRestoreDetectionContext.Provider value={{
      aiRestoredModels,
      aiRestoredModelsLoading,
      selectedAiRestoredModel,
      setSelectedAiRestoredModel,
      fetchAiRestoredModels
    }}>
      {children}
    </AiRestoreDetectionContext.Provider>
  );
};
```

### 4.3 组件实现

#### 4.3.1 条码检测面板

**文件**: `Frontend/src/components/parameterPanels/BarcodeDetectionPanel.tsx`

**核心功能**:

1. **模型选择机制**:

```typescript
const BarcodeDetectionPanel: React.FC = () => {
  const { systemBarcodeModels, customBarcodeModels, fetchBarcodeModels } = useBarcodeDetection();
  const [selectedModelName, setSelectedModelName] = useState<string | undefined>();

  // 设置默认模型选择
  const setDefaultModelSelection = useCallback((
    systemModels: VisionModel[],
    customModels: VisionModel[]
  ) => {
    const currentFormModel = form.getFieldValue('system_model_name');
    let defaultModelName: string | undefined;
    let modelsToCheck: VisionModel[];

    if (modelSelectionType === 'system') {
      modelsToCheck = systemModels;
    } else {
      modelsToCheck = customModels;
    }

    const isValidCurrentModel = modelsToCheck.some(m => m.name === currentFormModel);

    if (modelsToCheck.length > 0) {
      if (!currentFormModel || !isValidCurrentModel) {
        defaultModelName = modelsToCheck[0].name;
      } else {
        defaultModelName = currentFormModel;
      }
    }

    form.setFieldsValue({ system_model_name: defaultModelName });
    setSelectedModelName(defaultModelName);
  }, [modelSelectionType, form]);

  // 监听面板切换，自动加载模型
  useEffect(() => {
    const currentKey = selectedFunction?.key as string;
    if (currentKey === 'barcode-detection' && !modelsLoading) {
      fetchModels('initial');
    }
  }, [selectedFunction?.key]);
};
```

2. **模型上传功能**:

```typescript
const handleCustomModelUpload = async () => {
  if (!customModelFile) {
    messageApi.error('请先选择一个模型文件 (.pt)！');
    return;
  }

  // 文件格式验证
  if (!customModelFile.name?.toLowerCase().endsWith('.pt')) {
    messageApi.error('仅支持 .pt 格式的模型文件!');
    return;
  }

  setIsUploading(true);

  // 构建上传参数
  const params: UploadCustomModelParams = {
    model_file: fileToUpload,
    name: modelNameForApi,
    model_type: 'barcode',
  };

  try {
    const uploadedModel = await uploadCustomModel(params);
    messageApi.success(`模型 "${uploadedModel.name}" 上传成功！`);

    // 重新加载模型列表
    await fetchModels('manual');

    // 自动选择新上传的模型
    if (modelSelectionType === 'custom') {
      setSelectedModelName(uploadedModel.name);
      form.setFieldsValue({ system_model_name: uploadedModel.name });
    }
  } catch (error) {
    // 错误处理...
  }
};
```

3. **模型选择UI**:

```typescript
// 模型选择下拉框
<Form.Item
  label="选择模型"
  name="system_model_name"
  rules={[{ required: true, message: '请选择一个模型！' }]}
>
  <Select
    placeholder={`请选择${modelSelectionType === 'system' ? '系统' : '自定义'}模型`}
    loading={modelsLoading}
    value={selectedModelName}
    onChange={(value) => setSelectedModelName(value)}
    disabled={modelsLoading}
  >
    {(modelSelectionType === 'system' ? systemBarcodeModels : customBarcodeModels).map((model) => (
      <Select.Option key={model.id} value={model.name}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <span>{model.name}</span>
          {model.version && (
            <Tag color="blue" style={{ marginLeft: 8 }}>
              v{model.version}
            </Tag>
          )}
        </div>
      </Select.Option>
    ))}
  </Select>
</Form.Item>
```

#### 4.3.2 AI修复面板

**文件**: `Frontend/src/components/parameterPanels/AiRestorePanel.tsx`

**模型选择实现**:

```typescript
const AiRestorePanel: React.FC = () => {
  const {
    aiRestoredModels,
    selectedAiRestoredModel,
    setSelectedAiRestoredModel,
    fetchAiRestoredModels
  } = useAiRestoreDetection();

  // 面板激活时加载模型
  useEffect(() => {
    const currentKey = selectedFunction?.key as string;
    if (currentKey === 'ai-restore' && currentPanelRef.current !== currentKey) {
      currentPanelRef.current = currentKey;
      fetchAiRestoredModels();
    }
  }, [selectedFunction?.key, fetchAiRestoredModels]);

  // 模型选择UI
  const renderModelSelection = () => (
    <Form.Item label="AI修复模型" name="ai_restored_model_name">
      <Select
        value={selectedAiRestoredModel}
        onChange={setSelectedAiRestoredModel}
        placeholder="选择AI修复模型"
        loading={aiRestoredModelsLoading}
      >
        {aiRestoredModels.map((model) => (
          <Select.Option key={model.id} value={model.name}>
            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <span>{model.name}</span>
              {model.version && (
                <Tag color="green">v{model.version}</Tag>
              )}
            </div>
            {model.description && (
              <div style={{ fontSize: '12px', color: '#666', marginTop: '2px' }}>
                {model.description}
              </div>
            )}
          </Select.Option>
        ))}
      </Select>
    </Form.Item>
  );
};
```

### 4.4 推理请求集成

#### 4.4.1 条码检测推理

```typescript
const handleDetection = async (values: BarcodeDetectionFormValues) => {
  if (!selectedModelName) {
    messageApi.error('请选择一个模型进行检测！');
    return;
  }

  const apiParams: BarcodeDetectionParams = {
    image: imageSourceForApi,
    confidence_threshold: values.confidence_threshold,
    preprocessing_method: apiPreprocessingMethod,
    model_name: values.system_model_name, // 使用选中的模型名称
  };

  try {
    const result: BarcodeDetectionResult = await detectBarcode(apiParams);
    // 处理检测结果...
  } catch (error) {
    // 错误处理...
  }
};
```

#### 4.4.2 AI修复推理

```typescript
const handleAiRestore = async (values: AiRestoreFormValues) => {
  const apiParams: AiRestoreParams = {
    image: currentImageFile,
    model_name: selectedAiRestoredModel, // 使用选中的AI修复模型
    restore_mode: values.restore_mode,
    display_mode: values.display_mode,
  };

  try {
    const result = await restoreImage(apiParams);
    // 处理修复结果...
  } catch (error) {
    // 错误处理...
  }
};
```

---

## 5. 添加新模型的完整流程

### 5.1 添加新模型类型

#### 5.1.1 后端数据库扩展

**步骤1**: 创建数据库迁移

```bash
cd Backend_Django
python manage.py makemigrations vision_app --name add_new_model_type
```

**步骤2**: 编写迁移文件

```python
# Backend_Django/vision_app/migrations/xxxx_add_new_model_type.py
from django.db import migrations

def add_new_model_type_models(apps, schema_editor):
    AIModel = apps.get_model('vision_app', 'AIModel')

    # 添加新模型类型的系统模型
    AIModel.objects.using(db_alias).update_or_create(
        name='New_Model_V1.0.0',
        model_type='new_model_type',  # 新的模型类型
        is_system_model=True,
        defaults={
            'description': '新模型类型的系统模型',
            'model_file': 'new_model_v1.0.0.onnx',
            'version': '1.0.0',
            'uploaded_at': timezone.now()
        }
    )

class Migration(migrations.Migration):
    dependencies = [
        ('vision_app', '0013_update_ai_restorer_model_v1017'),
    ]

    operations = [
        migrations.RunPython(add_new_model_type_models),
    ]
```

**步骤3**: 执行迁移

```bash
python manage.py migrate
```

#### 5.1.2 后端API扩展

**步骤4**: 添加推理视图

```python
# Backend_Django/vision_app/views.py
@api_view(['POST'])
def detect_new_model_type(request):
    """新模型类型推理API"""
    model_name_param = request.POST.get('model_name')

    try:
        # 查找模型记录
        ai_model_instance = AIModel.objects.get(
            name=model_name_param,
            model_type='new_model_type'
        )

        # 构建模型路径
        if ai_model_instance.is_system_model:
            model_filename = os.path.basename(ai_model_instance.model_file.name)
            model_path = os.path.join(
                settings.SYSTEM_MODELS_ROOT,
                'new_model_type',
                model_filename
            )
        else:
            model_path = ai_model_instance.model_file.path

        # 验证文件存在
        if not os.path.exists(model_path):
            return Response({
                'error': f'模型文件未找到: {model_path}'
            }, status=status.HTTP_404_NOT_FOUND)

        # 加载模型并执行推理
        # predictor = NewModelTypePredictor(model_path=model_path)
        # result = predictor.predict(image_data)

        return Response({
            'success': True,
            'model_used': model_name_param,
            'results': []  # 推理结果
        }, status=status.HTTP_200_OK)

    except AIModel.DoesNotExist:
        return Response({
            'error': f'未找到名为 "{model_name_param}" 的新模型类型模型'
        }, status=status.HTTP_404_NOT_FOUND)
```

**步骤5**: 添加URL路由

```python
# Backend_Django/vision_app/urls.py
urlpatterns = [
    # 现有路由...
    path('detect/new_model_type/', views.detect_new_model_type, name='detect_new_model_type'),
]
```

#### 5.1.3 前端集成

**步骤6**: 扩展API服务

```typescript
// Frontend/src/services/api.ts

// 新模型类型推理参数
export interface NewModelTypeParams {
  image: File | string;
  model_name: string;
  // 其他参数...
}

// 新模型类型推理结果
export interface NewModelTypeResult {
  success: boolean;
  model_used: string;
  results: any[];
}

// 新模型类型推理函数
export const detectNewModelType = async (
  params: NewModelTypeParams
): Promise<NewModelTypeResult> => {
  const formData = new FormData();

  if (typeof params.image === 'string') {
    formData.append('image_data_url', params.image);
  } else {
    formData.append('image', params.image);
  }

  formData.append('model_name', params.model_name);

  const response = await apiClient.post<NewModelTypeResult>(
    'vision/detect/new_model_type/',
    formData
  );
  return response.data;
};

// 获取新模型类型模型列表
export const getNewModelTypeModels = async (): Promise<VisionModel[]> => {
  const response = await apiClient.get<GroupedVisionModelsApiResponse>(
    'vision/models/?model_type_filter=new_model_type'
  );

  if (response.data && 'new_model_type' in response.data) {
    return response.data['new_model_type'] || [];
  }
  return [];
};
```

**步骤7**: 创建状态管理Context

```typescript
// Frontend/src/contexts/NewModelTypeContext.tsx
interface NewModelTypeContextType {
  newModelTypeModels: VisionModel[];
  selectedNewModelTypeModel: string;
  setSelectedNewModelTypeModel: (modelName: string) => void;
  fetchNewModelTypeModels: () => Promise<void>;
}

const NewModelTypeProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [newModelTypeModels, setNewModelTypeModels] = useState<VisionModel[]>([]);
  const [selectedNewModelTypeModel, setSelectedNewModelTypeModel] = useState<string>('');

  const fetchNewModelTypeModels = useCallback(async () => {
    try {
      const models = await getNewModelTypeModels();
      setNewModelTypeModels(models);

      if (models.length > 0 && !selectedNewModelTypeModel) {
        setSelectedNewModelTypeModel(models[0].name);
      }
    } catch (error) {
      console.error('获取新模型类型模型失败:', error);
    }
  }, [selectedNewModelTypeModel]);

  return (
    <NewModelTypeContext.Provider value={{
      newModelTypeModels,
      selectedNewModelTypeModel,
      setSelectedNewModelTypeModel,
      fetchNewModelTypeModels
    }}>
      {children}
    </NewModelTypeContext.Provider>
  );
};
```

**步骤8**: 创建功能面板组件

```typescript
// Frontend/src/components/parameterPanels/NewModelTypePanel.tsx
const NewModelTypePanel: React.FC = () => {
  const {
    newModelTypeModels,
    selectedNewModelTypeModel,
    setSelectedNewModelTypeModel,
    fetchNewModelTypeModels
  } = useNewModelType();

  const [form] = Form.useForm();
  const [isInferring, setIsInferring] = useState(false);

  // 面板激活时加载模型
  useEffect(() => {
    const currentKey = selectedFunction?.key as string;
    if (currentKey === 'new-model-type') {
      fetchNewModelTypeModels();
    }
  }, [selectedFunction?.key, fetchNewModelTypeModels]);

  const handleInference = async (values: any) => {
    if (!selectedNewModelTypeModel) {
      messageApi.error('请选择一个模型！');
      return;
    }

    setIsInferring(true);

    try {
      const result = await detectNewModelType({
        image: currentImageFile,
        model_name: selectedNewModelTypeModel,
        // 其他参数...
      });

      messageApi.success('推理完成！');
      // 处理结果...

    } catch (error) {
      messageApi.error('推理失败！');
    } finally {
      setIsInferring(false);
    }
  };

  return (
    <Form form={form} onFinish={handleInference} layout="vertical">
      <Form.Item label="选择模型" name="model_name">
        <Select
          value={selectedNewModelTypeModel}
          onChange={setSelectedNewModelTypeModel}
          placeholder="选择新模型类型模型"
        >
          {newModelTypeModels.map((model) => (
            <Select.Option key={model.id} value={model.name}>
              {model.name}
              {model.version && <Tag color="blue">v{model.version}</Tag>}
            </Select.Option>
          ))}
        </Select>
      </Form.Item>

      <Form.Item>
        <Button
          type="primary"
          htmlType="submit"
          loading={isInferring}
          disabled={!selectedNewModelTypeModel}
        >
          开始推理
        </Button>
      </Form.Item>
    </Form>
  );
};
```

### 5.2 添加新版本模型

#### 5.2.1 系统模型版本更新

**创建迁移文件**:

```python
# Backend_Django/vision_app/migrations/xxxx_add_model_v2.py
def add_model_v2(apps, schema_editor):
    AIModel = apps.get_model('vision_app', 'AIModel')

    AIModel.objects.using(db_alias).update_or_create(
        name='AI_Restorer_V2.0.0',
        model_type='ai_restored',
        is_system_model=True,
        defaults={
            'description': 'AI图像修复模型 - 版本2.0.0 (重大更新)',
            'model_file': 'AI_Restorer_NCHW_1x1x256x256_V2.0.0.onnx',
            'version': '2.0.0',
            'uploaded_at': timezone.now()
        }
    )
```

#### 5.2.2 用户模型上传

**前端上传流程**:

```typescript
const handleModelUpload = async (file: File, modelType: string) => {
  const modelName = file.name.replace(/\.[^/.]+$/, ""); // 移除扩展名

  const params: UploadCustomModelParams = {
    model_file: file,
    name: modelName,
    model_type: modelType,
    version: '1.0.0', // 用户可以指定版本
    description: '用户上传的自定义模型'
  };

  try {
    const uploadedModel = await uploadCustomModel(params);
    messageApi.success(`模型 "${uploadedModel.name}" 上传成功！`);

    // 刷新模型列表
    await fetchModels();

  } catch (error) {
    messageApi.error('模型上传失败！');
  }
};
```

---

## 6. 文件结构说明

### 6.1 后端文件结构

```
Backend_Django/
├── vision_app/
│   ├── models.py                    # AIModel数据模型定义
│   ├── views.py                     # API视图函数
│   ├── serializers.py               # 数据序列化器
│   ├── urls.py                      # URL路由配置
│   └── migrations/                  # 数据库迁移文件
│       ├── 0001_initial.py          # 初始模型创建
│       ├── 0006_alter_aimodel_*.py  # OCR角色字段添加
│       ├── 0007_populate_*.py       # 车牌识别模型初始化
│       └── 0013_update_*.py         # AI修复模型多版本
├── models/                          # 模型文件存储
│   ├── system_models/               # 系统模型目录
│   │   ├── barcode/                 # 条码检测模型
│   │   ├── ocr/                     # OCR模型
│   │   └── ai_restored/             # AI修复模型
│   └── custom_models/               # 用户模型目录 (MEDIA_ROOT)
│       ├── barcode/                 # 用户条码模型
│       ├── ocr/                     # 用户OCR模型
│       └── ai_restored/             # 用户AI修复模型
└── backend_project/
    └── settings.py                  # Django配置文件
```

### 6.2 前端文件结构

```
Frontend/src/
├── services/
│   └── api.ts                       # API服务层，模型相关函数
├── contexts/                        # 状态管理Context
│   ├── BarcodeDetectionContext.tsx  # 条码检测状态
│   ├── OcrDetectionContext.tsx      # OCR检测状态
│   └── AiRestoreDetectionContext.tsx # AI修复状态
├── components/
│   └── parameterPanels/             # 功能面板组件
│       ├── BarcodeDetectionPanel.tsx # 条码检测面板
│       ├── OcrDetectionPanel.tsx     # OCR检测面板
│       └── AiRestorePanel.tsx        # AI修复面板
└── types/                           # TypeScript类型定义
    └── api.ts                       # API相关类型
```

### 6.3 关键文件作用

#### 6.3.1 后端核心文件

| 文件 | 作用 | 关键功能 |
|------|------|----------|
| `models.py` | 数据模型定义 | AIModel类、字段约束、文件路径管理 |
| `views.py` | API视图实现 | 模型列表、上传、推理API |
| `serializers.py` | 数据序列化 | 模型数据转换、验证逻辑 |
| `migrations/` | 数据库版本控制 | 模型结构变更、系统模型初始化 |

#### 6.3.2 前端核心文件

| 文件 | 作用 | 关键功能 |
|------|------|----------|
| `api.ts` | API通信层 | HTTP请求封装、类型定义 |
| `*Context.tsx` | 状态管理 | 模型数据缓存、状态同步 |
| `*Panel.tsx` | UI组件 | 模型选择界面、推理触发 |

---

## 7. 最佳实践和注意事项

### 7.1 开发最佳实践

#### 7.1.1 数据库设计原则

1. **唯一性约束**: 确保模型名称在相同类型和范围内唯一
2. **版本管理**: 使用语义化版本号，支持多版本并存
3. **软删除**: 考虑使用软删除而非物理删除，保留历史记录
4. **索引优化**: 为常用查询字段添加数据库索引

```python
# 推荐的模型查询优化
class AIModel(models.Model):
    # ... 字段定义 ...

    class Meta:
        unique_together = [['name', 'model_type', 'is_system_model', 'ocr_role']]
        indexes = [
            models.Index(fields=['model_type', 'is_system_model']),
            models.Index(fields=['name', 'model_type']),
        ]
```

#### 7.1.2 文件管理策略

1. **路径规范化**: 统一使用相对路径，避免硬编码绝对路径
2. **文件验证**: 上传前验证文件格式、大小、完整性
3. **存储分离**: 系统模型和用户模型分别存储，便于管理
4. **备份策略**: 定期备份重要模型文件

```python
# 文件验证示例
def validate_model_file(file):
    # 文件大小检查
    if file.size > 500 * 1024 * 1024:  # 500MB
        raise ValidationError("模型文件不能超过500MB")

    # 文件格式检查
    allowed_extensions = {
        'barcode': ['.pt'],
        'ocr': ['.pdmodel', '.pdiparams'],
        'ai_restored': ['.onnx']
    }

    # 文件完整性检查
    try:
        # 尝试加载模型文件头部
        pass
    except Exception:
        raise ValidationError("模型文件格式不正确或已损坏")
```

#### 7.1.3 API设计原则

1. **RESTful设计**: 遵循REST API设计规范
2. **错误处理**: 提供详细的错误信息和状态码
3. **参数验证**: 严格验证输入参数
4. **响应格式**: 统一的JSON响应格式

```python
# 标准API响应格式
{
    "success": true,
    "data": { ... },
    "message": "操作成功",
    "error_code": null
}

# 错误响应格式
{
    "success": false,
    "data": null,
    "message": "模型文件未找到",
    "error_code": "MODEL_NOT_FOUND"
}
```

### 7.2 性能优化建议

#### 7.2.1 模型加载优化

1. **延迟加载**: 只在需要时加载模型，避免启动时加载所有模型
2. **模型缓存**: 缓存已加载的模型实例，避免重复加载
3. **内存管理**: 及时释放不用的模型，防止内存泄漏

```python
# 模型缓存示例
class ModelCache:
    _cache = {}

    @classmethod
    def get_model(cls, model_path: str, model_type: str):
        cache_key = f"{model_type}:{model_path}"

        if cache_key not in cls._cache:
            if model_type == 'barcode':
                cls._cache[cache_key] = YOLO(model_path)
            elif model_type == 'ai_restored':
                cls._cache[cache_key] = AIRestoredPredictor(model_path)

        return cls._cache[cache_key]
```

#### 7.2.2 前端性能优化

1. **状态缓存**: 缓存模型列表，避免频繁API调用
2. **懒加载**: 面板切换时才加载对应模型数据
3. **防抖处理**: 用户操作防抖，减少不必要的请求

```typescript
// 防抖Hook示例
const useDebounce = (value: any, delay: number) => {
  const [debouncedValue, setDebouncedValue] = useState(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
};
```

### 7.3 安全注意事项

#### 7.3.1 文件上传安全

1. **文件类型限制**: 严格限制允许上传的文件类型
2. **文件大小限制**: 设置合理的文件大小上限
3. **文件名安全**: 过滤危险字符，防止路径遍历攻击
4. **病毒扫描**: 对上传文件进行安全扫描

```python
# 安全的文件名处理
import re
from django.utils.text import get_valid_filename

def sanitize_filename(filename):
    # 移除危险字符
    filename = re.sub(r'[^\w\s-.]', '', filename)
    # 使用Django的安全文件名函数
    filename = get_valid_filename(filename)
    # 限制文件名长度
    if len(filename) > 100:
        name, ext = os.path.splitext(filename)
        filename = name[:95] + ext
    return filename
```

#### 7.3.2 权限控制

1. **用户认证**: 模型上传需要用户认证
2. **权限分离**: 区分系统模型和用户模型的访问权限
3. **操作日志**: 记录模型相关的重要操作
4. **访问控制**: 限制模型文件的直接访问

```python
# 权限装饰器示例
from functools import wraps

def require_admin(view_func):
    @wraps(view_func)
    def wrapper(request, *args, **kwargs):
        if not request.user.is_authenticated or not request.user.is_staff:
            return Response({
                'error': '需要管理员权限'
            }, status=status.HTTP_403_FORBIDDEN)
        return view_func(request, *args, **kwargs)
    return wrapper

@require_admin
@api_view(['DELETE'])
def delete_model(request, model_id):
    # 删除模型的实现
    pass
```

### 7.4 错误处理和调试

#### 7.4.1 常见错误处理

1. **模型文件不存在**: 提供清晰的错误信息和解决建议
2. **模型格式不兼容**: 验证模型文件格式和版本
3. **内存不足**: 优雅处理内存不足的情况
4. **网络超时**: 设置合理的超时时间和重试机制

```python
# 错误处理示例
try:
    model = YOLO(model_path)
except FileNotFoundError:
    logger.error(f"模型文件未找到: {model_path}")
    return Response({
        'error': '模型文件未找到，请检查文件是否存在',
        'error_code': 'MODEL_FILE_NOT_FOUND'
    }, status=status.HTTP_404_NOT_FOUND)
except Exception as e:
    logger.error(f"模型加载失败: {str(e)}")
    return Response({
        'error': '模型加载失败，请检查模型文件格式',
        'error_code': 'MODEL_LOAD_ERROR'
    }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
```

#### 7.4.2 调试技巧

1. **日志记录**: 详细记录模型加载和推理过程
2. **性能监控**: 监控模型加载时间和内存使用
3. **错误追踪**: 使用错误追踪工具定位问题
4. **测试覆盖**: 编写全面的单元测试和集成测试

```python
# 日志配置示例
import logging

logger = logging.getLogger(__name__)

def load_model_with_logging(model_path: str, model_type: str):
    start_time = time.time()
    logger.info(f"开始加载模型: {model_path}")

    try:
        model = ModelCache.get_model(model_path, model_type)
        load_time = time.time() - start_time
        logger.info(f"模型加载成功，耗时: {load_time:.2f}秒")
        return model
    except Exception as e:
        logger.error(f"模型加载失败: {str(e)}")
        raise
```

---

## 总结

本文档详细介绍了AI视觉应用项目的模型管理系统，涵盖了从数据库设计到前端集成的完整技术栈。通过遵循本文档的指导原则和最佳实践，开发团队可以：

1. **高效开发**: 快速添加新的AI模型功能
2. **代码一致性**: 保持统一的代码风格和架构模式
3. **系统稳定性**: 避免常见的设计陷阱和性能问题
4. **安全可靠**: 确保模型管理的安全性和可靠性

该模型管理系统具有良好的扩展性和维护性，能够支持项目的长期发展和功能迭代。