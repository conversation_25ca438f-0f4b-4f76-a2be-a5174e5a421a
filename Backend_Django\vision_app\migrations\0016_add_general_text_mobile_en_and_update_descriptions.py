# Generated manually for adding general_text_mobile_en model and updating descriptions

from django.db import migrations
from django.utils import timezone

def add_general_text_mobile_en_and_update_descriptions(apps, schema_editor):
    """
    添加general_text_mobile_en模型并更新所有OCR模型的描述
    """
    AIModel = apps.get_model('vision_app', 'AIModel')
    db_alias = schema_editor.connection.alias
    
    print("开始添加新的英文mobile模型并更新描述...")
    
    # 1. 添加general_text_mobile_en检测模型
    print("1. 添加general_text_mobile_en检测模型...")
    det_model, det_created = AIModel.objects.using(db_alias).update_or_create(
        name='general_text_mobile_en',
        model_type='ocr',
        is_system_model=True,
        ocr_role='detection',
        defaults={
            'description': '通用英文文字检测 - 轻量级移动端优化',
            'model_file': 'ocr/general_ocr_mobile_en/inference',
            'version': '4.0.0',
            'uploaded_at': timezone.now()
        }
    )
    
    if det_created:
        print(f"   成功创建检测模型 ID {det_model.id}")
    else:
        print(f"   检测模型已存在，已更新 ID {det_model.id}")
    
    # 2. 添加general_text_mobile_en识别模型
    print("2. 添加general_text_mobile_en识别模型...")
    rec_model, rec_created = AIModel.objects.using(db_alias).update_or_create(
        name='general_text_mobile_en',
        model_type='ocr',
        is_system_model=True,
        ocr_role='recognition',
        defaults={
            'description': '通用英文文字识别 - 轻量级移动端优化',
            'model_file': 'ocr/general_ocr_mobile_en/inference',
            'version': '4.0.0',
            'uploaded_at': timezone.now()
        }
    )
    
    if rec_created:
        print(f"   成功创建识别模型 ID {rec_model.id}")
    else:
        print(f"   识别模型已存在，已更新 ID {rec_model.id}")
    
    # 3. 更新现有模型的描述 - 侧重功能和能力范围
    print("3. 更新现有OCR模型的描述...")
    
    # 更新车牌识别模型描述
    license_plate_models = AIModel.objects.using(db_alias).filter(
        name='license_plate_cn',
        model_type='ocr',
        is_system_model=True
    )
    
    for model in license_plate_models:
        if model.ocr_role == 'detection':
            model.description = '中文车牌检测 - 专业车牌定位识别'
        elif model.ocr_role == 'recognition':
            model.description = '中文车牌识别 - 专业车牌文字提取'
        model.save()
        print(f"   更新车牌模型 ID {model.id} ({model.ocr_role})")
    
    # 更新身份证识别模型描述
    id_card_models = AIModel.objects.using(db_alias).filter(
        name='id_card_en',
        model_type='ocr',
        is_system_model=True
    )
    
    for model in id_card_models:
        if model.ocr_role == 'detection':
            model.description = '英文证件号码检测 - 专业证件信息定位'
        elif model.ocr_role == 'recognition':
            model.description = '英文证件号码识别 - 专业证件信息提取'
        model.save()
        print(f"   更新证件模型 ID {model.id} ({model.ocr_role})")
    
    # 更新中英文通用模型描述
    general_ch_en_models = AIModel.objects.using(db_alias).filter(
        name='general_text_mobile_ch_en',
        model_type='ocr',
        is_system_model=True
    )
    
    for model in general_ch_en_models:
        if model.ocr_role == 'detection':
            model.description = '通用文字检测 - 中英文双语轻量级'
        elif model.ocr_role == 'recognition':
            model.description = '通用文字识别 - 中英文双语轻量级'
        model.save()
        print(f"   更新中英文通用模型 ID {model.id} ({model.ocr_role})")
    
    print("新模型添加和描述更新完成！")

def reverse_add_general_text_mobile_en_and_update_descriptions(apps, schema_editor):
    """
    回滚操作：删除general_text_mobile_en模型并恢复原始描述
    """
    AIModel = apps.get_model('vision_app', 'AIModel')
    db_alias = schema_editor.connection.alias
    
    print("开始回滚：删除general_text_mobile_en模型...")
    
    # 删除general_text_mobile_en模型
    deleted_count = AIModel.objects.using(db_alias).filter(
        name='general_text_mobile_en',
        model_type='ocr',
        is_system_model=True
    ).delete()
    print(f"删除了 {deleted_count[0]} 个 general_text_mobile_en 模型记录")
    
    # 恢复原始描述（简化版本，不完全恢复）
    print("恢复原始模型描述...")
    
    # 恢复车牌识别模型描述
    license_plate_models = AIModel.objects.using(db_alias).filter(
        name='license_plate_cn',
        model_type='ocr',
        is_system_model=True
    )
    
    for model in license_plate_models:
        if model.ocr_role == 'detection':
            model.description = '中文车牌检测模型 (PaddleOCR)'
        elif model.ocr_role == 'recognition':
            model.description = '中文车牌识别模型 (PaddleOCR)'
        model.save()
    
    print("回滚完成！")

class Migration(migrations.Migration):

    dependencies = [
        ('vision_app', '0015_update_ocr_model_file_paths'),
    ]

    operations = [
        migrations.RunPython(
            add_general_text_mobile_en_and_update_descriptions,
            reverse_add_general_text_mobile_en_and_update_descriptions
        ),
    ]
