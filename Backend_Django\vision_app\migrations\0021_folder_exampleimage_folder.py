# Generated by Django 5.2.1 on 2025-06-18 05:39

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("vision_app", "0020_exampleimage"),
    ]

    operations = [
        migrations.CreateModel(
            name="Folder",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=255)),
                (
                    "category",
                    models.CharField(
                        choices=[
                            ("barcode", "条码"),
                            ("ocr", "OCR"),
                            ("ai_restored", "AI修复"),
                        ],
                        max_length=50,
                    ),
                ),
                (
                    "display_order",
                    models.IntegerField(
                        default=0, help_text="用于排序的字段，数值越小越靠前"
                    ),
                ),
                (
                    "parent",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="subfolders",
                        to="vision_app.folder",
                    ),
                ),
            ],
            options={
                "ordering": ["display_order", "name"],
                "unique_together": {("category", "parent", "name")},
            },
        ),
        migrations.AddField(
            model_name="exampleimage",
            name="folder",
            field=models.ForeignKey(
                blank=True,
                help_text="图片所属的文件夹",
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="images",
                to="vision_app.folder",
            ),
        ),
    ]
