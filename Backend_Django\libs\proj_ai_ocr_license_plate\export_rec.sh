ROOT=$(pwd)
PADDLE2ONNX_PATH="/home/<USER>/Paddle2ONNX"

#* 导出模型
MODEL_SHAPE="[1,3,64,320]"
export_model="output/ccpd_rec_ppocr_v4"

pretrained_model="${export_model}/best_model/model.pdparams"
config_yaml=$(ls ${export_model}/*.yml)
config_yaml=${config_yaml[0]}
inference_dir="${export_model}/inference"
python3 tools/export_model.py \
-c ${config_yaml} \
-o Global.pretrained_model="${pretrained_model}" \
 Global.save_inference_dir="${inference_dir}"

# 固定形状
python3 ${PADDLE2ONNX_PATH}/tools/paddle/infer_paddle_model_shape.py \
--model_path ${inference_dir}/inference \
--save_path ${inference_dir}/new_inference \
--input_shape_dict="{'x': ${MODEL_SHAPE}}"

# 转换为onnx模型
model_name=$(basename ${export_model})
model_shape_str=$(echo "${MODEL_SHAPE:1:${#MODEL_SHAPE}-2}" | awk 'BEGIN{FS=","}{for(i=1;i<=NF;i++) printf "%s%s", $i, (i<NF?"x":"")}')
paddle2onnx --model_dir ${inference_dir} \
--model_filename new_inference.pdmodel \
--params_filename new_inference.pdiparams \
--save_file ${inference_dir}/${model_name}_${model_shape_str}.onnx \
--opset_version 15 \
--enable_onnx_checker True