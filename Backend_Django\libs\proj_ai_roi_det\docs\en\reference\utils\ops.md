---
description: Explore detailed documentation on utility operations in Ultralytics including non-max suppression, bounding box transformations, and more.
keywords: Ultralytics, utility operations, non-max suppression, bounding box transformations, YOLOv8, machine learning
---

# Reference for `ultralytics/utils/ops.py`

!!! note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/utils/ops.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/utils/ops.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/utils/ops.py) 🛠️. Thank you 🙏!

<br>

## ::: ultralytics.utils.ops.Profile

<br><br><hr><br>

## ::: ultralytics.utils.ops.segment2box

<br><br><hr><br>

## ::: ultralytics.utils.ops.scale_boxes

<br><br><hr><br>

## ::: ultralytics.utils.ops.make_divisible

<br><br><hr><br>

## ::: ultralytics.utils.ops.nms_rotated

<br><br><hr><br>

## ::: ultralytics.utils.ops.non_max_suppression

<br><br><hr><br>

## ::: ultralytics.utils.ops.clip_boxes

<br><br><hr><br>

## ::: ultralytics.utils.ops.clip_coords

<br><br><hr><br>

## ::: ultralytics.utils.ops.scale_image

<br><br><hr><br>

## ::: ultralytics.utils.ops.xyxy2xywh

<br><br><hr><br>

## ::: ultralytics.utils.ops.xywh2xyxy

<br><br><hr><br>

## ::: ultralytics.utils.ops.xywhn2xyxy

<br><br><hr><br>

## ::: ultralytics.utils.ops.xyxy2xywhn

<br><br><hr><br>

## ::: ultralytics.utils.ops.xywh2ltwh

<br><br><hr><br>

## ::: ultralytics.utils.ops.xyxy2ltwh

<br><br><hr><br>

## ::: ultralytics.utils.ops.ltwh2xywh

<br><br><hr><br>

## ::: ultralytics.utils.ops.xyxyxyxy2xywhr

<br><br><hr><br>

## ::: ultralytics.utils.ops.xywhr2xyxyxyxy

<br><br><hr><br>

## ::: ultralytics.utils.ops.ltwh2xyxy

<br><br><hr><br>

## ::: ultralytics.utils.ops.segments2boxes

<br><br><hr><br>

## ::: ultralytics.utils.ops.resample_segments

<br><br><hr><br>

## ::: ultralytics.utils.ops.crop_mask

<br><br><hr><br>

## ::: ultralytics.utils.ops.process_mask

<br><br><hr><br>

## ::: ultralytics.utils.ops.process_mask_native

<br><br><hr><br>

## ::: ultralytics.utils.ops.scale_masks

<br><br><hr><br>

## ::: ultralytics.utils.ops.scale_coords

<br><br><hr><br>

## ::: ultralytics.utils.ops.regularize_rboxes

<br><br><hr><br>

## ::: ultralytics.utils.ops.masks2segments

<br><br><hr><br>

## ::: ultralytics.utils.ops.convert_torch2numpy_batch

<br><br><hr><br>

## ::: ultralytics.utils.ops.clean_str

<br><br>
