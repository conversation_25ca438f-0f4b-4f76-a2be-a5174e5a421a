# 部署指南

本文档提供完整的部署工作流指南，涵盖各种部署场景和最佳实践。

---

## 🔄 **完整开发工作流指南**

### 🎯 **场景一：本地单机开发**

#### 热重载开发模式（推荐）
```powershell
# 1. 启动热重载环境
.\scripts\deploy-hotreload.ps1 -Mode start

# 2. 开发访问地址
# 前端应用: http://localhost:8080 (热重载)
# 后端API: http://localhost:8080/api (代理访问)
# 直接后端: http://localhost:8000 (调试用)

# 3. 开发流程
# - 前端代码修改 → 重新构建并同步
# - 后端代码修改 → Django自动重载
```

### 🌐 **场景二：跨电脑协作开发**

#### A电脑（开发机）设置
```powershell
# 1. 设置网络共享（需要管理员权限）
.\scripts\setup-code-sharing-simple.ps1 setup

# 2. 正常开发
# - 修改前端代码后运行: npm run build
# - 修改后端代码直接保存即可

# 3. 查看共享状态
.\scripts\setup-code-sharing-simple.ps1 status
```

#### B电脑（部署机）设置
```powershell
# 1. 导入镜像并启动服务
.\scripts\import-images.ps1
.\scripts\deploy-hotreload.ps1 -Mode start

# 2. 启动实时同步（替换为A电脑实际IP）
.\scripts\sync-from-dev.ps1 -DevMachineIP "*************" -Mode watch

# 3. 自动同步流程
# A电脑代码修改 → 自动检测 → B电脑同步 → Docker重载
```

### 🚀 **场景三：服务器部署**

#### 标准生产部署
```powershell
# 1. 构建和导出镜像
.\scripts\build-images.ps1
.\scripts\export-images.ps1

# 2. 创建部署包
.\scripts\package-for-server.ps1

# 3. 服务器导入和启动
.\scripts\import-images.ps1
.\scripts\start-container.ps1
```

#### 热重载服务器部署
```powershell
# 适用于服务器上的开发和测试
.\scripts\deploy-hotreload.ps1 -Mode start

# 支持代码热重载，便于服务器端调试
```

#### 生产服务器自动启动配置
```powershell
# 设置容器开机自动启动（重要！）
.\scripts\enable-auto-start.ps1

# 验证自动启动配置
docker inspect ai-vision-backend-hotreload --format '{{.HostConfig.RestartPolicy.Name}}'
docker inspect ai-vision-frontend-hotreload --format '{{.HostConfig.RestartPolicy.Name}}'

# 测试自动启动功能
# 1. 重启服务器
# 2. 检查容器是否自动启动: docker ps
```

### 🔧 **场景四：前端专项开发**

#### 前端独立开发
```powershell
# 1. 进入前端目录
cd ../Frontend

# 2. 启动前端开发服务器
npm run dev

# 3. 构建生产版本
npm run build

# 4. 手动同步到Docker热重载目录
# 复制 dist/ 目录到 docker/code-sync/Frontend/dist/
```

---

## 📊 **开发状态管理**

### 查看所有服务状态
```powershell
# 查看Docker容器状态
docker ps

# 查看热重载服务状态
.\scripts\deploy-hotreload.ps1 -Mode status

# 详细诊断
.\scripts\check\check-docker-env.ps1
.\scripts\check\check-network.ps1
```

### 停止所有开发服务
```powershell
# 停止热重载服务
.\scripts\deploy-hotreload.ps1 -Mode stop

# 停止标准容器服务
.\scripts\stop-container.ps1

# 停止所有Docker容器
docker-compose down
```

---

## 🎯 **最佳实践建议**

### 日常开发推荐流程
1. **启动**: `.\scripts\deploy-hotreload.ps1 -Mode start`
2. **开发**: 前端 localhost:8080，后端API localhost:8080/api
3. **测试**: 修改代码后自动热重载
4. **停止**: `.\scripts\deploy-hotreload.ps1 -Mode stop`

### 团队协作推荐流程
1. **A电脑**: 设置网络共享，正常开发
2. **B电脑**: 启动同步监控，实时获取代码更新
3. **测试**: B电脑提供稳定的测试环境
4. **部署**: 使用部署包进行生产部署

### 生产服务器部署流程
1. **镜像准备**: `.\scripts\build-images.ps1` 构建镜像
2. **服务启动**: `.\scripts\deploy-hotreload.ps1 -Mode start` 或 `.\scripts\start-container.ps1`
3. **自动启动**: `.\scripts\enable-auto-start.ps1` 设置开机自启
4. **验证测试**: 重启服务器验证容器自动启动
5. **监控维护**: 定期检查服务状态和日志

### 故障排除流程
1. **环境检查**: `.\scripts\check\check-docker-env.ps1`
2. **网络检查**: `.\scripts\check\check-network.ps1`
3. **重新构建**: `.\scripts\build-images.ps1 -NoCache`
4. **清理重启**: `docker system prune -a` 然后重新部署

---

## 🔄 开发工作流

### 🛠️ 开发模式
```powershell
# 方式1: 仅启动后端容器，前端本地开发
docker-compose up backend
cd ../Frontend && npm run dev

# 方式2: 热重载模式（推荐）
.\scripts\deploy-hotreload.ps1 -Mode start
```

### 📝 代码更新流程
```powershell
# 标准部署更新
1. 停止服务
   docker-compose down

2. 重新构建镜像
   .\scripts\build-images.ps1

3. 启动服务
   .\scripts\start-container.ps1

# 热重载更新（无需重建镜像）
1. 修改代码
2. 前端：npm run build && 复制到 code-sync/Frontend/dist/
3. 后端：直接修改 code-sync/Backend_Django/ 中的文件
4. Django 自动重载
```

### 🚀 部署到生产服务器
```powershell
# 1. 导出镜像
.\scripts\export-images.ps1

# 2. 传输到服务器
# 复制生成的 .tar 文件到服务器

# 3. 在服务器上导入
.\scripts\import-images.ps1

# 4. 启动服务
.\scripts\start-container.ps1
```

---

## 📋 服务管理

### 🔍 状态查看
```powershell
# 查看运行中的容器
docker ps

# 查看所有容器（包括停止的）
docker ps -a

# 查看特定compose文件的服务状态
docker-compose ps                                    # 标准部署
docker-compose -f docker-compose.hotreload.yml ps   # 热重载部署
```

### 📊 日志查看
```powershell
# 查看所有服务日志
docker-compose logs

# 查看特定服务日志
docker-compose logs frontend
docker-compose logs backend

# 实时查看日志（推荐）
docker-compose logs -f

# 查看最近N行日志
docker-compose logs --tail=50 backend
```

### ⚡ 服务控制
```powershell
# 停止服务
docker-compose down

# 重启服务
docker-compose restart

# 重新启动（重新创建容器）
docker-compose up -d

# 强制重新构建并启动
docker-compose up -d --build

# 停止特定服务
docker-compose stop frontend
docker-compose stop backend
```

### 🛠️ 容器管理
```powershell
# 进入后端容器
docker exec -it ai-vision-backend bash

# 进入前端容器
docker exec -it ai-vision-frontend sh

# 运行Django管理命令
docker exec ai-vision-backend python manage.py migrate
docker exec ai-vision-backend python manage.py collectstatic --noinput
docker exec ai-vision-backend python manage.py createsuperuser

# 查看容器资源使用情况
docker stats
```

---

## 🌍 部署环境配置

### 开发环境
- **目标**: 快速开发和调试
- **配置**: `docker-compose.hotreload.yml`
- **特性**: 代码热重载、调试模式、开发工具

### 测试环境
- **目标**: 功能验证和集成测试
- **配置**: `docker-compose.simple.yml`
- **特性**: 简化配置、快速验证、轻量部署

### 生产环境
- **目标**: 稳定运行和高性能
- **配置**: `docker-compose.yml`
- **特性**: 优化配置、健康检查、自动重启

---

## 📦 部署包管理

### 创建部署包
```powershell
# 基本部署包
.\scripts\package-for-server.ps1

# 包含镜像的部署包
.\scripts\package-for-server.ps1 -IncludeImages

# 压缩部署包
.\scripts\package-for-server.ps1 -Compress
```

### 部署包内容
```
server-deployment-package/
├── docker/
│   ├── code-sync/           # 热重载代码
│   ├── data/                # 数据目录
│   ├── scripts/             # 管理脚本
│   ├── docker-compose.*.yml # 配置文件
│   └── docker-images/       # 镜像文件（可选）
├── .env.example
└── DEPLOYMENT-INSTRUCTIONS.txt
```

### 服务器部署步骤
1. **传输部署包**到目标服务器
2. **解压并进入**部署目录
3. **导入镜像**（如果包含）: `.\scripts\import-images.ps1`
4. **启动服务**: `.\scripts\deploy-hotreload.ps1 -Mode start`
5. **设置自动启动**: `.\scripts\enable-auto-start.ps1`
6. **验证访问**: 检查前端和后端服务
7. **测试重启**: 重启服务器验证自动启动

---

## 🔧 高级部署配置

### 自定义端口
```yaml
# 修改 docker-compose.yml
ports:
  - "8081:80"    # 前端端口
  - "8001:8000"  # 后端端口
```

### 自定义数据目录
```yaml
# 修改数据挂载路径
volumes:
  - /custom/data/db:/app/db
  - /custom/data/models:/app/models
```

### 环境变量配置
```env
# 创建 .env 文件
DJANGO_DEBUG=False
DJANGO_ALLOWED_HOSTS=yourdomain.com,localhost
CORS_ALLOW_ALL_ORIGINS=False
```

---

## 📈 性能优化

### 生产环境优化
- 禁用调试模式
- 配置静态文件缓存
- 启用 Gzip 压缩
- 配置健康检查

### 开发环境优化
- 启用热重载
- 配置开发工具
- 优化构建缓存
- 简化日志输出
