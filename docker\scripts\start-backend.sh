#!/bin/bash

# AI Vision App Backend Startup Script

echo "=== AI Vision App Backend Starting ==="

# 等待数据库目录创建
echo "Checking database directory..."
mkdir -p /app/db /app/models /app/media /app/logs

# 设置权限
chmod -R 755 /app/db /app/models /app/media /app/logs

# 等待一段时间确保目录创建完成
sleep 2

# 运行数据库迁移
echo "Running database migrations..."
python manage.py makemigrations --noinput
python manage.py migrate --noinput

# 收集静态文件
echo "Collecting static files..."
python manage.py collectstatic --noinput

# 创建超级用户（如果不存在）
echo "Checking for superuser..."
python manage.py shell -c "
from django.contrib.auth import get_user_model
User = get_user_model()
if not User.objects.filter(username='admin').exists():
    User.objects.create_superuser('admin', '<EMAIL>', 'admin123')
    print('Superuser created: admin/admin123')
else:
    print('Superuser already exists')
"

# 启动Django开发服务器
echo "Starting Django server..."
# 使用环境变量配置端口，默认为8000
BACKEND_PORT=${BACKEND_PORT:-8000}
echo "Server will start on port: $BACKEND_PORT"
exec python manage.py runserver 0.0.0.0:$BACKEND_PORT
