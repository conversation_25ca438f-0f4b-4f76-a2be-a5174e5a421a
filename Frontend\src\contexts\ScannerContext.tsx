import React, { createContext, useContext, useState, useCallback, useRef, useEffect } from 'react';
import { scannerConnect, scannerDisconnect, scannerStartStream, scannerStopStream } from '../services/scannerApi';
import { getNetworkConfigSync } from '../config';

// --- Types ---
type InputSource = 'file' | 'scanner';

interface ScannerState {
  isConnected: boolean;
  isStreaming: boolean;
  deviceIP: string | null;
  frameData: string | null; // Base64 encoded image
  inputSource: InputSource;
  frontendFps: number | null;  // 前端FPS
  backendFps: number | null;   // 后端FPS
}

interface ScannerContextType extends ScannerState {
  connect: (ip: string) => Promise<boolean>;
  disconnect: () => Promise<void>;
  startStream: () => Promise<boolean>;
  stopStream: () => Promise<void>;
}

// --- Context Definition ---
const ScannerContext = createContext<ScannerContextType | undefined>(undefined);

// --- Provider Component ---
export const ScannerProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [state, setState] = useState<ScannerState>({
    isConnected: false,
    isStreaming: false,
    deviceIP: null,
    frameData: null,
    inputSource: 'file', // Default to file input
    frontendFps: null,
    backendFps: null,
  });

  // 帧率监控变量
  const frameCountRef = useRef(0);
  const lastFpsTimeRef = useRef(Date.now());
  
  // HTTP轮询相关
  const pollingIntervalRef = useRef<NodeJS.Timeout | null>(null);

  const startPolling = useCallback(() => {
    if (pollingIntervalRef.current) {
      clearInterval(pollingIntervalRef.current);
    }

    console.log('开始HTTP轮询获取图像帧');
    frameCountRef.current = 0;
    lastFpsTimeRef.current = Date.now();

    pollingIntervalRef.current = setInterval(async () => {
      try {
        // 使用配置管理器获取API URL
        const config = getNetworkConfigSync();
        const frameUrl = `${config.apiBaseUrl}/vision/scanner/frame/`;
        const response = await fetch(frameUrl);
        const data = await response.json();
        
        if (data.status === 'success' && data.image) {
          setState(prev => ({ 
            ...prev, 
            frameData: `data:image/jpeg;base64,${data.image}`,
            backendFps: data.backend_fps || null  // 更新后端FPS
          }));
          
          // 调试日志 - 检查后端FPS数据
          if (data.backend_fps !== undefined) {
            console.log(`Backend FPS received: ${data.backend_fps}`);
          }
          
          // 前端帧率统计
          frameCountRef.current += 1;
          const currentTime = Date.now();
          if (currentTime - lastFpsTimeRef.current >= 1000) { // 每秒统计一次
            const fps = frameCountRef.current / ((currentTime - lastFpsTimeRef.current) / 1000);
            console.log(`Frontend HTTP Polling FPS: ${fps.toFixed(2)}`);
            
            // 更新前端FPS到状态
            setState(prev => ({ ...prev, frontendFps: fps }));
            
            frameCountRef.current = 0;
            lastFpsTimeRef.current = currentTime;
          }
        }
      } catch (error) {
        console.error('HTTP轮询获取图像帧失败:', error);
      }
    }, 250); // 250ms间隔，约4fps
  }, []);

  const stopPolling = useCallback(() => {
    if (pollingIntervalRef.current) {
      clearInterval(pollingIntervalRef.current);
      pollingIntervalRef.current = null;
      console.log('停止HTTP轮询');
    }
  }, []);

  const connect = useCallback(async (ip: string): Promise<boolean> => {
    try {
      const response = await scannerConnect(ip);
      if (response.status === 'success') {
        setState(prev => ({ ...prev, isConnected: true, deviceIP: ip, inputSource: 'scanner' })); // Switch to scanner on connect
        return true;
      }
      return false;
    } catch (error) {
      console.error("Failed to connect scanner:", error);
      return false;
    }
  }, []);

  const disconnect = useCallback(async () => {
    try {
      stopPolling(); // 停止轮询
      await scannerDisconnect();
      setState({
        isConnected: false,
        isStreaming: false,
        deviceIP: null,
        frameData: null,
        inputSource: 'file', // Reset to default
        frontendFps: null,
        backendFps: null,
      });
    } catch (error) {
      console.error("Failed to disconnect scanner:", error);
    }
  }, [stopPolling]);

  const startStream = useCallback(async (): Promise<boolean> => {
    if (!state.isConnected) return false;
    try {
      const response = await scannerStartStream();
      if (response.status === 'success') {
        setState(prev => ({ ...prev, isStreaming: true }));
        startPolling(); // 开始HTTP轮询
        return true;
      }
      return false;
    } catch (error) {
      console.error("Failed to start stream:", error);
      return false;
    }
  }, [state.isConnected, startPolling]);

  const stopStream = useCallback(async () => {
    if (!state.isStreaming) return;
    try {
      stopPolling(); // 停止轮询
      await scannerStopStream();
      setState(prev => ({ ...prev, isStreaming: false }));
    } catch (error) {
      console.error("Failed to stop stream:", error);
    }
  }, [state.isStreaming, stopPolling]);

  // 清理轮询
  useEffect(() => {
    return () => {
      stopPolling();
    };
  }, [stopPolling]);

  return (
    <ScannerContext.Provider value={{ ...state, connect, disconnect, startStream, stopStream }}>
      {children}
    </ScannerContext.Provider>
  );
};

// --- Custom Hook ---
export const useScanner = (): ScannerContextType => {
  const context = useContext(ScannerContext);
  if (context === undefined) {
    throw new Error('useScanner must be used within a ScannerProvider');
  }
  return context;
};