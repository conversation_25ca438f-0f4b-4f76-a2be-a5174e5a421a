"""
存储配置模块

包含文件存储相关的配置，从settings.py中分离出来以提高可维护性。
包括媒体文件、系统模型、示例图片等的存储路径配置。
"""

from pathlib import Path


def get_storage_configs(base_dir: Path) -> dict:
    """
    获取存储配置字典
    
    Args:
        base_dir: Django项目的BASE_DIR路径
        
    Returns:
        dict: 存储配置字典
    """
    return {
        # 静态文件配置
        'STATIC_URL': "static/",
        
        # 媒体文件配置 (用户上传的文件)
        'MEDIA_URL': '/media/',  # Django 会将 MEDIA_ROOT 下的文件通过这个 URL 路径提供服务
        'MEDIA_ROOT': base_dir / 'models' / 'custom_models',  # 自定义模型存储根目录，注意：upload_to 将在此目录下创建子文件夹
        
        # 系统模型存储根目录
        'SYSTEM_MODELS_ROOT': base_dir / 'models' / 'system_models',
        
        # 示例图片存储根目录
        'EXAMPLE_IMAGES_ROOT': base_dir / 'models' / 'example_images',
    }


def get_model_storage_paths(base_dir: Path) -> dict:
    """
    获取各类模型的具体存储路径
    
    Args:
        base_dir: Django项目的BASE_DIR路径
        
    Returns:
        dict: 模型存储路径字典
    """
    storage_configs = get_storage_configs(base_dir)
    
    return {
        # 自定义模型路径
        'custom_models': {
            'barcode': storage_configs['MEDIA_ROOT'] / 'barcode',
            'ocr': storage_configs['MEDIA_ROOT'] / 'ocr',
            'ai_restored': storage_configs['MEDIA_ROOT'] / 'ai_restored',
            'feature_matching': storage_configs['MEDIA_ROOT'] / 'feature_matching',
        },
        
        # 系统模型路径
        'system_models': {
            'barcode': storage_configs['SYSTEM_MODELS_ROOT'] / 'barcode',
            'ocr': storage_configs['SYSTEM_MODELS_ROOT'] / 'ocr',
            'ai_restored': storage_configs['SYSTEM_MODELS_ROOT'] / 'ai_restored',
            'feature_matching': storage_configs['SYSTEM_MODELS_ROOT'] / 'feature_matching',
        },
        
        # 示例图片路径
        'example_images': {
            'barcode': storage_configs['EXAMPLE_IMAGES_ROOT'] / 'barcode',
            'ocr': storage_configs['EXAMPLE_IMAGES_ROOT'] / 'ocr',
            'ai_restored': storage_configs['EXAMPLE_IMAGES_ROOT'] / 'ai_restored',
        }
    }
