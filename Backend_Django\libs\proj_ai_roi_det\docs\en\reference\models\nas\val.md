---
description: Explore the Ultralytics NASValidator for efficient YOLO model validation. Learn about NMS and post-processing configurations.
keywords: Ultralytics, YOLO, NASValidator, object detection, non-maximum suppression, NMS, YOLO models, machine learning
---

# Reference for `ultralytics/models/nas/val.py`

!!! note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/nas/val.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/nas/val.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/models/nas/val.py) 🛠️. Thank you 🙏!

<br>

## ::: ultralytics.models.nas.val.NASValidator

<br><br>
