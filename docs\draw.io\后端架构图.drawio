<mxfile host="Electron" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/26.2.15 Chrome/134.0.6998.205 Electron/35.2.1 Safari/537.36" version="26.2.15">
  <diagram name="第 1 页" id="0WNiHQ31Feyjb5RSkVaz">
    <mxGraphModel dx="3565" dy="2148" grid="0" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1654" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="JMdF2OkQw6zPD9X_Vb0N-1" value="Backend" style="whiteSpace=wrap;strokeWidth=2;verticalAlign=top;fontStyle=1;fontSize=25;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="37" y="24" width="2202" height="1132" as="geometry" />
        </mxCell>
        <mxCell id="JMdF2OkQw6zPD9X_Vb0N-3" value="Services" style="whiteSpace=wrap;strokeWidth=2;fontStyle=1;fontSize=25;verticalAlign=top;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="JMdF2OkQw6zPD9X_Vb0N-1">
          <mxGeometry x="29.12" y="647" width="846.88" height="456" as="geometry" />
        </mxCell>
        <mxCell id="JMdF2OkQw6zPD9X_Vb0N-19" value="Data Stores" style="whiteSpace=wrap;strokeWidth=2;verticalAlign=top;fontStyle=1;fontSize=25;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="JMdF2OkQw6zPD9X_Vb0N-1">
          <mxGeometry x="1338.9975059045819" y="724" width="842.5224374114313" height="379" as="geometry" />
        </mxCell>
        <mxCell id="JMdF2OkQw6zPD9X_Vb0N-136" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.75;exitY=1;exitDx=0;exitDy=0;entryX=0.75;entryY=0;entryDx=0;entryDy=0;curved=1;" edge="1" parent="JMdF2OkQw6zPD9X_Vb0N-1" source="JMdF2OkQw6zPD9X_Vb0N-4" target="JMdF2OkQw6zPD9X_Vb0N-126">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1230" y="153" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="JMdF2OkQw6zPD9X_Vb0N-137" value="&lt;font style=&quot;font-size: 18px;&quot;&gt;输出日志&lt;/font&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="JMdF2OkQw6zPD9X_Vb0N-136">
          <mxGeometry x="0.171" y="-17" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="JMdF2OkQw6zPD9X_Vb0N-4" value="API 层 (FastAPI)" style="whiteSpace=wrap;strokeWidth=2;fontSize=20;fontStyle=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="JMdF2OkQw6zPD9X_Vb0N-1">
          <mxGeometry x="927.9967737364194" y="99" width="179.94615021256496" height="54" as="geometry" />
        </mxCell>
        <mxCell id="JMdF2OkQw6zPD9X_Vb0N-5" value="用户认证与权限 API" style="whiteSpace=wrap;strokeWidth=2;fillColor=#dae8fc;strokeColor=#6c8ebf;fontStyle=1;fontSize=18;" vertex="1" parent="JMdF2OkQw6zPD9X_Vb0N-1">
          <mxGeometry x="178.99690599905526" y="262" width="178.90599905526688" height="54" as="geometry" />
        </mxCell>
        <mxCell id="JMdF2OkQw6zPD9X_Vb0N-117" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.25;exitY=1;exitDx=0;exitDy=0;entryX=0.25;entryY=0;entryDx=0;entryDy=0;curved=1;" edge="1" parent="JMdF2OkQw6zPD9X_Vb0N-1" source="JMdF2OkQw6zPD9X_Vb0N-6" target="JMdF2OkQw6zPD9X_Vb0N-88">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="JMdF2OkQw6zPD9X_Vb0N-122" value="操作元数据" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=18;" vertex="1" connectable="0" parent="JMdF2OkQw6zPD9X_Vb0N-117">
          <mxGeometry x="0.7003" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="JMdF2OkQw6zPD9X_Vb0N-118" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.75;exitY=1;exitDx=0;exitDy=0;entryX=0.25;entryY=0;entryDx=0;entryDy=0;curved=1;" edge="1" parent="JMdF2OkQw6zPD9X_Vb0N-1" source="JMdF2OkQw6zPD9X_Vb0N-6" target="JMdF2OkQw6zPD9X_Vb0N-116">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="JMdF2OkQw6zPD9X_Vb0N-124" value="存储/读取文件" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=18;" vertex="1" connectable="0" parent="JMdF2OkQw6zPD9X_Vb0N-118">
          <mxGeometry x="0.7691" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="JMdF2OkQw6zPD9X_Vb0N-6" value="数据集管理 API" style="whiteSpace=wrap;strokeWidth=2;fontSize=18;fontStyle=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="JMdF2OkQw6zPD9X_Vb0N-1">
          <mxGeometry x="1567.8307605101559" y="575" width="174.74539442607463" height="54" as="geometry" />
        </mxCell>
        <mxCell id="JMdF2OkQw6zPD9X_Vb0N-119" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.25;exitY=1;exitDx=0;exitDy=0;entryX=0.75;entryY=0;entryDx=0;entryDy=0;curved=1;" edge="1" parent="JMdF2OkQw6zPD9X_Vb0N-1" source="JMdF2OkQw6zPD9X_Vb0N-7" target="JMdF2OkQw6zPD9X_Vb0N-88">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="JMdF2OkQw6zPD9X_Vb0N-123" value="操作元数据/版本" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=1;points=[];movable=1;rotatable=1;deletable=1;editable=1;locked=0;connectable=1;fontSize=18;" vertex="1" connectable="0" parent="JMdF2OkQw6zPD9X_Vb0N-119">
          <mxGeometry x="0.7318" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="JMdF2OkQw6zPD9X_Vb0N-120" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.75;exitY=1;exitDx=0;exitDy=0;entryX=0.75;entryY=0;entryDx=0;entryDy=0;curved=1;" edge="1" parent="JMdF2OkQw6zPD9X_Vb0N-1" source="JMdF2OkQw6zPD9X_Vb0N-7" target="JMdF2OkQw6zPD9X_Vb0N-116">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="JMdF2OkQw6zPD9X_Vb0N-125" value="存储/读取权重文件" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=18;" vertex="1" connectable="0" parent="JMdF2OkQw6zPD9X_Vb0N-120">
          <mxGeometry x="0.7033" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="JMdF2OkQw6zPD9X_Vb0N-7" value="模型管理 API" style="whiteSpace=wrap;strokeWidth=2;fontSize=18;fontStyle=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="JMdF2OkQw6zPD9X_Vb0N-1">
          <mxGeometry x="1794.5837128011337" y="575" width="158.10297590930563" height="54" as="geometry" />
        </mxCell>
        <mxCell id="JMdF2OkQw6zPD9X_Vb0N-132" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.25;entryY=0;entryDx=0;entryDy=0;curved=1;" edge="1" parent="JMdF2OkQw6zPD9X_Vb0N-1" source="JMdF2OkQw6zPD9X_Vb0N-8" target="JMdF2OkQw6zPD9X_Vb0N-18">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="JMdF2OkQw6zPD9X_Vb0N-8" value="微调任务 API" style="whiteSpace=wrap;strokeWidth=2;fillColor=#dae8fc;strokeColor=#6c8ebf;fontStyle=1;fontSize=18;" vertex="1" parent="JMdF2OkQw6zPD9X_Vb0N-1">
          <mxGeometry x="594.0172177609826" y="262" width="158.10297590930563" height="54" as="geometry" />
        </mxCell>
        <mxCell id="JMdF2OkQw6zPD9X_Vb0N-134" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.75;entryY=0;entryDx=0;entryDy=0;curved=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" edge="1" parent="JMdF2OkQw6zPD9X_Vb0N-1" source="JMdF2OkQw6zPD9X_Vb0N-9" target="JMdF2OkQw6zPD9X_Vb0N-18">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="JMdF2OkQw6zPD9X_Vb0N-9" value="推理任务 API" style="whiteSpace=wrap;strokeWidth=2;fillColor=#dae8fc;strokeColor=#6c8ebf;fontStyle=1;fontSize=18;" vertex="1" parent="JMdF2OkQw6zPD9X_Vb0N-1">
          <mxGeometry x="924.7852857817667" y="262" width="158.10297590930563" height="54" as="geometry" />
        </mxCell>
        <mxCell id="JMdF2OkQw6zPD9X_Vb0N-11" value="MinIO (数据集文件/模型权重)" style="whiteSpace=wrap;strokeWidth=2;fontSize=18;" vertex="1" parent="JMdF2OkQw6zPD9X_Vb0N-1">
          <mxGeometry x="1878.8359565422768" y="908.5" width="270.4393008974965" height="78" as="geometry" />
        </mxCell>
        <mxCell id="JMdF2OkQw6zPD9X_Vb0N-13" value="推理服务 (ONNX Runtime, 可直连或Celery Worker)" style="whiteSpace=wrap;strokeWidth=2;fontSize=18;" vertex="1" parent="JMdF2OkQw6zPD9X_Vb0N-1">
          <mxGeometry x="513.9967642890883" y="746" width="313.08549834671703" height="300" as="geometry" />
        </mxCell>
        <mxCell id="JMdF2OkQw6zPD9X_Vb0N-14" value="" style="whiteSpace=wrap;strokeWidth=2;" vertex="1" parent="JMdF2OkQw6zPD9X_Vb0N-1">
          <mxGeometry x="147.701464336325" y="878" as="geometry" />
        </mxCell>
        <mxCell id="JMdF2OkQw6zPD9X_Vb0N-15" value="" style="whiteSpace=wrap;strokeWidth=2;" vertex="1" parent="JMdF2OkQw6zPD9X_Vb0N-1">
          <mxGeometry x="206.9900803023146" y="1065" as="geometry" />
        </mxCell>
        <mxCell id="JMdF2OkQw6zPD9X_Vb0N-16" value="" style="whiteSpace=wrap;strokeWidth=2;" vertex="1" parent="JMdF2OkQw6zPD9X_Vb0N-1">
          <mxGeometry x="352.6112423240435" y="878" as="geometry" />
        </mxCell>
        <mxCell id="JMdF2OkQw6zPD9X_Vb0N-17" value="" style="whiteSpace=wrap;strokeWidth=2;" vertex="1" parent="JMdF2OkQw6zPD9X_Vb0N-1">
          <mxGeometry x="396.29759093056214" y="1065" as="geometry" />
        </mxCell>
        <mxCell id="JMdF2OkQw6zPD9X_Vb0N-20" value="RBAC&#xa;（基于角色的访问控制）" style="curved=1;startArrow=none;endArrow=block;exitX=0;exitY=0.64;entryX=0.5;entryY=-0.01;rounded=0;fontSize=18;" edge="1" parent="JMdF2OkQw6zPD9X_Vb0N-1" source="JMdF2OkQw6zPD9X_Vb0N-4" target="JMdF2OkQw6zPD9X_Vb0N-5">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="274.5999055266887" y="187" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="JMdF2OkQw6zPD9X_Vb0N-21" value="" style="curved=1;startArrow=none;endArrow=block;exitX=1;exitY=0.64;entryX=0.5;entryY=0;rounded=0;" edge="1" parent="JMdF2OkQw6zPD9X_Vb0N-1" source="JMdF2OkQw6zPD9X_Vb0N-4" target="JMdF2OkQw6zPD9X_Vb0N-6">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1662" y="215" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="JMdF2OkQw6zPD9X_Vb0N-22" value="" style="curved=1;startArrow=none;endArrow=block;exitX=1;exitY=0.61;entryX=0.5;entryY=0;rounded=0;" edge="1" parent="JMdF2OkQw6zPD9X_Vb0N-1" source="JMdF2OkQw6zPD9X_Vb0N-4" target="JMdF2OkQw6zPD9X_Vb0N-7">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1897" y="183" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="JMdF2OkQw6zPD9X_Vb0N-23" value="" style="curved=1;startArrow=none;endArrow=block;exitX=0;exitY=0.79;entryX=0.5;entryY=-0.01;rounded=0;" edge="1" parent="JMdF2OkQw6zPD9X_Vb0N-1" source="JMdF2OkQw6zPD9X_Vb0N-4" target="JMdF2OkQw6zPD9X_Vb0N-8">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="679.2187057156353" y="187" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="JMdF2OkQw6zPD9X_Vb0N-24" value="" style="curved=1;startArrow=none;endArrow=block;exitX=0.32;exitY=0.99;entryX=0.5;entryY=-0.01;rounded=0;" edge="1" parent="JMdF2OkQw6zPD9X_Vb0N-1" source="JMdF2OkQw6zPD9X_Vb0N-4" target="JMdF2OkQw6zPD9X_Vb0N-9">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1009.9867737364194" y="187" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="JMdF2OkQw6zPD9X_Vb0N-61" value="微调服务（Celery Worker）" style="whiteSpace=wrap;strokeWidth=2;fontSize=18;" vertex="1" parent="JMdF2OkQw6zPD9X_Vb0N-1">
          <mxGeometry x="83.20844119036373" y="746" width="313.08549834671703" height="300" as="geometry" />
        </mxCell>
        <mxCell id="JMdF2OkQw6zPD9X_Vb0N-88" value="MySQL&#xa;（用户/角色/数据集元数据/模型版本/任务状态/指标/日志）" style="whiteSpace=wrap;strokeWidth=2;fontSize=18;" vertex="1" parent="JMdF2OkQw6zPD9X_Vb0N-1">
          <mxGeometry x="1377.4830987246103" y="908.5" width="329.7279168634861" height="144.5" as="geometry" />
        </mxCell>
        <mxCell id="JMdF2OkQw6zPD9X_Vb0N-116" value="MinIO&#xa;（数据集文件/模型权重文件）" style="whiteSpace=wrap;strokeWidth=2;fontSize=18;" vertex="1" parent="JMdF2OkQw6zPD9X_Vb0N-1">
          <mxGeometry x="1836.1897590930562" y="907" width="313.08549834671703" height="144" as="geometry" />
        </mxCell>
        <mxCell id="JMdF2OkQw6zPD9X_Vb0N-18" value="Async Task Queue" style="whiteSpace=wrap;strokeWidth=2;verticalAlign=top;fontStyle=1;fontSize=25;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="JMdF2OkQw6zPD9X_Vb0N-1">
          <mxGeometry x="308.2283230987247" y="408" width="379.6551724137931" height="161" as="geometry" />
        </mxCell>
        <mxCell id="JMdF2OkQw6zPD9X_Vb0N-128" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.25;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;curved=1;" edge="1" parent="JMdF2OkQw6zPD9X_Vb0N-1" source="JMdF2OkQw6zPD9X_Vb0N-60" target="JMdF2OkQw6zPD9X_Vb0N-61">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="JMdF2OkQw6zPD9X_Vb0N-130" value="分发微调任务" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=18;" vertex="1" connectable="0" parent="JMdF2OkQw6zPD9X_Vb0N-128">
          <mxGeometry x="-0.1185" y="-1" relative="1" as="geometry">
            <mxPoint x="42" y="-29" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="JMdF2OkQw6zPD9X_Vb0N-129" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.75;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;curved=1;" edge="1" parent="JMdF2OkQw6zPD9X_Vb0N-1" source="JMdF2OkQw6zPD9X_Vb0N-60" target="JMdF2OkQw6zPD9X_Vb0N-13">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="JMdF2OkQw6zPD9X_Vb0N-131" value="分发批量推理任务" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=18;" vertex="1" connectable="0" parent="JMdF2OkQw6zPD9X_Vb0N-129">
          <mxGeometry x="-0.1492" relative="1" as="geometry">
            <mxPoint x="-23" y="-28" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="JMdF2OkQw6zPD9X_Vb0N-60" value="Celery&#xa;（异步队列任务）" style="whiteSpace=wrap;strokeWidth=2;fontSize=18;" vertex="1" parent="JMdF2OkQw6zPD9X_Vb0N-1">
          <mxGeometry x="392.4805668398677" y="461" width="208.03023145961265" height="90" as="geometry" />
        </mxCell>
        <mxCell id="JMdF2OkQw6zPD9X_Vb0N-126" value="&lt;font color=&quot;#000000&quot;&gt;&lt;span style=&quot;font-size: 25px;&quot;&gt;&lt;b&gt;Centralized Logging System&lt;/b&gt;&lt;/span&gt;&lt;/font&gt;" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#f5f5f5;fontColor=#333333;strokeColor=#666666;verticalAlign=top;" vertex="1" parent="JMdF2OkQw6zPD9X_Vb0N-1">
          <mxGeometry x="1001" y="724" width="305" height="379" as="geometry" />
        </mxCell>
        <mxCell id="JMdF2OkQw6zPD9X_Vb0N-127" value="集中式日志系统" style="whiteSpace=wrap;strokeWidth=2;fontSize=18;" vertex="1" parent="JMdF2OkQw6zPD9X_Vb0N-1">
          <mxGeometry x="1057.96" y="878" width="191.09" height="144" as="geometry" />
        </mxCell>
        <mxCell id="JMdF2OkQw6zPD9X_Vb0N-140" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.002;entryY=0.4;entryDx=0;entryDy=0;entryPerimeter=0;" edge="1" parent="JMdF2OkQw6zPD9X_Vb0N-1" source="JMdF2OkQw6zPD9X_Vb0N-3" target="JMdF2OkQw6zPD9X_Vb0N-126">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="JMdF2OkQw6zPD9X_Vb0N-141" value="&lt;span style=&quot;color: rgba(0, 0, 0, 0); font-family: monospace; font-size: 0px; text-align: start; background-color: rgb(236, 236, 236);&quot;&gt;%3CmxGraphModel%3E%3Croot%3E%3CmxCell%20id%3D%220%22%2F%3E%3CmxCell%20id%3D%221%22%20parent%3D%220%22%2F%3E%3CmxCell%20id%3D%222%22%20value%3D%22%26lt%3Bfont%20style%3D%26quot%3Bfont-size%3A%2018px%3B%26quot%3B%26gt%3B%E8%BE%93%E5%87%BA%E6%97%A5%E5%BF%97%26lt%3B%2Ffont%26gt%3B%22%20style%3D%22edgeLabel%3Bhtml%3D1%3Balign%3Dcenter%3BverticalAlign%3Dmiddle%3Bresizable%3D0%3Bpoints%3D%5B%5D%3B%22%20vertex%3D%221%22%20connectable%3D%220%22%20parent%3D%221%22%3E%3CmxGeometry%20x%3D%221249.789417885791%22%20y%3D%22441.8953715282614%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3C%2Froot%3E%3C%2FmxGraphModel%3E输出&lt;/span&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="JMdF2OkQw6zPD9X_Vb0N-140">
          <mxGeometry x="-0.0207" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="JMdF2OkQw6zPD9X_Vb0N-142" value="&lt;font style=&quot;font-size: 18px;&quot;&gt;输出日志&lt;/font&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="JMdF2OkQw6zPD9X_Vb0N-140">
          <mxGeometry x="0.0042" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
