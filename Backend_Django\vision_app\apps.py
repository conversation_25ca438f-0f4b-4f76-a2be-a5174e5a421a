from django.apps import AppConfig
import logging

logger = logging.getLogger(__name__)


class VisionAppConfig(AppConfig):
    default_auto_field = "django.db.models.BigAutoField"
    name = "vision_app"

    def ready(self):
        """Django应用启动时的初始化"""
        try:
            # 预加载ultralytics以避免运行时DLL问题
            logger.info("Preloading ultralytics module...")
            from ultralytics import YOLO
            logger.info("Successfully preloaded ultralytics module")
        except Exception as e:
            logger.warning(f"Failed to preload ultralytics: {str(e)}")
            # 不抛出异常，让应用继续启动
