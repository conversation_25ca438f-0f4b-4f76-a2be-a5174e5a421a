version: '3.8'

services:
  # 后端服务 - 热更新模式（用于服务器部署）
  backend:
    image: web_ai_vision_app-backend:latest
    pull_policy: never
    container_name: ai-vision-backend-hotreload
    restart: unless-stopped
    # 使用host网络模式以支持扫描器硬件访问
    network_mode: "host"
    env_file:
      - ./.env
    environment:
      - SECRET_KEY=${DJANGO_SECRET_KEY}
      - DJANGO_SETTINGS_MODULE=backend_project.settings_docker
      - PYTHONPATH=/app
      # 配置pip使用国内镜像
      - PIP_INDEX_URL=https://pypi.tuna.tsinghua.edu.cn/simple
      - PIP_TRUSTED_HOST=pypi.tuna.tsinghua.edu.cn
      # 局域网访问配置
      - ALLOWED_HOSTS=${ALLOWED_HOSTS}
      - CORS_ALLOW_ALL_ORIGINS=True
      # 开发模式配置
      - PYTHONUNBUFFERED=1
      - DJANGO_DEBUG=True
      - DJANGO_RELOAD=True
      # 网络配置统一管理
      - BACKEND_PORT=${BACKEND_PORT:-8000}
      - FRONTEND_DEV_PORT=${FRONTEND_DEV_PORT:-5173}
      - FRONTEND_PROD_PORT=${FRONTEND_PROD_PORT:-8080}
      - LAN_IPS=${LAN_IPS:-}
      - CORS_ORIGINS=${CORS_ORIGINS:-}
      # 扫描器硬件配置
      - SCANNER_ENABLED=${SCANNER_ENABLED:-true}
      - SCANNER_DEFAULT_IP=${SCANNER_DEFAULT_IP:-************}
      - SCANNER_DEFAULT_PORT=${SCANNER_DEFAULT_PORT:-9999}
      - SCANNER_TIMEOUT=${SCANNER_TIMEOUT:-30}
    volumes:
      # 代码热更新挂载 - 直接挂载后端代码
      - ./code-sync/Backend_Django:/app:rw
      # 持久化数据目录 - 在代码挂载之后，避免被覆盖
      - ./data/db:/app/db
      - ./data/models:/app/models
      - ./data/media:/app/media
      - ./data/logs:/app/logs
    # host网络模式下不需要端口映射
    # ports:
    #   - "${BACKEND_PORT:-8000}:${BACKEND_PORT:-8000}"
    # host网络模式下不需要networks配置
    # networks:
    #   - ai-vision-network
    command: >
      sh -c "
        echo 'Starting Django with hot reload...' &&
        python manage.py collectstatic --noinput --clear &&
        python manage.py migrate &&
        python manage.py runserver 0.0.0.0:8000
      "
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/vision/models/"]
      interval: 30s
      timeout: 30s
      retries: 5
      start_period: 120s

  # 前端服务 - 热更新模式
  frontend:
    image: web_ai_vision_app-frontend:latest
    pull_policy: never
    container_name: ai-vision-frontend-hotreload
    restart: unless-stopped
    environment:
      # nginx环境变量替换配置
      - NGINX_ENVSUBST_OUTPUT_DIR=/etc/nginx
      - NGINX_ENVSUBST_TEMPLATE_DIR=/etc/nginx/templates
      - NGINX_ENVSUBST_TEMPLATE_SUFFIX=.template
      # 后端服务器配置 - 后端使用host网络模式
      - BACKEND_HOST=host.docker.internal
      - BACKEND_PORT=8000
    volumes:
      # 代码热更新挂载 - 前端构建产物
      - ./code-sync/Frontend/dist:/usr/share/nginx/html:rw
      # 使用nginx配置模板支持环境变量
      - ./nginx-hotreload.conf.template:/etc/nginx/templates/nginx.conf.template:ro
      # 健康检查文件
      - ./scripts/health.json:/usr/share/nginx/html/health:ro
    ports:
      - "${FRONTEND_PROD_PORT:-8080}:80"
    depends_on:
      - backend
    networks:
      - ai-vision-network
    # 添加额外主机映射以支持host.docker.internal
    extra_hosts:
      - "host.docker.internal:host-gateway"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s

networks:
  ai-vision-network:
    driver: bridge

volumes:
  # 定义命名卷用于数据持久化
  db_data:
    driver: local
  models_data:
    driver: local
  media_data:
    driver: local
  logs_data:
    driver: local
