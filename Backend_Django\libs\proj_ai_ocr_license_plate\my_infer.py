from paddleocr import PaddleOCR
import os
import numpy as np
from PIL import Image, ImageDraw, ImageFont

# (Keep your existing setup for model paths and args_for_official_model)
# ...

# 指定你希望官方模型保存和加载的根目录
custom_model_root_dir = r"D:\DeskTop\web_ai_vision_app\Backend_Django\libs\proj_ai_ocr_license_plate\models"

# 构建检测、识别和（可选）分类模型的具体路径
official_det_model_dir = os.path.join(custom_model_root_dir, "Identity_card_det_model")
official_rec_model_dir = os.path.join(custom_model_root_dir, "Identity_card_rec_model")
official_cls_model_dir = os.path.join(custom_model_root_dir, "official_cls_model") # 如果使用 cls

# 确保这些目录存在
os.makedirs(official_det_model_dir, exist_ok=True)
os.makedirs(official_rec_model_dir, exist_ok=True)
os.makedirs(official_cls_model_dir, exist_ok=True)


# 配置参数
args_for_official_model = {
    'use_gpu': False,
    'use_angle_cls': False,
    'lang': 'en',
    'det_model_dir': official_det_model_dir,
    'rec_model_dir': official_rec_model_dir,
    'det_limit_side_len': 640,
    'det_limit_type': 'max',
    'det_db_thresh': 0.3,
    'det_db_box_thresh': 0.6,
    'max_text_length': 35,
    'drop_score': 0.5,
    'show_log': True,
}

print(f"Initializing PaddleOCR. Models will be loaded from/downloaded to:")
print(f"  Det model dir: {official_det_model_dir}")
print(f"  Rec model dir: {official_rec_model_dir}")
if args_for_official_model['use_angle_cls']:
    print(f"  Cls model dir: {official_cls_model_dir}")

try:
    ocr_engine = PaddleOCR(**args_for_official_model)
    print("PaddleOCR initialized successfully.")
except Exception as e:
    print(f"Error initializing PaddleOCR: {e}")
    exit()

# --- Visualisation settings ---
font_path = "C:\\Windows\\Fonts\\msyh.ttc" # 微软雅黑 (Microsoft YaHei)
if not os.path.exists(font_path):
    font_path = "C:\\Windows\\Fonts\\simhei.ttf" # 黑体 (SimHei)
    if not os.path.exists(font_path):
        font_path = "C:\\Windows\\Fonts\\arial.ttf" # Arial as a fallback
        if not os.path.exists(font_path):
            font_path = "./doc/fonts/simfang.ttf" # PaddleOCR's default
            if not os.path.exists(font_path):
                print("Warning: Suitable font not found. Text might not display correctly.")
                font_path = None

def draw_ocr_mirrored_text_highlights(image_path, ocr_results, output_path,
                                      font_path=None, # Font for text on right panel
                                      highlighter_color=(255, 255, 0, 70), # Yellow, translucent
                                      box_outline_color=(0, 150, 0, 200), # Outline color
                                      right_panel_text_color=(0, 0, 0), # Black text on right
                                      right_panel_bg_color=(245, 245, 245) # Light gray bg for right
                                     ):
    try:
        original_image = Image.open(image_path).convert("RGBA")
        img_width, img_height = original_image.size

        # --- Panel 1: Original image with highlights ---
        highlight_layer_left = Image.new('RGBA', (img_width, img_height), (0, 0, 0, 0))
        draw_highlight_left = ImageDraw.Draw(highlight_layer_left)

        # --- Panel 2: Blank canvas, same size, for mirrored highlights and text ---
        # Start with background color, then draw highlights and text on it
        image_right = Image.new('RGB', (img_width, img_height), right_panel_bg_color)
        # For drawing highlights on right panel with alpha, we need an RGBA layer, then paste
        highlight_layer_right = Image.new('RGBA', (img_width, img_height), (0,0,0,0)) # Transparent
        draw_highlight_right = ImageDraw.Draw(highlight_layer_right)
        draw_text_right = ImageDraw.Draw(highlight_layer_right) # Text will be drawn on this layer too


        for i, line_info in enumerate(ocr_results):
            points_orig = line_info[0]
            points_int = np.array(points_orig, dtype=np.int32)
            text = line_info[1][0]
            # score = line_info[1][1] # Not drawing score in this version

            # Get bounding box min/max for text fitting
            min_x = np.min(points_int[:, 0])
            max_x = np.max(points_int[:, 0])
            min_y = np.min(points_int[:, 1])
            max_y = np.max(points_int[:, 1])
            box_w = max_x - min_x
            box_h = max_y - min_y

            # --- Draw on Highlight Layer for Left Panel ---
            draw_highlight_left.polygon([tuple(p) for p in points_int], fill=highlighter_color)
            draw_highlight_left.polygon([tuple(p) for p in points_int], outline=box_outline_color, width=2)

            # --- Draw Highlight and Text on Right Panel's Highlight Layer ---
            # 1. Draw the same highlight box
            draw_highlight_right.polygon([tuple(p) for p in points_int], fill=highlighter_color)
            draw_highlight_right.polygon([tuple(p) for p in points_int], outline=box_outline_color, width=2)

            # 2. Draw text inside this highlight on the right panel
            # Attempt to scale font size to fit the box height, with a sensible minimum/maximum
            # Max font size can be a fraction of box_h, min can be e.g., 8
            target_font_size = max(8, min(int(box_h * 0.7), 24)) # Heuristic for font size
            try:
                font_for_text = ImageFont.truetype(font_path, target_font_size) if font_path else ImageFont.load_default(size=target_font_size)
            except IOError:
                font_for_text = ImageFont.load_default(size=target_font_size)

            # Calculate text size with the chosen font
            try:
                text_bbox_ = draw_text_right.textbbox((0,0), text, font=font_for_text)
                text_w = text_bbox_[2] - text_bbox_[0]
                text_h = text_bbox_[3] - text_bbox_[1]
            except AttributeError:
                text_w, text_h = draw_text_right.textsize(text, font=font_for_text)


            # --- Text Centering Logic within the box on the right panel ---
            # Center text within the bounding box (min_x, min_y, max_x, max_y)
            text_x = min_x + (box_w - text_w) / 2
            text_y = min_y + (box_h - text_h) / 2
            
            # Clip text coordinates to be within image boundaries (simple clipping)
            text_x = max(0, min(text_x, img_width - text_w))
            text_y = max(0, min(text_y, img_height - text_h))

            # Draw text on the right panel's highlight_layer_right (which is RGBA)
            # This layer will be alpha_composited onto the right panel's background
            draw_text_right.text((text_x, text_y), text,
                                 fill=right_panel_text_color + (255,), # Add full alpha for text
                                 font=font_for_text)


        # --- Composite Left Panel ---
        image_left_final = Image.alpha_composite(original_image, highlight_layer_left)

        # --- Composite Right Panel ---
        # First, create the base right panel with its background color
        image_right_base = Image.new('RGBA', (img_width, img_height), right_panel_bg_color + (255,)) # Opaque BG
        # Then, composite the highlights and text (which are on highlight_layer_right) onto this base
        image_right_final = Image.alpha_composite(image_right_base, highlight_layer_right)


        # --- Combine Left and Right Panels Side-by-Side ---
        combined_width = img_width * 2 # Two panels of the same width
        combined_image = Image.new('RGBA', (combined_width, img_height)) # Start with transparent

        combined_image.paste(image_left_final, (0, 0))
        combined_image.paste(image_right_final, (img_width, 0))

        # Convert to RGB before saving if needed (e.g., for JPG)
        combined_image_to_save = combined_image.convert("RGB")
        combined_image_to_save.save(output_path)
        print(f"Mirrored highlight/text result saved to {output_path}")

    except Exception as e:
        print(f"Error drawing mirrored results for {image_path}: {e}")
        import traceback
        traceback.print_exc()


# ... (rest of your script: image_dir setup, loop through image_files)
image_dir = "./test_images/id_card"
if not os.path.exists(image_dir):
    print(f"Error: Image directory '{image_dir}' not found.")
    exit()

image_files = [os.path.join(image_dir, fname) for fname in os.listdir(image_dir)
               if fname.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp', '.tiff'))]

if not image_files:
    print(f"No image files found in '{image_dir}'.")
    exit()

for img_path in image_files:
    print(f"Processing {img_path}...")
    try:
        ocr_output = ocr_engine.ocr(img_path, cls=args_for_official_model['use_angle_cls'])

        if ocr_output and ocr_output[0] is not None:
            results_to_draw = ocr_output[0]
            print(f"Detected {len(results_to_draw)} text regions.")

            base, ext = os.path.splitext(img_path)
            output_img_path = f"{base}_ocr_mirrored_highlights{ext}"
            
            draw_ocr_mirrored_text_highlights(
                img_path, results_to_draw, output_img_path,
                font_path=font_path,
                highlighter_color=(255, 230, 100, 70), # Lighter yellow, adjust alpha
                box_outline_color=(0,100,200, 180),
                right_panel_text_color = (0,0,0) # Black text
            )
        else:
            print("  No text detected or result is None.")
    except Exception as e:
        print(f"Error processing {img_path}: {e}")
        import traceback
        traceback.print_exc()

print("Inference finished.")