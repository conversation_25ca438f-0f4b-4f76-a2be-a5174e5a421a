---
description: Explore the Ultralytics distance calculation module. Learn to calculate distances between objects in real-time video streams with our comprehensive guide.
keywords: Ultralytics, distance calculation, object tracking, real-time video, centroid, distance estimation, YOLO, ML, cv2
---

# Reference for `ultralytics/solutions/distance_calculation.py`

!!! note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/solutions/distance_calculation.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/solutions/distance_calculation.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/solutions/distance_calculation.py) 🛠️. Thank you 🙏!

<br>

## ::: ultralytics.solutions.distance_calculation.DistanceCalculation

<br><br>
