#ifndef _VERSION_H
#define _VERSION_H

//-----------------------------------------------------------------------------
//  Includes

#include "autoconf.h"
//-----------------------------------------------------------------------------
//  Definitions

#ifndef MACR_AIENGINE_VERSION
#define MACR_AIENGINE_VERSION "\0VERSION_NOT_DEFINED\0Unique_Keyword-AIENGINE_VERSION"
#endif

#ifndef MACR_AIENGINE_BUILD_FLAGS
#define MACR_AIENGINE_BUILD_FLAGS "\0BUILD_FLAGS_NOT_DEFINED\0Unique_Keyword-AIENGINE_BUILD_FLAGS"
#endif
//-----------------------------------------------------------------------------
//  Declarations
static inline const char *_ai_engine_get_version(void)
{
    const char *ai_engine_version = MACR_AIENGINE_VERSION; // 版本号的起始字符是分隔符，用于确保静态查询版本号时，不会有其它内容混入版本号中
    return ai_engine_version + 1; // 跳过1个起始分割符
}

static inline const char *_ai_engine_get_build_flags(void)
{
    const char *build_flags = MACR_AIENGINE_BUILD_FLAGS; // 字符串的起始符号是分隔符
    return build_flags + 1; // 跳过1个起始分割符
}

#endif
//-----------------------------------------------------------------------------
//  End of file