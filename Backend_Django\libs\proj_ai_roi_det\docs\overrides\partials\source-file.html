{% import "partials/language.html" as lang with context %}

<!-- taken from
https://github.com/squidfunk/mkdocs-material/blob/master/src/partials/source-file.html -->

<br>
<div class="md-source-file">
    <small>

        <!-- mkdocs-git-revision-date-localized-plugin -->
        {% if page.meta.git_revision_date_localized %}
        📅 {{ lang.t("source.file.date.updated") }}:
        {{ page.meta.git_revision_date_localized }}
        {% if page.meta.git_creation_date_localized %}
        <br/>
        🎂 {{ lang.t("source.file.date.created") }}:
        {{ page.meta.git_creation_date_localized }}
        {% endif %}

        <!-- mkdocs-git-revision-date-plugin -->
        {% elif page.meta.revision_date %}
        📅 {{ lang.t("source.file.date.updated") }}:
        {{ page.meta.revision_date }}
        {% endif %}
    </small>
</div>
