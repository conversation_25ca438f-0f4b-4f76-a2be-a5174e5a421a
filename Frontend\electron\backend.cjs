const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');
const { app } = require('electron');

// --- 日志记录设置 (与main.cjs共享或类似逻辑) ---
// 注意：在backend.cjs中直接使用app.getPath可能不合适，因为它可能在app准备好之前被调用
// 一个更健壮的方法是从main.cjs传递logPath或log函数
// 这里为了简化，我们假设app已经ready或采用一个固定的后备路径
let logFilePath;
try {
  const userDataPath = app ? app.getPath('userData') : path.join(__dirname, 'logs'); // 后备路径
  if (app && !fs.existsSync(userDataPath)) {
    fs.mkdirSync(userDataPath, { recursive: true });
  }
  logFilePath = path.join(userDataPath, 'backend.log');
} catch (e) {
  // 如果在测试环境或者app未初始化，使用本地日志
  const logsDir = path.join(__dirname, 'logs');
  if (!fs.existsSync(logsDir)) {
    fs.mkdirSync(logsDir, { recursive: true });
  }
  logFilePath = path.join(logsDir, 'backend.log');
  console.error('Could not get userDataPath, using local backend.log', e);
}

function log(message) {
  const timestamp = new Date().toISOString();
  const logMessage = `${timestamp}: ${message}\n`;
  console.log(`(Backend Log): ${message}`);
  try {
    fs.appendFileSync(logFilePath, logMessage);
  } catch (err) {
    console.error('Failed to write to backend log file:', err);
  }
}

// 清理旧日志
try {
  if (fs.existsSync(logFilePath)) {
    fs.unlinkSync(logFilePath); 
  }
} catch (err) {
  console.error('Failed to delete old backend log file:', err);
}
log('Backend script initialized.');
// --- 日志记录设置结束 ---

const isDev = process.env.NODE_ENV === 'development';
let backendProcess = null;

function startBackend() {
  return new Promise((resolve, reject) => {
    try {
      log('Starting backend service attempt...');
      const rootDir = path.resolve(__dirname, '../../');
      
      const pythonExecutableName = process.platform === 'win32' ? 'python.exe' : 'python';
      
      const pythonPath = isDev
        ? path.join(rootDir, 'Backend_Django/venv/Scripts', pythonExecutableName)
        : path.join(process.resourcesPath, 'Backend_Django/venv/Scripts', pythonExecutableName);

      const scriptPath = isDev
        ? path.join(rootDir, 'Backend_Django/manage.py')
        : path.join(process.resourcesPath, 'Backend_Django/manage.py');

      log(`Environment: ${isDev ? 'development' : 'production'}`);
      log(`Python path: ${pythonPath}`);
      log(`Script path: ${scriptPath}`);

      if (!fs.existsSync(pythonPath)) {
        const errorMsg = `Python interpreter not found at: ${pythonPath}`;
        log(errorMsg);
        return reject(new Error(errorMsg));
      }
      if (!fs.existsSync(scriptPath)) {
        const errorMsg = `Django manage.py not found at: ${scriptPath}`;
        log(errorMsg);
        return reject(new Error(errorMsg));
      }

      log('Spawning Django process...');
      backendProcess = spawn(pythonPath, [scriptPath, 'runserver', '8000', '--noreload'], {
        stdio: 'pipe',
        env: {
          ...process.env,
          PYTHONUNBUFFERED: '1',
          DJANGO_DEBUG: isDev ? 'True' : 'False'
        }
      });

      backendProcess.stdout.on('data', (data) => {
        const output = data.toString();
        log(`Backend stdout: ${output.trim()}`);
        if (output.includes('Starting development server at') || output.includes('Quit the server with CTRL-BREAK')) {
          log('Django service confirmed running.');
          resolve();
        }
      });

      backendProcess.stderr.on('data', (data) => {
        const error = data.toString();
        log(`Backend stderr: ${error.trim()}`);
        // 不要因为警告而拒绝，除非是非常明确的错误指示
      });

      backendProcess.on('error', (error) => {
        log(`Backend process spawn error: ${error.stack || error}`);
        reject(error);
      });

      backendProcess.on('exit', (code, signal) => {
        log(`Backend process exited with code ${code} and signal ${signal}`);
        if (code !== 0 && code !== null) { // null code can happen if killed successfully
          const errorMsg = `Backend process exited unexpectedly with code ${code}.`;
          log(errorMsg); // Log it but don't necessarily reject promise here if startup was already resolved
        }
      });

      const timeout = isDev ? 45000 : 90000; // 增加超时时间
      log(`Setting backend startup timeout to ${timeout / 1000} seconds.`);
      const startupTimeout = setTimeout(() => {
        // 检查进程是否真的在运行，或者只是挂起
        if (backendProcess && !backendProcess.killed) {
            log(`Backend startup timeout (${timeout/1000}s). Attempting to kill process.`);
            // 尝试发送信号，如果进程未响应，可能需要更强制的杀死
            if (process.platform === 'win32') {
                spawn('taskkill', ['/pid', backendProcess.pid, '/f', '/t']);
            } else {
                backendProcess.kill('SIGKILL'); // 更强制的信号
            }
            reject(new Error('Backend startup timed out. Process was killed.'));
        } else if (!backendProcess) {
             reject(new Error('Backend startup timed out. Process was not initiated or already exited.'));
        }
      }, timeout);

      // 如果resolve了，清除超时
      backendProcess.stdout.once('data', (data) => {
        if (data.toString().includes('Starting development server at') || data.toString().includes('Quit the server with CTRL-BREAK')) {
          clearTimeout(startupTimeout);
        }
      });

    } catch (error) {
      log(`Failed to start backend service: ${error.stack || error}`);
      reject(error);
    }
  });
}

function stopBackend() {
  return new Promise((resolve) => {
    if (backendProcess && !backendProcess.killed) {
      log('Stopping backend service...');
      if (process.platform === 'win32') {
        log(`Killing backend process tree (PID: ${backendProcess.pid}) on Windows.`);
        spawn('taskkill', ['/pid', backendProcess.pid, '/f', '/t']);
      } else {
        log(`Sending SIGTERM to backend process (PID: ${backendProcess.pid}) on ${process.platform}.`);
        backendProcess.kill('SIGTERM');
        // Set a timeout to SIGKILL if it doesn't terminate gracefully
        setTimeout(() => {
            if (backendProcess && !backendProcess.killed) {
                log(`Backend process (PID: ${backendProcess.pid}) did not terminate with SIGTERM, sending SIGKILL.`);
                backendProcess.kill('SIGKILL');
            }
        }, 5000); // 5 seconds to terminate gracefully
      }
      backendProcess = null; // Clear immediately, actual process termination is async
      log('Backend service stop command issued.');
    }
    resolve();
  });
}

module.exports = {
  startBackend,
  stopBackend
}; 