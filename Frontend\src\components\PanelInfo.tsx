import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON>po<PERSON>, <PERSON>, Col, But<PERSON>, message, Spin } from 'antd';
import { CopyOutlined, CheckOutlined } from '@ant-design/icons';
import { useFunctionPanel } from '../contexts/FunctionPanelContext';
import { useImageWorkspace, DisplayableDetection } from '../contexts/ImageWorkspaceContext'; // Keep for currentImageInfo, imageList
import { useBarcodeDetectionParams, PreprocessingMethod } from '../contexts/BarcodeDetectionContext';
// Import OcrTask type from api.ts for availableOcrTasks state
import { OcrTask } from '../services/api'; 
// Remove OCR_TASK_OPTIONS and OcrTaskOption import, get relevant states from useOcrDetectionParams
import { useOcrDetectionParams } from '../contexts/OcrDetectionContext';
import { useFeatureMatchingTraditional } from '../contexts/FeatureMatchingTraditionalContext';
import { useFeatureMatchingModel } from '../contexts/FeatureMatchingModelContext'; // 导入模型匹配 Context
import BaseInfoPanel from './BaseInfoPanel'; // 导入 BaseInfoPanel 组件

const { Text } = Typography;

interface PanelInfoProps {
  // Props definition if needed in the future
}

// Helper to get display name for preprocessing method
const getPreprocessingDisplayName = (method: PreprocessingMethod): string => {
  switch (method) {
    case 'full_scale':
      return '全图缩放';
    case 'roi':
      return '指定ROI区域（自适应缩放）';
    default:
      // This case should ideally not be reached if PreprocessingMethod type is correctly synced
      // and the context provides a valid value.
      console.warn(`Unknown preprocessing method in PanelInfo: ${method}`);
      return method || '未知'; // Return the method itself or '未知'
  }
};

// Updated getOcrTaskLabel function to use availableOcrTasks from context
const getOcrTaskLabel = (
    taskValue: string,
    tasks: OcrTask[],
    loading: boolean,
    error: string | null
): string => {
    if (loading) return '加载中...';
    if (error) return '获取失败';
    if (!taskValue) return '未选择';
    const task = tasks.find(t => t.task_name === taskValue);
    if (!task) return taskValue;

    // 优先使用 display_name，如果为空则回退到 task_name
    return task.display_name || task.task_name;
};

const PanelInfo: React.FC<PanelInfoProps> = () => {
  const { selectedFunction } = useFunctionPanel();
  // Destructure detectionResults at the top level from useImageWorkspace
  const { currentImageInfo, imageList, detectionResults } = useImageWorkspace(); 
  const { preprocessingMethod, confidenceThreshold, batchProcessingInterval } = useBarcodeDetectionParams();
  // Get selectedTaskValue, ocrProcessingResults, and new task loading states from OcrDetectionContext
  const { 
      selectedTaskValue, 
      ocrProcessingResults, 
      availableOcrTasks, 
      ocrTasksLoading,
      ocrTasksError
  } = useOcrDetectionParams();
  const { isMatching, matchingResult } = useFeatureMatchingTraditional();
  const { isLoading: isModelMatching, matchingResult: modelMatchingResult } = useFeatureMatchingModel();
  const [copyAllLoading, setCopyAllLoading] = useState(false);
  const [copiedItems, setCopiedItems] = useState<{[key: string]: boolean}>({});

  // Dynamic title
  let title = "面板信息";
  if (selectedFunction && selectedFunction.name) {
    title = `${selectedFunction.name} 面板信息`;
  }

  // 添加一个通用的复制函数
  const copyToClipboard = async (text: string): Promise<boolean> => {
    try {
      // 创建一个临时的textarea元素
      const textarea = document.createElement('textarea');
      textarea.value = text;
      // 将textarea添加到文档中
      document.body.appendChild(textarea);
      // 选择文本
      textarea.select();
      // 尝试复制
      document.execCommand('copy');
      // 移除临时元素
      document.body.removeChild(textarea);
      return true;
    } catch (error) {
      console.error('复制失败:', error);
      return false;
    }
  };

  const renderBarcodeDetectionInfo = () => {
    if (!currentImageInfo) {
      return <Text>请先加载一张图片。</Text>;
    }

    // Use detectionResults from the top-level scope (already destructured)
    // No need to call useImageWorkspace() here again.
    const barcodeDetectionResultsFromWorkspace = detectionResults; 
    const detectionCount = barcodeDetectionResultsFromWorkspace?.filter(d => d.type === 'barcode').length;
    let detectionCountText = '--';
    if (barcodeDetectionResultsFromWorkspace === null) {
      detectionCountText = '--';
    } else if (typeof detectionCount === 'number') {
      detectionCountText = detectionCount.toString();
    }
    
    const barcodeDetections = barcodeDetectionResultsFromWorkspace?.filter(d => d.type === 'barcode') || [];
    
    // 检查是否有多张图片（用于多图推理提示）
    const hasMultipleImages = imageList && imageList.length > 1;

    return (
      <div style={{ display: 'flex', flexDirection: 'column', gap: '4px' }}>
        <Row justify="space-between" align="middle">
          <Col span={10}><Text strong>预处理方式:</Text></Col>
          <Col span={14}><Text>{getPreprocessingDisplayName(preprocessingMethod)}</Text></Col>
        </Row>
        <Row justify="space-between" align="middle">
          <Col span={10}><Text strong>置信度阈值:</Text></Col>
          <Col span={14}><Text>{confidenceThreshold.toFixed(2)} ({(confidenceThreshold * 100).toFixed(0)}%)</Text></Col>
        </Row>
        {hasMultipleImages && (
          <Row justify="space-between" align="middle">
            <Col span={10}><Text strong>推理间隔:</Text></Col>
            <Col span={14}><Text>{batchProcessingInterval}毫秒</Text></Col>
          </Row>
        )}
        <Row justify="space-between" align="middle">
          <Col span={10}><Text strong>检测数量:</Text></Col>
          <Col span={14}><Text>{detectionCountText}</Text></Col>
        </Row>
        {barcodeDetections.length > 0 && (
          <>
            <Row justify="space-between" align="middle" style={{ marginTop: '8px' }}>
              <Col>
                <Text strong>检测到的条码坐标:</Text>
                <Text type="secondary" style={{ fontSize: '11px', marginLeft: '4px' }}>
                  (顺序: [x1,y1,x2,y2])
                </Text>
              </Col>
            </Row>
            <div style={{
              maxHeight: '100px', // 可以根据需要调整高度
              overflowY: 'auto',
              border: '1px solid #d9d9d9',
              borderRadius: '2px',
              padding: '4px 6px',
              marginTop: '4px',
              fontSize: '12px'
            }}>
              {barcodeDetections.map((det, index) => (
                <div key={det.id || `barcode-coord-${index}`} style={{ marginBottom: '2px' }}>
                  <Text>
                    <Text strong>{det.displayIndex !== undefined ? det.displayIndex : (index + 1)}.</Text>
                    <Text style={{ marginLeft: '4px' }}>
                      {det.label || 'N/A'} [{det.box.map(coord => Math.round(coord)).join(', ')}]
                    </Text>
                  </Text>
                </div>
              ))}
            </div>
          </>
        )}
      </div>
    );
  };

  const renderOcrDetectionInfo = () => {
    if (!currentImageInfo) {
      return <Text>请先加载一张图片。</Text>;
    }

    const currentOcrResultsForPanel = ocrProcessingResults || [];
    const ocrDetectionCount = currentOcrResultsForPanel.length;

    const handleCopyAll = async () => {
      if (currentOcrResultsForPanel.length === 0) {
        message.warning('暂无可复制的文本内容');
        return;
      }
      
      setCopyAllLoading(true);
      // 只复制识别结果文本，每条之间用换行符分隔
      const allText = currentOcrResultsForPanel
        .map(result => result.label)
        .join('\n');
      
      try {
        const success = await copyToClipboard(allText);
        if (success) {
          message.success('已复制全部识别结果');
          // 设置所有项为已复制状态
          const newCopiedItems = currentOcrResultsForPanel.reduce((acc, result, index) => {
            acc[result.id || `ocr-res-${index}`] = true;
            return acc;
          }, {} as {[key: string]: boolean});
          setCopiedItems(newCopiedItems);
          // 3秒后重置状态
          setTimeout(() => {
            setCopiedItems({});
          }, 3000);
        } else {
          throw new Error('复制操作失败');
        }
      } catch (error) {
        message.error('复制失败，请重试');
      } finally {
        setCopyAllLoading(false);
      }
    };

    const handleCopySingle = async (result: DisplayableDetection, index: number) => {
      const itemKey = result.id || `ocr-res-${index}`;
      // 只复制识别结果文本
      const text = result.label;
      
      try {
        const success = await copyToClipboard(text);
        if (success) {
          message.success('已复制该条识别结果');
          // 设置当前项为已复制状态
          setCopiedItems(prev => ({ ...prev, [itemKey]: true }));
          // 3秒后重置状态
          setTimeout(() => {
            setCopiedItems(prev => {
              const newState = { ...prev };
              delete newState[itemKey];
              return newState;
            });
          }, 3000);
        } else {
          throw new Error('复制操作失败');
        }
      } catch (error) {
        message.error('复制失败，请重试');
      }
    };
    
    return (
      <div style={{ display: 'flex', flexDirection: 'column', gap: '4px' }}>
        <Row justify="space-between" align="middle">
          <Col span={10}><Text strong>OCR类型:</Text></Col>
          <Col span={14}><Text>{getOcrTaskLabel(selectedTaskValue, availableOcrTasks, ocrTasksLoading, ocrTasksError)}</Text></Col>
        </Row>
        <Row justify="space-between" align="middle">
          <Col span={10}><Text strong>结果数量:</Text></Col>
          <Col span={14}><Text>{ocrProcessingResults === null ? '--' : ocrDetectionCount}</Text></Col>
        </Row>
        <div>
          <div style={{ 
            display: 'flex', 
            justifyContent: 'space-between', 
            alignItems: 'center',
            marginTop: '4px', 
            marginBottom: '4px' 
          }}>
            <Text strong>
              识别结果列表:
              <Text type="secondary" style={{ fontSize: '12px', marginLeft: '4px' }}>
                (结果按图像中的位置大致排序)
              </Text>
            </Text>
            <Button
              type="text"
              icon={copyAllLoading ? <CheckOutlined style={{ color: '#52c41a' }} /> : <CopyOutlined />}
              size="small"
              onClick={handleCopyAll}
              loading={copyAllLoading}
              title="复制全部结果"
              style={{ 
                padding: '0 4px',
                color: copyAllLoading ? '#52c41a' : undefined 
              }}
            >
              {copyAllLoading ? '已复制' : '复制全部'}
            </Button>
          </div>
          <div style={{
            height: 'calc(100% - 80px)',
            minHeight: '120px',
            overflowY: 'auto',
            border: '1px solid #d9d9d9',
            borderRadius: '2px',
            padding: '4px 6px',
          }}>
            {ocrProcessingResults === null && !ocrTasksLoading ? (
              <Text type="secondary">暂无识别结果或正在处理中...</Text>
            ) : ocrTasksLoading ? (
                <Text type="secondary">OCR任务加载中...</Text>
            ) : currentOcrResultsForPanel.length === 0 ? (
              <Text type="secondary">未检测到文本内容。</Text>
            ) : (
              currentOcrResultsForPanel.map((result, index) => {
                const itemKey = result.id || `ocr-res-${index}`;
                const isCopied = copiedItems[itemKey];
                
                return (
                  <div
                    key={itemKey}
                    style={{
                      marginBottom: '4px',
                      paddingBottom: '2px',
                      borderBottom: index < currentOcrResultsForPanel.length - 1 ? '1px dashed #f0f0f0' : 'none',
                      fontSize: '12px',
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center'
                    }}
                  >
                    <div style={{ flex: 1 }}>
                      <Text strong>{result.displayIndex !== undefined ? result.displayIndex : (index + 1)}.</Text>
                      <Text style={{ marginLeft: '4px' }}>{result.label}</Text>
                      {result.confidence !== undefined && (
                        <Text type="secondary" style={{ fontSize: '12px', marginLeft: '4px' }}>
                          (置信度: {result.confidence.toFixed(3)}、字符数量: {result.label ? result.label.length : 0})
                        </Text>
                      )}
                    </div>
                    <Button
                      type="text"
                      icon={isCopied ? <CheckOutlined style={{ color: '#52c41a' }} /> : <CopyOutlined />}
                      size="small"
                      onClick={() => handleCopySingle(result, index)}
                      style={{ 
                        padding: '0 4px',
                        color: isCopied ? '#52c41a' : undefined 
                      }}
                    />
                  </div>
                );
              })
            )}
          </div>
        </div>
      </div>
    );
  };

  const renderFeatureMatchingInfo = () => {
    if (isMatching) {
        return (
            <div style={{ textAlign: 'center', padding: '20px' }}>
                <Spin tip="匹配中...">
                    <div style={{ minHeight: '60px' }} />
                </Spin>
            </div>
        );
    }

    if (!matchingResult) {
        return (
            <div style={{ fontSize: '13px' }}>
                <Typography.Title level={5} style={{ marginTop: 0 }}>功能说明</Typography.Title>
                <Typography.Paragraph type="secondary">
                    传统特征点匹配功能用于在一张较大的“目标”图像中，精确定位出一块与“模板”图像内容相匹配的区域。
                </Typography.Paragraph>

                <Typography.Title level={5}>使用方法</Typography.Title>
                <Typography.Paragraph type="secondary">
                    <ol style={{ paddingLeft: '20px', margin: 0 }}>
                        <li>
                            <b>提供模板:</b> 在左侧“模板”卡片中上传图片，或通过主视图的“框选工具”截取一个区域作为模板。
                        </li>
                        <li>
                            <b>提供目标:</b> 在右侧“目标”卡片中上传您要搜索的大图。
                        </li>
                        <li>
                            <b>执行匹配:</b> 设置好参数后，点击“开始匹配”按钮。
                        </li>
                    </ol>
                </Typography.Paragraph>

                <Typography.Title level={5}>算法简介</Typography.Title>
                <Typography.Paragraph type="secondary">
                    本功能基于经典的计算机视觉算法 <Text code>SIFT</Text> 或 <Text code>ORB</Text>。它们通过提取图像中独特的“关键点”（如角点、斑点）并计算其“描述符”，然后在两张图中寻找描述符相似的关键点对，最终确定模板在目标图中的位置、大小和旋转角度。
                </Typography.Paragraph>
            </div>
        );
    }

    if (matchingResult.status === 'error') {
        return <Alert message={`错误: ${matchingResult.message}`} type="error" showIcon />;
    }

    return (
        <div style={{ display: 'flex', flexDirection: 'column', gap: '4px' }}>
            <Row justify="space-between" align="middle">
                <Col span={10}><Text strong>使用算法:</Text></Col>
                <Col span={14}><Text>{matchingResult.algorithm_used}</Text></Col>
            </Row>
            <Row justify="space-between" align="middle">
                <Col span={10}><Text strong>模板关键点:</Text></Col>
                <Col span={14}><Text>{matchingResult.keypoints_info.template_keypoints}</Text></Col>
            </Row>
            <Row justify="space-between" align="middle">
                <Col span={10}><Text strong>目标关键点:</Text></Col>
                <Col span={14}><Text>{matchingResult.keypoints_info.target_keypoints}</Text></Col>
            </Row>
            <Row justify="space-between" align="middle">
                <Col span={10}><Text strong>优质匹配点:</Text></Col>
                <Col span={14}><Text>{matchingResult.keypoints_info.good_matches}</Text></Col>
            </Row>
            <Row justify="space-between" align="middle" style={{ marginTop: '8px', paddingTop: '8px', borderTop: '1px dashed #d9d9d9' }}>
                <Col span={10}><Text strong>处理耗时:</Text></Col>
                <Col span={14}>
                    <Text style={{ color: '#1890ff' }}>
                        {matchingResult.processing_time.toFixed(3)} 秒
                    </Text>
                </Col>
            </Row>
        </div>
    );
  };

  const renderFeatureMatchingModelInfo = () => {
    if (isModelMatching) {
        return (
            <div style={{ textAlign: 'center', padding: '20px' }}>
                <Spin tip="模型匹配中...">
                    <div style={{ minHeight: '60px' }} />
                </Spin>
            </div>
        );
    }

    if (!modelMatchingResult) {
        return (
            <div style={{ fontSize: '13px' }}>
                <Typography.Title level={5} style={{ marginTop: 0 }}>功能说明</Typography.Title>
                <Typography.Paragraph type="secondary">
                    基于深度学习模型的特征点匹配功能。它使用先进的神经网络模型来提取和匹配特征，通常在视角变化、光照变化和模糊等复杂场景下表现更佳。
                </Typography.Paragraph>
                <Typography.Title level={5}>使用方法</Typography.Title>
                <Typography.Paragraph type="secondary">
                    <ol style={{ paddingLeft: '20px', margin: 0 }}>
                        <li><b>选择模型:</b> 从下拉列表中选择一个预训练的特征匹配模型。</li>
                        <li><b>提供图片:</b> 上传模板和目标图片。</li>
                        <li><b>调整参数:</b> 根据需要微调关键点数量、NMS等参数。</li>
                        <li><b>执行匹配:</b> 点击“开始匹配”按钮。</li>
                    </ol>
                </Typography.Paragraph>
            </div>
        );
    }

    if (modelMatchingResult.status === 'error') {
        return <Alert message={`错误: ${modelMatchingResult.message}`} type="error" showIcon />;
    }
    
    const { data } = modelMatchingResult;

    return (
        <div style={{ display: 'flex', flexDirection: 'column', gap: '4px' }}>
            <Row justify="space-between" align="middle">
                <Col span={10}><Text strong>匹配状态:</Text></Col>
                <Col span={14}><Text style={{ color: modelMatchingResult.status === 'success' ? '#52c41a' : '#f5222d' }}>{modelMatchingResult.message}</Text></Col>
            </Row>
            <Row justify="space-between" align="middle">
                <Col span={10}><Text strong>模板关键点:</Text></Col>
                <Col span={14}><Text>{data.template_keypoints_count}</Text></Col>
            </Row>
            <Row justify="space-between" align="middle">
                <Col span={10}><Text strong>目标关键点:</Text></Col>
                <Col span={14}><Text>{data.target_keypoints_count}</Text></Col>
            </Row>
            <Row justify="space-between" align="middle">
                <Col span={10}><Text strong>优质匹配点:</Text></Col>
                <Col span={14}><Text>{data.match_count}</Text></Col>
            </Row>
            <Row justify="space-between" align="middle" style={{ marginTop: '8px', paddingTop: '8px', borderTop: '1px dashed #d9d9d9' }}>
                <Col span={10}><Text strong>处理耗时:</Text></Col>
                <Col span={14}>
                    <Text style={{ color: '#1890ff' }}>
                        {data.processing_time.toFixed(3)} 秒
                    </Text>
                </Col>
            </Row>
        </div>
    );
  };

  const renderDefaultInfo = () => {
    return <Text>请先选择功能，或查看通用图像信息。</Text>;
  };

  const renderContent = () => {
    switch (selectedFunction?.key) {
      case 'barcode-detection':
        return renderBarcodeDetectionInfo();
      case 'ocr-detection':
        return renderOcrDetectionInfo();
      case 'feature-matching-traditional':
        return renderFeatureMatchingInfo();
      case 'feature-matching-model':
        return renderFeatureMatchingModelInfo();
      default:
        return renderDefaultInfo();
    }
  };

  return (
    <BaseInfoPanel title={title}>
      {renderContent()}
    </BaseInfoPanel>
  );
};

export default PanelInfo;