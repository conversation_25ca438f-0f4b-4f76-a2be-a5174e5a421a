// import React from 'react'; // useRoutes 已经包含了 React
// import { Layout } from 'antd'; // 不再直接使用 Layout
// import { Allotment } from "allotment"; // 移到 DashboardPage
// import "allotment/dist/style.css"; // 移到 DashboardPage
// import MenuBar from './components/MenuBar'; // 移到 DashboardPage 或 Layout
// import StatusBar from './components/StatusBar'; // 移到 DashboardPage 或 Layout
// import FunctionTree from './components/FunctionTree'; // 移到 DashboardPage
// import ParameterPanel from './components/ParameterPanel'; // 移到 DashboardPage
// import ImageDisplay from './components/ImageDisplay'; // 移到 DashboardPage
// import ImageInfoPanel from './components/ImageInfoPanel'; // 移到 DashboardPage
import { useRoutes } from 'react-router-dom';
import { App as AntApp } from 'antd'; // 导入 antd 的 App 组件，并重命名以避免冲突
import { routes } from './router'; // 导入路由配置
import { ScannerProvider } from './contexts/ScannerContext'; // 导入ScannerProvider
import './App.css';

// const { Content } = Layout; // 不再需要

const AppContent = () => {
  const element = useRoutes(routes); // 使用 useRoutes hook 渲染路由
  return element;
};

const App = () => {
  // App 组件现在负责提供 antd 的上下文，并渲染路由
  return (
    <AntApp>
      <ScannerProvider>
        <AppContent />
      </ScannerProvider>
    </AntApp>
  );

  /* 原有的布局代码将移到 DashboardPage.tsx 或 DashboardLayout.tsx
  return (
    <Layout className="app-layout">
      <MenuBar />
      <Content>
        <Allotment>
          <Allotment.Pane preferredSize={200} minSize={150} maxSize={300}>
            <FunctionTree />
          </Allotment.Pane>
          <Allotment.Pane>
            <Allotment vertical>
              <Allotment.Pane>
                <ImageDisplay />
              </Allotment.Pane>
              <Allotment.Pane preferredSize={200} minSize={120} maxSize={350}>
                <ImageInfoPanel />
              </Allotment.Pane>
            </Allotment>
          </Allotment.Pane>
          <Allotment.Pane preferredSize={250} minSize={200} maxSize={400}>
            <ParameterPanel />
          </Allotment.Pane>
        </Allotment>
      </Content>
      <StatusBar />
    </Layout>
  );
  */
};

export default App;
