{"name": "ai-vision-platform", "description": "AI Vision Platform - An intelligent image processing and analysis application", "author": {"name": "Mindeo"}, "version": "1.0.0", "private": true, "type": "module", "main": "electron/main.cjs", "scripts": {"dev": "vite", "dev:lan": "cross-env VITE_BACKEND_URL=http://**************:9000 vite", "build": "npx tsc -b && npx vite build", "build:hotreload": "npx tsc -b && npx vite build --mode hotreload", "start": "vite preview --port 5173", "lint": "eslint .", "preview": "vite preview", "check-config": "node scripts/check-config.js", "electron:dev": "cross-env NODE_ENV=development concurrently \"npm run dev\" \"npm run electron:start\"", "electron:start": "cross-env NODE_ENV=development wait-on tcp:5173 && electron .", "electron:build": "npm run build && electron-builder", "electron:build:win": "npm run build && electron-builder --win", "electron:build:mac": "npm run build && electron-builder --mac", "electron:build:linux": "npm run build && electron-builder --linux"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@tanstack/react-query": "^5.74.11", "allotment": "^1.20.3", "antd": "^5.24.8", "axios": "^1.9.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^7.5.3", "react-zoom-pan-pinch": "^3.7.0", "socket.io-client": "^4.8.1"}, "devDependencies": {"@eslint/js": "^9.22.0", "@types/react": "^18.2.55", "@types/react-dom": "^18.2.19", "@vitejs/plugin-react": "^4.3.4", "concurrently": "^9.1.2", "cross-env": "^7.0.3", "electron": "^36.2.1", "electron-builder": "^26.0.12", "eslint": "^9.22.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.7", "globals": "^16.0.0", "typescript": "^5.2.2", "typescript-eslint": "^8.26.1", "vite": "^5.3.5", "wait-on": "^8.0.3"}}