import React, { useRef, useState } from 'react';
import { Button, Dropdown, Space, Typography, App } from 'antd';
import type { MenuProps } from 'antd';
// const { SubMenu } = Menu; // Import SubMenu
const { Title } = Typography; // 引入Typography中的Title组件
import { useNavigate } from 'react-router-dom'; // 导入useNavigate用于页面跳转
import { FileImageOutlined, FolderOpenOutlined, ClockCircleOutlined, SaveOutlined, PictureOutlined, UserOutlined, LogoutOutlined, ThunderboltOutlined, PoweroffOutlined } from '@ant-design/icons'; // 添加新图标
// import { useAuth } from '../contexts/AuthContext'; // 移除 useAuth
import { useImageWorkspace } from '../contexts/ImageWorkspaceContext'; // Import the hook
import type { RecentImageItem } from '../contexts/ImageWorkspaceContext'; // Import RecentImageItem type
import ExampleImagesModal from './ExampleImagesModal'; // 导入示例图片模态框组件
import AdminLoginModal from './AdminLoginModal'; // 导入管理员登录模态框组件
import { useAdmin } from '../contexts/AdminContext'; // 导入管理员Context
import { useScanner } from '../contexts/ScannerContext'; // 导入ScannerContext
import DeviceConnectModal from './DeviceConnectModal'; // 导入连接模态框

const MenuBar: React.FC = () => {
  // const { isAuthenticated, user, logout } = useAuth(); // 移除认证相关
 const {
   loadImage,
   loadFolder,
   startRoiSelection,
   recentlyOpenedImages,
   loadRecentImage,
   currentImageInfo, // Used for filename and enabling "Save As..."
   imageList,        // Added to get the original File object
   currentImageIndex, // Added to get the original File object
   getDisplayedViewDataURL // Added to get the displayed view with annotations
 } = useImageWorkspace(); // Use the context hook and get recent images related functions
  // const navigate = useNavigate(); // useNavigate 不再需要

  // 管理员Context
  const { isAdmin, logout: adminLogout } = useAdmin();

  // 使用App组件的message API
  const { message } = App.useApp();

  // Scanner Context
  const { isConnected, isStreaming, disconnect } = useScanner();
  const [isConnectModalVisible, setIsConnectModalVisible] = useState(false);

  // 获取设备状态指示灯颜色
  const getDeviceStatusColor = () => {
    if (!isConnected) return '#ff4d4f'; // 红色：未连接
    if (isConnected && !isStreaming) return '#fadb14'; // 黄色：已连接未推流
    return '#52c41a'; // 绿色：已连接且推流
  };

  // 获取设备状态描述
  const getDeviceStatusText = () => {
    if (!isConnected) return '设备未连接';
    if (isConnected && !isStreaming) return '设备已连接，未推流';
    return '设备已连接，正在推流';
  };

  // 页面导航
  const navigate = useNavigate();

  // 示例图片模态框状态
  const [exampleImagesModalVisible, setExampleImagesModalVisible] = useState(false);

  // 管理员登录模态框状态
  const [adminLoginModalVisible, setAdminLoginModalVisible] = useState(false);

  // Refs for hidden file inputs
  const imageInputRef = useRef<HTMLInputElement>(null);
  const folderInputRef = useRef<HTMLInputElement>(null);

  // const handleLogout = () => { // logout 功能移除
  //   logout();
  //   console.log('User logged out');
  // };

  // --- Handlers for file/folder selection ---
  const handleOpenImageClick = () => {
    imageInputRef.current?.click();
  };

  const handleOpenFolderClick = () => {
    folderInputRef.current?.click();
  };

  const handleImageInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files && files.length > 0) {
      console.log('Selected image file:', files[0]);
      loadImage(files[0]); // Call context action
    }
    event.target.value = '';
  };

  const handleFolderInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files) {
      console.log('Selected folder contents:', files);
      loadFolder(files); // Call context action
    }
    event.target.value = '';
  };

  // 处理示例图片菜单点击
  const handleExampleImagesClick = () => {
    console.log('Opening example images modal');
    setExampleImagesModalVisible(true);
  };

  // 关闭示例图片模态框
  const handleExampleImagesModalCancel = () => {
    setExampleImagesModalVisible(false);
  };

  // 处理管理员按钮点击
  const handleAdminButtonClick = () => {
    if (isAdmin) {
      // 如果已经是管理员，跳转到管理页面
      navigate('/admin');
    } else {
      // 如果不是管理员，显示登录模态框
      setAdminLoginModalVisible(true);
    }
  };

  // 处理管理员登出
  const handleAdminLogout = async () => {
    try {
      await adminLogout();
    } catch (error) {
      console.error('管理员登出失败:', error);
    }
  };

  // 关闭管理员登录模态框
  const handleAdminLoginModalCancel = () => {
    setAdminLoginModalVisible(false);
  };

  // 管理员登录成功回调
  const handleAdminLoginSuccess = () => {
    console.log('MenuBar: 管理员登录成功，跳转到管理页面');
    // 跳转到管理页面
    navigate('/admin');
    message.success('登录成功！欢迎使用管理后台');
  };
  // --- End Handlers ---

  const handleSaveAs = async () => {
    if (!currentImageInfo || !getDisplayedViewDataURL) {
      console.warn('No image or getDisplayedViewDataURL function to save for "Save As..."');
      message.warning('没有可保存的图像或无法获取图像数据');
      return;
    }

    let fileName = 'image.png'; // Default filename

    // Determine filename (using original file name if available, otherwise fallback)
    const currentFile = imageList && currentImageIndex >= 0 && imageList[currentImageIndex] ? imageList[currentImageIndex] : null;
    if (currentFile && currentFile.name) {
      const nameWithoutExtension = currentFile.name.substring(0, currentFile.name.lastIndexOf('.')) || currentFile.name;
      fileName = `${nameWithoutExtension}_annotated.png`; // Suffix to indicate it's the annotated view
    } else if (currentImageInfo && currentImageInfo.name) {
      const nameWithoutExtension = currentImageInfo.name.substring(0, currentImageInfo.name.lastIndexOf('.')) || currentImageInfo.name;
      fileName = `${nameWithoutExtension}_annotated.png`;
    } else {
      fileName = `annotated_image.png`; // Generic fallback
    }

    console.log(`Preparing to save displayed view as: ${fileName}`);
    message.loading({ content: '正在生成图像...', key: 'saveAsDisplayed' });

    try {
      const dataUrl = await getDisplayedViewDataURL();

      if (dataUrl) {
        const link = document.createElement('a');
        link.href = dataUrl;
        link.download = fileName;
        document.body.appendChild(link); // Required for Firefox
        link.click();
        document.body.removeChild(link); // Clean up
        // No need to revoke object URL here as dataUrl is a direct data URL, not an object URL from a blob.
        console.log(`Displayed view download triggered as ${fileName}`);
        message.success({ content: `图像已保存为 ${fileName}`, key: 'saveAsDisplayed', duration: 2 });
      } else {
        console.error('Failed to obtain data URL for displayed view.');
        message.error({ content: '保存带标注的图像失败，未能获取图像数据。', key: 'saveAsDisplayed', duration: 3 });
      }
    } catch (error) {
      console.error('Error getting or saving displayed view data URL:', error);
      message.error({ content: '保存带标注的图像时发生错误。', key: 'saveAsDisplayed', duration: 3 });
    }
  };

  // 定义文件下拉菜单项
 const recentImageSubMenuItems = (): MenuProps['items'] => {
   if (!recentlyOpenedImages || recentlyOpenedImages.length === 0) {
     return [{ key: 'no-recent', label: '(无最近图像)', disabled: true }];
   }
   return recentlyOpenedImages.map((item: RecentImageItem, index: number) => ({
     key: `recent-${item.id}-${index}`, // Ensure unique key
     label: item.name,
     title: `${item.name} (${item.type === 'local' ? '本地文件' : 'URL'}, ${new Date(item.timestamp).toLocaleString()})`, // Tooltip with more info
     onClick: async () => {
       console.log('Attempting to load recent image:', item);
       await loadRecentImage(item);
       // If it's a local file, loadImage in context will prompt user.
       // If it's a URL, current context logic shows a placeholder alert.
     },
   }));
 };

 const fileMenuItems: MenuProps['items'] = [
   {
      key: 'open-image',
      icon: <FileImageOutlined />,
      label: '打开图像...',
      onClick: handleOpenImageClick,
    },
    {
      key: 'open-folder',
      icon: <FolderOpenOutlined />,
      label: '打开文件夹...',
      onClick: handleOpenFolderClick,
    },
    {
      key: 'open-recent',
      icon: <ClockCircleOutlined />,
     label: '打开最近图像',
     // disabled: recentlyOpenedImages.length === 0, // Disable if no recent images - handled by SubMenu items instead
     children: recentImageSubMenuItems(),
   },
   {
      key: 'example-images',
      icon: <PictureOutlined />,
      label: '示例图片库...',
      onClick: handleExampleImagesClick,
    },
   {
      type: 'divider',
    },
    {
      key: 'save-as',
      icon: <SaveOutlined />,
      label: '另存为...',
      onClick: handleSaveAs,
      disabled: !currentImageInfo, // Enable only if an image is loaded
    },
  ];

  // 定义编辑下拉菜单项
  const editMenuItems: MenuProps['items'] = [
    {
      key: 'select-roi',
      label: '截取ROI范围',
      onClick: () => {
        console.log('"截取ROI范围" clicked'); // For debugging
        startRoiSelection();
      },
    },
    // 可以添加更多编辑菜单项...
  ];

  // 定义设备下拉菜单项
  const deviceMenuItems: MenuProps['items'] = [
    {
      key: 'connect-scanner',
      icon: <ThunderboltOutlined />,
      label: '连接扫描仪...',
      onClick: () => setIsConnectModalVisible(true),
      disabled: isConnected,
    },
    {
      key: 'disconnect-scanner',
      icon: <PoweroffOutlined />,
      label: '断开连接',
      onClick: disconnect,
      disabled: !isConnected,
    },
  ];

  // 定义帮助下拉菜单项 (如果帮助菜单也需要下拉)
  // const helpMenuItems: MenuProps['items'] = [ /* ... */ ];

  return (
    <div className="menu-bar" style={{
      backgroundColor: '#3280FC', // 用户指定的新蓝色 RGB(50, 128, 252)
      borderBottom: '1px solid #2A6EDC', // 相应的边框色
      color: '#ffffff', // 设置默认文字颜色为白色
      display: 'flex',
      alignItems: 'center',
      padding: '0 16px',
      height: '34px'
    }}>
      {/* 左侧菜单区域 - 使用 Space 来放置 Dropdown 和 Menu */}
      <Space size={0}> { /* Use Space to group File dropdown and Edit/Help menu */ }
          {/* 文件菜单 Dropdown */}
          <Dropdown menu={{ items: fileMenuItems }} placement="bottomLeft">
              {/* Use a Button or span that looks like a menu item */}
              <Button type="text" className="menu-btn" style={{ lineHeight: '32px', padding: '0 12px', color: '#ffffff' }}>
                  文件(F)
              </Button>
          </Dropdown>

          {/* 编辑菜单 Dropdown */}
          <Dropdown menu={{ items: editMenuItems }} placement="bottomLeft">
            <Button type="text" className="menu-btn" style={{ lineHeight: '32px', padding: '0 12px', color: '#ffffff' }}>
              编辑(E)
            </Button>
          </Dropdown>

          {/* 设备菜单 Dropdown */}
          <Dropdown menu={{ items: deviceMenuItems }} placement="bottomLeft">
            <Button 
              type="text" 
              className="menu-btn" 
              style={{ lineHeight: '32px', padding: '0 12px', color: '#ffffff', display: 'flex', alignItems: 'center', gap: '6px' }}
              title={getDeviceStatusText()}
            >
              设备(D)
              {/* 状态指示灯 */}
              <span 
                style={{
                  width: '10px',
                  height: '10px',
                  borderRadius: '50%',
                  backgroundColor: getDeviceStatusColor(),
                  border: '1px solid rgba(255, 255, 255, 0.3)',
                  boxShadow: `0 0 4px ${getDeviceStatusColor()}40`,
                  display: 'inline-block'
                }}
              />
            </Button>
          </Dropdown>

          {/* 帮助菜单 - 如果只是一个标签，可以保持简单 */}
          {/* 如果帮助也有子菜单，则也应改为 Dropdown */}
          <Button type="text" className="menu-btn" style={{ lineHeight: '32px', padding: '0 12px', color: '#ffffff' }}>
            帮助(H)
          </Button>
      </Space>

      {/* 中央平台标题 */}
      <div style={{
          position: 'absolute',
          left: '45%',
          transform: 'translateX(-50%)',
          height: '34px',
          display: 'flex',
          alignItems: 'center'
        }}>
        <Title
          level={4}
          style={{
            margin: 0,
            padding: 0,
            color: '#ffffff',
            fontSize: '16px',
            lineHeight: '32px',
            fontWeight: 'bold'
          }}
        >
          AI模型推理可视化平台 — Visualization For AI Model
        </Title>
      </div>

      {/* 隐藏的文件输入 */}
      <input
        type="file"
        ref={imageInputRef}
        style={{ display: 'none' }}
        accept="image/jpeg, image/png, image/bmp, image/webp, image/avif" // Update accepted types
        onChange={handleImageInputChange}
        aria-label="Open image file"
      />
      <input
        type="file"
        ref={folderInputRef}
        style={{ display: 'none' }}
        // Use type assertion to allow webkitdirectory
        {...({ webkitdirectory: "" } as any)}
        onChange={handleFolderInputChange}
        aria-label="Open folder"
      />

      {/* 右侧区域 */}
      <div style={{ marginLeft: 'auto', display: 'flex', alignItems: 'center' }}>
        {isAdmin ? (
          // 已登录管理员状态 - 显示管理员按钮和登出按钮
          <Space size="small">
            <Button
              type="text"
              icon={<UserOutlined />}
              onClick={handleAdminButtonClick}
              style={{
                color: '#ffffff',
                border: '1px solid rgba(255, 255, 255, 0.3)',
                borderRadius: '4px'
              }}
            >
              管理员
            </Button>
            <Button
              type="text"
              icon={<LogoutOutlined />}
              onClick={handleAdminLogout}
              style={{
                color: '#ffffff',
                border: '1px solid rgba(255, 255, 255, 0.3)',
                borderRadius: '4px'
              }}
              title="管理员登出"
            />
          </Space>
        ) : (
          // 未登录状态 - 显示登录按钮
          <Button
            type="text"
            icon={<UserOutlined />}
            onClick={handleAdminButtonClick}
            style={{
              color: '#ffffff',
              border: '1px solid rgba(255, 255, 255, 0.3)',
              borderRadius: '4px'
            }}
          >
            管理员
          </Button>
        )}
      </div>

      {/* 示例图片模态框 */}
      <ExampleImagesModal
        visible={exampleImagesModalVisible}
        onCancel={handleExampleImagesModalCancel}
      />

      {/* 管理员登录模态框 */}
      <AdminLoginModal
        visible={adminLoginModalVisible}
        onCancel={handleAdminLoginModalCancel}
        onSuccess={handleAdminLoginSuccess}
      />

      {/* 设备连接模态框 */}
      <DeviceConnectModal
        visible={isConnectModalVisible}
        onCancel={() => setIsConnectModalVisible(false)}
      />
    </div>
  );
};

export default MenuBar;