/**
 * 性能监控和优化工具
 */

// 性能监控配置
const PERFORMANCE_CONFIG = {
  // 是否启用性能监控
  enabled: import.meta.env.DEV, // 只在开发环境启用
  // 警告阈值（毫秒）
  warningThreshold: 100,
  // 错误阈值（毫秒）
  errorThreshold: 300,
};

/**
 * 性能计时器类
 */
export class PerformanceTimer {
  private startTime: number;
  private label: string;

  constructor(label: string) {
    this.label = label;
    this.startTime = performance.now();
  }

  /**
   * 结束计时并输出结果
   */
  end(): number {
    if (!PERFORMANCE_CONFIG.enabled) return 0;

    const duration = performance.now() - this.startTime;
    
    if (duration > PERFORMANCE_CONFIG.errorThreshold) {
      console.error(`🔴 Performance Issue: ${this.label} took ${duration.toFixed(2)}ms`);
    } else if (duration > PERFORMANCE_CONFIG.warningThreshold) {
      console.warn(`🟡 Performance Warning: ${this.label} took ${duration.toFixed(2)}ms`);
    } else {
      console.log(`✅ Performance OK: ${this.label} took ${duration.toFixed(2)}ms`);
    }

    return duration;
  }
}

/**
 * 防抖函数
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

/**
 * 节流函数
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}

/**
 * 异步操作性能监控装饰器
 */
export function measureAsync<T extends (...args: any[]) => Promise<any>>(
  func: T,
  label?: string
): T {
  return (async (...args: Parameters<T>) => {
    const timer = new PerformanceTimer(label || func.name);
    try {
      const result = await func(...args);
      timer.end();
      return result;
    } catch (error) {
      timer.end();
      throw error;
    }
  }) as T;
}

/**
 * 同步操作性能监控装饰器
 */
export function measureSync<T extends (...args: any[]) => any>(
  func: T,
  label?: string
): T {
  return ((...args: Parameters<T>) => {
    const timer = new PerformanceTimer(label || func.name);
    try {
      const result = func(...args);
      timer.end();
      return result;
    } catch (error) {
      timer.end();
      throw error;
    }
  }) as T;
}

/**
 * 延迟执行函数（优化版本）
 */
export function delay(ms: number): Promise<void> {
  return new Promise(resolve => {
    // 使用 requestAnimationFrame 优化短延迟
    if (ms < 16) {
      requestAnimationFrame(() => resolve());
    } else {
      setTimeout(resolve, ms);
    }
  });
}

/**
 * 批量处理函数
 */
export function batchProcess<T, R>(
  items: T[],
  processor: (item: T) => R | Promise<R>,
  batchSize: number = 10,
  delayBetweenBatches: number = 0
): Promise<R[]> {
  return new Promise(async (resolve, reject) => {
    const results: R[] = [];
    
    try {
      for (let i = 0; i < items.length; i += batchSize) {
        const batch = items.slice(i, i + batchSize);
        const batchResults = await Promise.all(batch.map(processor));
        results.push(...batchResults);
        
        // 批次间延迟，避免阻塞UI
        if (delayBetweenBatches > 0 && i + batchSize < items.length) {
          await delay(delayBetweenBatches);
        }
      }
      
      resolve(results);
    } catch (error) {
      reject(error);
    }
  });
}

/**
 * 内存使用监控
 */
export function logMemoryUsage(label: string = 'Memory Usage'): void {
  if (!PERFORMANCE_CONFIG.enabled) return;
  
  if ('memory' in performance) {
    const memory = (performance as any).memory;
    console.log(`📊 ${label}:`, {
      used: `${(memory.usedJSHeapSize / 1024 / 1024).toFixed(2)} MB`,
      total: `${(memory.totalJSHeapSize / 1024 / 1024).toFixed(2)} MB`,
      limit: `${(memory.jsHeapSizeLimit / 1024 / 1024).toFixed(2)} MB`,
    });
  }
}

/**
 * 长任务监控
 */
export function monitorLongTasks(): void {
  if (!PERFORMANCE_CONFIG.enabled || !('PerformanceObserver' in window)) return;
  
  try {
    const observer = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        console.warn(`🐌 Long Task detected: ${entry.duration.toFixed(2)}ms`);
      }
    });
    
    observer.observe({ entryTypes: ['longtask'] });
  } catch (error) {
    console.warn('Long task monitoring not supported');
  }
}

// 自动启动长任务监控
if (PERFORMANCE_CONFIG.enabled) {
  monitorLongTasks();
}
