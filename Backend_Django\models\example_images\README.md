# 示例图片库说明

## 目录结构

```
example_images/
├── barcode/          # 条码检测示例图片
├── ocr/             # OCR文字识别示例图片
├── ai_restored/     # AI图像修复示例图片
└── README.md        # 本说明文件
```

## 功能说明

示例图片库为业务人员和测试人员提供预设的示例图像，用于快速测试和演示AI视觉功能。

### 支持的功能

1. **按功能分类展示**：图片按AI功能（条码检测、OCR识别、AI修复）分类展示
2. **多选加载**：支持单选或多选图片加载到工作区
3. **热更新**：支持动态添加新图片，无需修改代码
4. **详细信息**：显示图片尺寸、文件大小等信息

### 支持的图片格式

- JPEG (.jpg, .jpeg)
- PNG (.png)
- BMP (.bmp)
- WebP (.webp)
- AVIF (.avif)

## 使用方法

### 添加示例图片

1. 将图片文件放入对应的功能目录：
   - `barcode/` - 条码检测示例图片
   - `ocr/` - OCR文字识别示例图片
   - `ai_restored/` - AI图像修复示例图片

2. 图片会自动被系统识别，无需重启服务

### 访问示例图片

1. 在前端菜单栏点击 "文件" → "示例图片库..."
2. 在弹出的模态框中选择功能分类
3. 选择需要的图片（支持多选）
4. 点击"加载到工作区"按钮

## 图片命名建议

为了更好的用户体验，建议按以下规则命名图片：

### 条码检测示例
- `qr_code_sample.jpg` - QR码示例
- `barcode_128_sample.png` - Code128条码示例
- `datamatrix_sample.jpg` - DataMatrix码示例

### OCR识别示例
- `chinese_text_sample.jpg` - 中文文字示例
- `english_text_sample.png` - 英文文字示例
- `license_plate_sample.jpg` - 车牌识别示例
- `document_scan_sample.png` - 文档扫描示例

### AI图像修复示例
- `low_resolution_sample.jpg` - 低分辨率图片
- `blurry_image_sample.png` - 模糊图片
- `noisy_image_sample.jpg` - 噪声图片
- `old_photo_sample.png` - 老照片修复

## 技术实现

### 后端API

- **获取图片列表**: `GET /api/vision/example-images/`
- **获取图片文件**: `GET /api/vision/example-images/{category}/{filename}`

### 前端组件

- **ExampleImagesModal**: 示例图片选择模态框
- **MenuBar**: 菜单栏集成

### 配置

在 `Backend_Django/backend_project/settings.py` 中配置：

```python
# 示例图片存储根目录
EXAMPLE_IMAGES_ROOT = BASE_DIR / 'models' / 'example_images'
```

## 注意事项

1. **文件大小**：建议单个图片文件不超过10MB
2. **图片质量**：建议使用清晰、有代表性的示例图片
3. **版权问题**：确保使用的图片没有版权问题
4. **安全性**：系统会验证文件路径，防止路径遍历攻击
5. **缓存**：图片文件会被浏览器缓存1小时，提高加载速度

## 故障排除

### 图片不显示
1. 检查文件格式是否支持
2. 检查文件路径是否正确
3. 检查文件权限是否可读

### API错误
1. 检查后端服务是否正常运行
2. 检查网络连接是否正常
3. 查看浏览器控制台错误信息

### 加载失败
1. 检查图片文件是否损坏
2. 检查文件大小是否过大
3. 检查网络带宽是否充足
