from __future__ import annotations
import cv2
import json
import copy
import math
import random
import shutil
import numpy as np
from shapely.geometry import Polygon, MultiPolygon

from typing import TypedDict, get_type_hints
from pathlib import Path, PosixPath

class LabelmeShape(TypedDict):
    """
    Labelme中的shapes属性

    Args:
        label (str): 目标类别
        points (list[list[int, int]]): 目标的坐标
        group_id (str): 分组
        shape_type (str): 坐标类型
        flags (dict): 标志
    """
    label: str
    points: list[list[int, int]]
    group_id: str
    shape_type: str
    flags: dict


class LabelmeDict(TypedDict):
    """
    Labelme标签

    Args:
        version (str): Labelme版本号
        flags (list): 标志
        shapes (list[LabelmeShape]): 各个目标的形状
        imagePath (str): 图片相对路径
        imageData (str): 图片数据
        imageHeight (int): 图片高度
        imageWidth (int): 图片宽度
        srcPath (str): 原始图片路径
    """
    version: str
    flags: dict
    shapes: list[LabelmeShape]
    imagePath: str
    imageData: str
    imageHeight: int
    imageWidth: int
    srcPath: str


class LabelYOLO():
    def __init__(
        self,
        cls: int,
        x_center: float,
        y_center: float,
        width: float,
        height: float,
        is_norm: bool,
        ) -> None:
        """
        YOLO标签

        Args:
            cls (int): 标签类别
            x_center (float): 目标中心x轴坐标
            y_center (float): 目标中心y轴坐标
            width (float): 目标宽度
            height (float): 目标高度
            is_norm (bool): 坐标是否归一化
        """
        self.cls = cls
        self.x_center: float = x_center
        self.y_center: float = y_center
        self.width: float = width
        self.height: float = height
        self.is_norm: bool = is_norm

    def __str__(
            self
        ) -> str:
        """
        打印YOLO标签
        """
        return f"{self.cls} {self.x_center} {self.y_center} {self.width} {self.height}"


    def Convert_To_VOC(
            self
        ) -> LabelVOC:
        """
        转换VOC标签为YOLO标签

        Returns:
            (LabelVOC): 转换得到的VOC标签
        """
        x_min = self.x_center - self.width/2.0
        y_min = self.y_center - self.height/2.0
        return LabelVOC(
            cls = self.cls,
            x_min = x_min,
            y_min = y_min,
            x_max = x_min + self.width,
            y_max = y_min + self.height,
            is_norm = self.is_norm
        )


    def Normalize(
            self,
            img_w: float,
            img_h: float
        ) -> None:
        """
        对标签做归一化

        Args:
            img_w (float): 图像宽度
            img_h (float): 图像高度
        """
        if self.is_norm == True:
            return

        self.x_center = self.x_center / img_w
        self.y_center = self.y_center / img_h
        self.width = self.width / img_w
        self.height = self.height / img_h
        self.is_norm = True


    def Denormalize(
            self,
            img_w: float,
            img_h: float
        ) -> None:
        """
        对标签去归一化

        Args:
            img_w (float): 图像宽度
            img_h (float): 图像高度
        """
        if self.is_norm == False:
            return

        self.x_center = self.x_center * img_w
        self.y_center = self.y_center * img_h
        self.width = self.width * img_w
        self.height = self.height * img_h
        self.is_norm = False


class LabelYOLOSeg():
    def __init__(
        self,
        cls: int,
        points: list[list[int, int]],
        is_norm: bool,
        ) -> None:
        """
        YOLO标签

        Args:
            cls (int): 标签类别
            x_center (float): 目标中心x轴坐标
            y_center (float): 目标中心y轴坐标
            width (float): 目标宽度
            height (float): 目标高度
            points (list[list[int, int]]): 坐标点
            is_norm (bool): 坐标是否归一化
        """
        self.cls = cls
        self.points: list[list[int, int]] = points
        self.is_norm: bool = is_norm


    def __str__(
            self
        ) -> str:
        """
        打印YOLO标签
        """
        points = ' '.join([f"{point[0]} {point[1]}" for point in self.points])
        return f"{self.cls} {points}"


    def Normalize(
            self,
            img_w: float,
            img_h: float
        ) -> None:
        """
        对标签做归一化

        Args:
            img_w (float): 图像宽度
            img_h (float): 图像高度
        """
        if self.is_norm == True:
            return

        for point in self.points:
            point[0] = point[0] / img_w
            point[1] = point[1] / img_h
        self.is_norm = True


    def Denormalize(
            self,
            img_w: float,
            img_h: float
        ) -> None:
        """
        对标签去归一化

        Args:
            img_w (float): 图像宽度
            img_h (float): 图像高度
        """
        if self.is_norm == False:
            return

        for point in self.points:
            point[0] = point[0] * img_w
            point[1] = point[1] * img_h
        self.is_norm = False


class LabelVOC():
    def __init__(
        self,
        cls: int,
        x_min: float,
        y_min: float,
        x_max: float,
        y_max: float,
        is_norm: bool,
        ) -> None:
        """
        VOC标签

        Args:
            cls (int): 标签类别
            x_min (float): 左上角点x轴坐标
            y_min (float): 左上角点y轴坐标
            x_max (float): 右下角点x轴坐标
            y_max (float): 右下角点y轴坐标
            is_norm (bool): 坐标是否归一化
        """
        self.cls = cls
        self.x_min: float = x_min
        self.y_min: float = y_min
        self.x_max: float = x_max
        self.y_max: float = y_max
        self.is_norm: bool = is_norm

    def __str__(
            self
        ) -> str:
        """
        打印VOC标签
        """
        return f"{self.cls} {self.x_min} {self.y_min} {self.x_max} {self.y_max}"


    def Convert_To_YOLO(
            self
        ) -> LabelYOLO:
        """
        转换VOC标签为YOLO标签

        Returns:
            (LabelYOLO): 转换得到的YOLO标签
        """
        width = self.x_max - self.x_min
        height = self.y_max - self.y_min
        return LabelYOLO(
            cls = self.cls,
            x_center = self.x_min + width/2.0,
            y_center = self.y_min + height/2.0,
            width = width,
            height = height,
            is_norm = self.is_norm
        )


    def Normalize(
            self,
            img_w: float,
            img_h: float
        ) -> None:
        """
        对标签做归一化

        Args:
            img_w (float): 图像宽度
            img_h (float): 图像高度
        """
        if self.is_norm == True:
            return
        self.x_min=self.x_min / img_w
        self.y_min=self.y_min / img_h
        self.x_max=self.x_max / img_w
        self.y_max=self.y_max / img_h
        self.is_norm = True


    def Denormalize(
            self,
            img_w: float,
            img_h: float
        ) -> None:
        """
        对标签去归一化

        Args:
            img_w (float): 图像宽度
            img_h (float): 图像高度
        """
        if self.is_norm == False:
            return

        self.x_min=self.x_min * img_w
        self.y_min=self.y_min * img_h
        self.x_max=self.x_max * img_w
        self.y_max=self.y_max * img_h
        self.is_norm = False


class DatasetYOLO():
    def __init__(self, classes:list[str]) -> None:
        """
        Args:
            classes (list[str]): 检测目标类别
        """
        #todo 改classes 为(list[dict[str, list[str]]])
        self.classes:list[str] = classes # 目标类别
        self.cls_idx:dict[str, int] = {} # 类别索引
        for idx, cls in enumerate(classes):
            self.cls_idx[cls] = idx


    def _read_labelme_file(
            self,
            label_file: PosixPath,
        ) -> tuple[LabelmeDict, list[LabelmeShape]]:
        """
        读Labelme标签文件

        Args:
            label_file (PosixPath): Labelme标签文件路径

        Returns:
            (tuple[LabelmeDict, LabelmeShape]): Labelme标签文件内容，和仅保留需要类别的LabelmeShape列表
        """
        # 读取标签内容
        label_content:LabelmeDict = {}
        with open(label_file, "r") as file:
            label_content = json.loads(file.read())

        # 获取需要的类别
        shapes:list[LabelmeShape] = [x for x in label_content["shapes"] if x["label"] in self.classes]

        # 返回结果
        return label_content, shapes


    def _save_label_and_image(
            self,
            label_file: str,
            label_content: LabelmeDict,
            tag: str,
            dst_path: str,
            image: cv2.Mat | None = None,
            img_suffix: str | None = None,
            only_label: bool = False,
            record_src_path: bool = False,
        ) -> None:
        """
        保存标签和图片

        Args:
            label_file (PosixPath): 标签文件路径
            label_content (LabelmeDict): 标签内容
            tag (str): 数据标记
            dst_path (str): 保存路径
            img_suffix (str | None): 指定图片后缀
            only_label (bool): 只保存标签文件
            record_src_path (bool): 记录原始标签路径
        """
        # 初始化变量
        label_file:Path = Path(label_file)
        label_path:Path = label_file.parent # 标签文件所在路径
        # 图片存放相对路径
        src_img_file_rel = Path(label_content["imagePath"])
        # 设置保存文件名
        sample_name = f"{src_img_file_rel.stem}-{tag}" # 样本名
        img_suffix = src_img_file_rel.suffix if img_suffix is None else img_suffix
        save_img_file = f"{sample_name}{img_suffix}" # 保存文件名
        label_content["imagePath"] = save_img_file
        if image is None:
            # 不另外保存图片
            if not only_label:
                shutil.copy(Path(label_file.parent, src_img_file_rel), Path(dst_path, save_img_file)) # 保存图片文件
            else:
                # 只保存标签时，保存标签的绝对路径
                label_content["imagePath"] = Path(label_path.absolute(), src_img_file_rel).as_posix()
        else:
            cv2.imwrite(Path(dst_path, save_img_file).as_posix(), image)

        # 记录标签原始路径
        if record_src_path:
            label_content["srcPath"] = Path(label_path.absolute(), src_img_file_rel).as_posix()

        # 创建标签和图片文件
        with open(Path(dst_path, f"{sample_name}.json"), "w") as file:
            file.write(json.dumps(label_content, indent=4))


    def Distribute_Data_Set(
            self,
            src_pathes: dict[list[str], float],
            train: float,
            val: float,
            dst_path: str,
            append_mode: bool = False,
            only_label: bool = False,
            seed: int = 20000330,
            ) -> tuple[str] | None:
        """
        基于Labelme标签划分训练集、验证集和测试集

        Args:
            src_pathes (dict[list[str], float]): 源文件路径列表
            train (float): 训练数据集比例
            val (float): 验证数据集比例
            dst_path (str): 目标生成路径
            append_mode (bool): 是否采用追加模式，追加模式下会在保留原有数据集的基础上增加新数据
            only_label (bool): 只保存标签文件
            seed (int): 随机种子，用于确保数据集划分的结果不变

        Returns:
            (tuple[str] | None): 训练集、验证集和测试集位置，失败时返回None
        """
        if dst_path in src_pathes.keys():
            print("src_path and dst_path can't be the same.")
            return None

        # 初始化输出路径
        train_path = Path(dst_path, "train") # 训练集路径
        val_path = Path(dst_path, "val") # 验证集路径
        test_path = Path(dst_path, "test") # 测试集路径

        # 非追加模式下，移除原有路径
        if append_mode == False:
            shutil.rmtree(train_path, ignore_errors=True)
            shutil.rmtree(val_path, ignore_errors=True)
            shutil.rmtree(test_path, ignore_errors=True)

        # 创建输出路径
        train_path.mkdir(parents=True, exist_ok=True)
        val_path.mkdir(parents=True, exist_ok=True)
        test_path.mkdir(parents=True, exist_ok=True)

        # 遍历源路径列表
        index = 0
        for src_path in src_pathes.keys():
            # 获取源路径下全部标签文件
            src = Path(src_path) # 标签路径
            proportion = min(1.0, src_pathes[src_path]) # 数据分配比例
            label_files:list[PosixPath] = list(src.glob(r"*.json"))

            # 按照特异样本划分标签
            label_dict:dict[str, list[PosixPath]] = {}
            for label_file in label_files:
                label_index = label_file.stem # 获取标签命名
                # 当标签命名通过'-'划分时，认为该样本有多个对应标签
                if '-' not in label_index:
                    label_indexes = f"{label_file.parent}/{label_file.stem}"
                    label_dict[label_indexes] = [label_file] # 单个样本单个标签
                    continue
                # 单个样本多个标签
                label_indexes = f"{label_file.parent}/{label_file.stem.split('-')[0]}"
                label_list = label_dict.get(label_indexes, [])
                label_list.append(label_file)
                label_dict[label_indexes] = label_list

            total_samples:int = len(label_dict) # 样本总数
            if total_samples <= 0:
                continue

            # 计算训练集图像数量
            num_train_samples:int = round(total_samples * train)
            num_train_samples = max(0, num_train_samples)
            remain_samples:int = total_samples - num_train_samples # 计算剩余图像数量

            # 计算验证集图像数量
            num_val_samples:int = round(total_samples * val)
            num_val_samples = max(0, num_val_samples)
            num_val_samples = min(num_val_samples, remain_samples) # 不能分配超过剩余图像数量的图片
            remain_samples = remain_samples - num_val_samples

            # 计算测试集图像数量
            num_test_samples = remain_samples # 剩余图像分配给测试集

            # 获取标签和图片路径
            random.seed(seed)
            label_indexes = list(label_dict.keys())
            random.shuffle(label_indexes) # 打乱数据顺序

            # 分配训练集
            start = 0 # 起始位置
            end = start + int(num_train_samples * proportion) # 终止位置
            for label_index in label_indexes[start : end]:
                # 逐个添加标签
                label_list = label_dict.get(label_index, [])
                for label_file in label_list:
                    label_content, _ = self._read_labelme_file(label_file)
                    self._save_label_and_image(label_file, label_content, str(index), train_path, only_label=only_label, record_src_path=True)
                    index += 1

            # 分配验证集
            start = num_train_samples
            end = start + int(num_val_samples * proportion)
            for label_index in label_indexes[start : end]:
                # 逐个添加标签
                label_list = label_dict.get(label_index, [])
                for label_file in label_list:
                    label_content, _ = self._read_labelme_file(label_file)
                    self._save_label_and_image(label_file, label_content, str(index), val_path, only_label=only_label, record_src_path=True)
                    index += 1

            # 分配测试集
            start = num_train_samples + num_val_samples
            end = start + int(num_test_samples * proportion)
            for label_index in label_indexes[start : end]:
                # 逐个添加标签
                label_list = label_dict.get(label_index, [])
                for label_file in label_list:
                    label_content, _ = self._read_labelme_file(label_file)
                    self._save_label_and_image(label_file, label_content, str(index), test_path, only_label=only_label, record_src_path=True)
                    index += 1

        return (train_path, val_path, test_path)


    def Invert_Image(
            self,
            src_pathes: list[str],
            dst_path: str,
        ) -> str | None:
        """
        对图片做反色处理

        Args:
            src_pathes (list[str]): 源文件路径列表
            dst_path (str): 目标生成路径

        Returns:
            (str | None): 生成数据位置，失败时返回None
        """
        # 判断传入参数是否合法
        if dst_path in src_pathes:
            return None

        dst = Path(dst_path) # 目标路径

        # 创建存储路径
        shutil.rmtree(dst, ignore_errors=True)
        dst.mkdir(parents=True, exist_ok=True)

        # 获取标签文件
        label_files:list[PosixPath] = []
        for src_path in src_pathes:
            src = Path(src_path)
            label_files.extend(list(src.glob(r"*.json")))

        # 逐个处理图片
        tag = "inv"
        for idx, label_file in enumerate(label_files):
            # 读取标签内容和每个类别的形状
            label_content, _ = self._read_labelme_file(label_file)

            # 读取图片
            src_img_path = label_file.parent # 图片存放起始路径
            src_img_file_rel = Path(label_content["imagePath"]) # 图片存放相对路径
            src_img_file = Path(src_img_path, src_img_file_rel) # 图片存放路径
            image = cv2.imread(src_img_file.as_posix(), cv2.IMREAD_GRAYSCALE)

            # 反色
            inverted_img = cv2.bitwise_not(image)
            # # 可视化
            # cv2.imshow("", inverted_img)
            # cv2.waitKey()

            # 保存图片
            final_tag = f"{tag}-{idx}"
            self._save_label_and_image(label_file, label_content, final_tag, dst_path, image=inverted_img)

        return dst_path


    def Adjust_Contrast(
            self,
            src_pathes: list[str],
            alpha_range: tuple[float],
            dst_path: str,
            beta_range: tuple[float]=(0.0, 0.0),
            seed: int = 20241226,
        ) -> str | None:
        """
        调整图像对比度

        Args:
            src_pathes (list[str]): 源文件路径列表
            alpha_range (tuple[float]): 对比度控制参数选值范围，小于1.0降低对比度，大于1.0增加对比度
            dst_path (str): 目标生成路径
            beta_range (tuple[float]): 亮度控制参数选值范围，小于0降低亮度，大于0增加亮度
            seed (int): 随机种子，用于确保每次旋转的结果相同

        Returns:
            (str | None): 生成数据位置，失败时返回None
        """
        # 判断传入参数是否合法
        if dst_path in src_pathes:
            return None

        random.seed(seed) # 设置随机种子
        dst = Path(dst_path) # 目标路径

        # 创建存储路径
        shutil.rmtree(dst, ignore_errors=True)
        dst.mkdir(parents=True, exist_ok=True)

        # 获取标签文件
        label_files:list[PosixPath] = []
        for src_path in src_pathes:
            src = Path(src_path)
            label_files.extend(list(src.glob(r"*.json")))

        # 限制对比度调整范围
        alpha_range = (max(0.0, alpha_range[0]), min(3.0, alpha_range[1]))

        # 逐个处理图片
        tag = "adct"
        for idx, label_file in enumerate(label_files):
            # 读取标签内容和每个类别的形状
            label_content, _ = self._read_labelme_file(label_file)

            # 读取图片
            src_img_path = label_file.parent # 图片存放起始路径
            src_img_file_rel = Path(label_content["imagePath"]) # 图片存放相对路径
            src_img_file = Path(src_img_path, src_img_file_rel) # 图片存放路径
            image = cv2.imread(src_img_file.as_posix(), cv2.IMREAD_GRAYSCALE)

            # 调整对比度
            alpha = random.uniform(*alpha_range)
            beta = random.uniform(*beta_range)
            target_img = cv2.convertScaleAbs(image, alpha=alpha, beta=beta)
            # # 可视化
            # cv2.imshow("", inverted_img)
            # cv2.waitKey()

            # 保存图片
            final_tag = f"{tag}-{idx}"
            self._save_label_and_image(label_file, label_content, final_tag, dst_path, image=target_img)

        return dst_path


    def Crop_Ref(
            self,
            src_pathes: list[str],
            size: tuple[int, int],
            ref_classes: list[str],
            dst_path: str,
            maintain_ratio: float = 0.3,
            mode: str = "fix",
        ) -> str | None:
        """
        以目标为中心裁剪图片

        Args:
            src_pathes (list[str]): 源文件路径列表
            size (tuple[int, int]): 目标尺寸（H, W）
            ref_classes (list[str]): 参考的类别
            dst_path (str): 目标生成路径
            maintain_ratio (float): 目标保留比例，当目标裁剪后保留下来的面积比例小于该值时，舍弃目标
            mode (str): 裁剪模式，可选["fix", "dilate"]

        Returns:
            (str | None): 生成数据位置，失败时返回None
        """
        # 判断传入参数是否合法
        if dst_path in src_pathes:
            return None

        dst = Path(dst_path) # 目标路径

        # 创建存储路径
        shutil.rmtree(dst, ignore_errors=True)
        dst.mkdir(parents=True, exist_ok=True)

        # 获取标签文件
        label_files:list[PosixPath] = []
        for src_path in src_pathes:
            src = Path(src_path)
            label_files.extend(list(src.glob(r"*.json")))

        # 逐个处理图片
        index = 0
        for idx, label_file in enumerate(label_files):
            # 读取标签内容和每个类别的形状
            label_content, shapes = self._read_labelme_file(label_file)

            # 判断图片中是否有需要裁剪的类别
            skip = True
            for shape in shapes:
                if shape['label'] in ref_classes:
                    skip = False
            if skip:
                continue

            # 读取图片
            src_img_path = label_file.parent # 图片存放起始路径
            src_img_file_rel = Path(label_content["imagePath"]) # 图片存放相对路径
            src_img_file = Path(src_img_path, src_img_file_rel) # 图片存放路径
            image = cv2.imread(src_img_file.as_posix(), cv2.IMREAD_GRAYSCALE)

            # 图片尺寸小于等于裁剪尺寸时不裁剪
            img_sz = image.shape
            if mode == 'fix':
                if img_sz[0] <= size[0] or img_sz[1] <= size[1]:
                    continue

            # 逐个处理目标
            for shape in shapes:
                # 跳过不需要处理的项
                if shape['label'] not in ref_classes:
                    continue

                # 计算中心点坐标和目标高宽
                cp = [0, 0] # x, y
                bbox_xmin = img_sz[1]
                bbox_ymin = img_sz[0]
                bbox_xmax = 0
                bbox_ymax = 0
                for point in shape['points']:
                    cp[0] += point[0]
                    cp[1] += point[1]
                    bbox_xmin = round(min(bbox_xmin, point[0]))
                    bbox_ymin = round(min(bbox_ymin, point[1]))
                    bbox_xmax = round(max(bbox_xmax, point[0]))
                    bbox_ymax = round(max(bbox_ymax, point[1]))
                cp[0] /= len(shape['points'])
                cp[1] /= len(shape['points'])
                bbox_h = bbox_ymax - bbox_ymin + 1
                bbox_w = bbox_xmax - bbox_xmin + 1

                # 获取裁剪区域坐标
                if mode == 'fix':
                    tag = "crop_ref_fix"
                    x_min = round(max(cp[0] - size[1]/2, 0))
                    x_max = min(x_min + size[1]-1, img_sz[1]-1)
                    y_min = round(max(cp[1] - size[0]/2, 0))
                    y_max = min(y_min + size[0]-1, img_sz[0]-1)
                elif mode == 'dilate':
                    tag = "crop_ref_dilate"
                    x_min = round(max(cp[0] - size[1]/2 - bbox_w/2, 0))
                    x_max = round(min(x_min + size[1]-1 + bbox_w, img_sz[1]-1))
                    y_min = round(max(cp[1] - size[0]/2 - bbox_h/2, 0))
                    y_max = round(min(y_min + size[0]-1 + bbox_h, img_sz[0]-1))
                else:
                    print('Unsupported crop mode!')
                    exit()

                crop_area = [
                    [x_min, y_min],
                    [x_max, y_min],
                    [x_max, y_max],
                    [x_min, y_max],
                ]

                # 获取裁剪图片中的目标
                new_shapes:list[LabelmeShape] = []
                save_img = True
                for _shape in shapes:
                    # 跳过非多边形框
                    if _shape['shape_type'] != 'polygon':
                        continue
                    # 判断polygon是否合法
                    points = []
                    shape_poly = Polygon(_shape['points'].copy())
                    if not shape_poly.is_valid:
                        # polygon不合法，报错提示并推出
                        print(f"Shape is invalid, please check label file: {label_file}.")
                        print(f"Invalid shape: {_shape['points']}")
                        exit()
                    # 获取交集区域坐标
                    intersection = shape_poly.intersection(Polygon(crop_area.copy()))
                    new_points_list = []
                    # 没有交集区域，跳过当前目标
                    if intersection.is_empty:
                        continue
                    # 将交集坐标加入到结果中
                    if intersection.geom_type == "Polygon":
                        poly:Polygon = intersection
                        new_points_list.append(list(poly.exterior.coords))
                    elif intersection.geom_type == "MultiPolygon":
                        polys:MultiPolygon = intersection
                        new_points_list.extend([list(poly.exterior.coords) for poly in polys.geoms])
                    # 计算裁剪后的多边形面积，判断是否保留该多边形
                    for new_points in new_points_list:
                        ratio = Polygon(new_points).area / Polygon(_shape['points']).area
                        if ratio < maintain_ratio:
                            continue
                        # 转换坐标
                        for point in new_points:
                            new_point = list(point).copy()
                            new_point[0] -= x_min
                            new_point[1] -= y_min
                            points.append(new_point)

                        # 添加坐标点
                        new_shape = _shape.copy()
                        new_shape['points'] = points
                        new_shapes.append(new_shape)

                # 判断是否保存图像
                if save_img == False \
                    or len(new_shapes) == 0:
                    continue

                # 裁剪图像
                crop_img = image.copy()[y_min:y_max+1, x_min:x_max+1] # 裁剪图像
                crop_h = crop_img.shape[0] # 裁剪图像的高度
                crop_w = crop_img.shape[1] # 裁剪图像的宽度

                # 生成Labelme标签
                labelme_dict = LabelmeDict(
                    version = "5.1.0",
                    flags = {},
                    shapes = new_shapes,
                    imagePath = f"{label_content['imagePath']}",
                    imageData = None,
                    imageHeight = crop_h,
                    imageWidth = crop_w,
                    srcPath=label_content.get("srcPath", ""),
                )

                # 保存图片和标签文件
                final_tag = f"{tag}-{index}" # 数据标签
                self._save_label_and_image(label_file, labelme_dict, final_tag, dst_path, image=crop_img)

                index += 1 # 计数值+1

        return dst_path


    def Rotate(
            self,
            src_pathes: list[str],
            quantity: int,
            dst_path: str,
            seed: int = 20241007,
            angle_range: float = (-180, 180),
        ) -> str | None:
        """
        旋转图像

        Args:
            src_pathes (list[str]): 源文件路径列表
            quantity (int): 生成图片数量
            dst_path (str): 目标生成路径
            seed (int): 随机种子，用于确保每次旋转的结果相同
            angle_range (tuple[int]): 旋转范围（单位：度）

        Returns:
            (str | None): 生成数据位置，失败时返回None
        """
        # 判断传入参数是否合法
        if dst_path in src_pathes:
            return None

        dst = Path(dst_path) # 目标路径

        # 创建存储路径
        shutil.rmtree(dst, ignore_errors=True)
        dst.mkdir(parents=True, exist_ok=True)

        # 获取标签文件
        label_files:list[PosixPath] = []
        for src_path in src_pathes:
            src = Path(src_path)
            label_files.extend(list(src.glob(r"*.json")))

        # 设置随机种子
        random.seed(seed)

        # 逐个处理图片
        tag = "rotate"
        index = 0
        for idx, label_file in enumerate(label_files):
            # 读取标签内容和每个类别的形状
            label_content, shapes = self._read_labelme_file(label_file)

            # 读取图片
            src_img_path = label_file.parent # 图片存放起始路径
            src_img_file_rel = Path(label_content["imagePath"]) # 图片存放相对路径
            src_img_file = Path(src_img_path, src_img_file_rel) # 图片存放路径
            image = cv2.imread(src_img_file.as_posix(), cv2.IMREAD_GRAYSCALE)

            # 创建旋转矩阵
            for idx in range(quantity):
                height, width = image.shape[:2]
                center = (width/2, height/2)
                _angle = random.uniform(*angle_range)
                rotate_matrix = cv2.getRotationMatrix2D(center=center, angle=_angle, scale=1)

                # 计算新的图像边界
                cos = np.abs(rotate_matrix[0, 0])
                sin = np.abs(rotate_matrix[0, 1])
                new_w = int((height * sin) + (width * cos))
                new_h = int((height * cos) + (width * sin))

                # 调整旋转矩阵的平移部分
                rotate_matrix[0, 2] += (new_w / 2) - center[0]
                rotate_matrix[1, 2] += (new_h / 2) - center[1]

                # 旋转图片
                rotated_image = cv2.warpAffine(src=image, M=rotate_matrix, dsize=(new_w, new_h), borderMode=0)
                # # 可视化
                # cv2.imshow("", rotated_image)
                # cv2.waitKey()

                # 将旋转应用到坐标点上
                new_shapes = copy.deepcopy(shapes)
                for shape in new_shapes:
                    points = np.array(shape['points'])
                    points = points.astype(np.float32).reshape(-1, 1, 2)
                    rotated_points = cv2.transform(points, rotate_matrix).reshape(-1, 2).tolist()
                    shape['points'] = rotated_points

                # 生成Labelme标签
                labelme_dict = LabelmeDict(
                    version = "5.1.0",
                    flags = {},
                    shapes = new_shapes,
                    imagePath = f"{label_content['imagePath']}",
                    imageData = None,
                    imageHeight = new_h,
                    imageWidth = new_w,
                    srcPath=label_content.get("srcPath", ""),
                )

                # 保存图片和标签文件
                final_tag = f"{tag}-{index}-{idx}" # 数据标签
                self._save_label_and_image(label_file, labelme_dict, final_tag, dst_path, image=rotated_image)
            index += 1

        return dst_path

class ObjectDetectionYOLO(DatasetYOLO):
    def __init__(self, classes:list[str]) -> None:
        """
        Args:
            classes (list[str]): 检测目标类别
        """
        super().__init__(classes)


    def _labelme_shapes_to_yolo(
            self,
            shapes: list[LabelmeShape],
            width: int,
            height: int,
            normalize: bool,
        ) -> list[LabelYOLO]:
        """
        Labelme Shapes转为YOLO标签

        Args:
            shapes (list[LabelmeShape]): LabelmeShape列表
            width (int): 图像宽度
            height (int): 图像高度
            normalize (bool): 是否归一化

        Returns:
            (list[LabelYOLO]): 转换后的YOLO标签
        """
        yolo_labels:list[LabelYOLO] = [] # YOLO标签
        for shape in shapes:
            voc = LabelVOC(cls=self.cls_idx[shape["label"]], x_min=width-1, y_min=height-1, x_max=0, y_max=0, is_norm=False)

            # 获取图像裁剪后的坐标
            for point in shape["points"]:
                voc.x_min = min(voc.x_min, point[0])
                voc.x_max = max(voc.x_max, point[0])
                voc.y_min = min(voc.y_min, point[1])
                voc.y_max = max(voc.y_max, point[1])

            # 转换为YOLO标签
            yolo = voc.Convert_To_YOLO()

            # 归一化
            if normalize == True:
                yolo.Normalize(width, height)

            # 添加标签
            yolo_labels.append(yolo)

        return yolo_labels


    def _voc_to_labelme_shapes(
            self,
            voc_labels: list[LabelVOC],
        ) -> list[LabelmeShape]:
        """
        VOC标签转换为Labelme Shapes

        Args:
            voc_labels (list[LabelVOC]): VOC标签列表

        Returns:
            (list[LabelmeShape]): 转换后的Labelme Shapes
        """
        shapes:list[LabelmeShape] = []
        for voc in voc_labels:
            shape = LabelmeShape(
                label = self.classes[voc.cls],
                points = [[voc.x_min, voc.y_max], [voc.x_max, voc.y_min]],
                group_id = None,
                shape_type = "rectangle",
                flags = {}
            )
            shapes.append(shape)

        return shapes


    def YOLO_To_Labelme(
            self,
            src_pathes: list[str],
            dst_path: str
        ) -> str | None:
        """
        将YOLO标签转为Labelme标签

        Args:
            src_pathes (list[str]): 源文件路径列表
            dst_path (str): 目标生成路径

        Returns:
            (str | None): 生成数据位置，失败时返回None
        """
        # 源路径和目标路径不能一致
        if dst_path in src_pathes:
            return None

        dst = Path(dst_path) # 目标路径
        shutil.rmtree(dst, ignore_errors=True)
        dst.mkdir(parents=True, exist_ok=True)

        # 获取标签文件
        label_files:list[PosixPath] = []
        for src_path in src_pathes:
            src = Path(src_path)
            label_files.extend(list(src.glob(r"**/*.txt")))

        # 遍历所有标签
        for label_file in label_files:
            # 读取图片
            image_file = Path(label_file.parents[1], "images").glob(label_file.stem + ".*")
            image_file:PosixPath = list(image_file)[0]
            image = cv2.imread(image_file.as_posix(), cv2.IMREAD_GRAYSCALE)
            img_w = image.shape[1]
            img_h = image.shape[0]

            # 读所有边框信息
            bboxes:list = []
            with open(label_file, "r") as file:
                bboxes = [bbox.split(" ") for bbox in file.read().split("\n")[:-1]]

            # 将边框转换为VOC标签
            voc_labels:list[LabelVOC] = []
            for bbox in bboxes:
                # 转换边框为YOLO标签
                yolo = LabelYOLO(
                    cls = eval(bbox[0]),
                    x_center = eval(bbox[1]),
                    y_center = eval(bbox[2]),
                    width = eval(bbox[3]),
                    height = eval(bbox[4]),
                    is_norm = True
                )

                # 去归一化并转换为VOC标签
                yolo.Denormalize(img_w, img_h)
                voc = yolo.Convert_To_VOC()
                voc_labels.append(voc)

            # 将VOC标签转换为LabelmeShape
            shapes:list[LabelmeShape] = []
            for voc in voc_labels:
                shape = LabelmeShape(
                    label = self.classes[voc.cls],
                    points = [[voc.x_min, voc.y_max], [voc.x_max, voc.y_min]],
                    group_id = None,
                    shape_type = "rectangle",
                    flags = {}
                )
                shapes.append(shape)

            # 生成Labelme标签
            labelme_dict = LabelmeDict(
                version = "5.1.0",
                flags = {},
                shapes = shapes,
                imagePath = image_file.name,
                imageData = None,
                imageHeight = img_h,
                imageWidth = img_w,
            )

            # 创建标签和图片文件
            with open(Path(dst, label_file.stem + ".json"), "w") as file:
                file.write(json.dumps(labelme_dict, indent=4))
            shutil.copy(image_file, dst)

        return dst_path


    def Labelme_To_YOLO(
            self,
            src_pathes: list[str],
            dst_path: str
        ) -> str | None:
        """
        将Labelme标签转为YOLO标签

        Args:
            src_pathes (list[str]): 源文件路径列表
            dst_path (str): 目标生成路径

        Returns:
            (str | None): 生成数据位置，失败时返回None
        """
        # 源路径和目标路径不能一致
        if dst_path in src_pathes:
            return None

        dst = Path(dst_path) # 目标路径
        shutil.rmtree(dst, ignore_errors=True)
        images_path = Path(dst, "images") # 图像存储路径
        labels_path = Path(dst, "labels") # 标签存储路径
        traces_path = Path(dst, "traces") # 追踪路径

        # 创建存储路径
        images_path.mkdir(parents=True, exist_ok=True)
        labels_path.mkdir(parents=True, exist_ok=True)
        traces_path.mkdir(parents=True, exist_ok=True)

        # 获取标签文件
        label_files:list[PosixPath] = []
        for src_path in src_pathes:
            src = Path(src_path)
            label_files.extend(list(src.glob(r"*.json")))

        # 逐个处理图片
        index = 0 # 计数值
        for label_file in label_files:
            # 读取标签内容
            label_content:LabelmeDict = {}
            with open(label_file, "r") as file:
                label_content = json.loads(file.read())

            # 获取需要的类别
            shapes:list[LabelmeShape] = [x for x in label_content["shapes"] if x["label"] in self.classes]
            if (len(shapes) == 0):
                continue # 图片中没有需要的类别时，跳过该图片

            # 将shapes转换为YOLO标签
            yolo_labels:list[LabelYOLO] = self._labelme_shapes_to_yolo(shapes, label_content["imageWidth"], label_content["imageHeight"], True)

            # 获取图片路径
            src_img_path = label_file.parent # 图片存放起始路径
            src_img_file_rel = Path(label_content["imagePath"]) # 图片存放相对路径
            src_img_file = Path(src_img_path, src_img_file_rel)
            # 保存图片
            file_name = f"{index}"
            # file_name = f"{index}-{src_img_file.stem}"
            shutil.copy(src_img_file, Path(images_path, f"{file_name}{src_img_file.suffix}"))
            # 保存标签
            with open(Path(labels_path, f"{file_name}.txt"), "w") as file:
                file.write("\n".join([x.__str__() for x in yolo_labels]))
            # 保存追踪文件
            with open(Path(traces_path, f"{file_name}.txt"), "w") as file:
                file.write(label_content["srcPath"])
            index += 1 # 更新计数值

        return dst_path


    def Resize_Image(
            self,
            src_pathes: list[str],
            size: tuple[int, int],
            dst_path: str,
            keep_ratio: bool = True,
            mode: str = "integer_factor",
            interpolation_method: str = "nearest",
            min_obj_w: float = 1.0,
        ) -> str | None:
        """
        缩放图像

        Args:
            src_pathes (list[str]): 源文件路径列表
            size: (tuple[int, int]): 目标缩放尺寸（H, W）
            dst_path (str): 目标生成路径
            keep_ratio (bool): 保持高度和宽度方向上的缩放系数一致
            mode (str): 缩放模式，可选["integer_factor", "float_factor"]
            interpolation_method (str): 插值方法，可选["nearest", "linear"]
            min_obj_w (float): 最小目标宽度

        Returns:
            (str | None): 生成数据位置，失败时返回None
        """
        # 判断传入参数是否正确
        if dst_path in src_pathes:
            return None

        dst = Path(dst_path) # 目标路径

        # 创建存储路径
        shutil.rmtree(dst, ignore_errors=True)
        dst.mkdir(parents=True, exist_ok=True)

        # 获取标签文件
        label_files:list[PosixPath] = []
        for src_path in src_pathes:
            src = Path(src_path)
            label_files.extend(list(src.glob(r"*.json")))

        # 逐个处理标签文件
        index = 0
        for label_file in label_files:
            # 读取标签内容和各个类别的形状
            label_content, shapes = self._read_labelme_file(label_file)
            if (len(shapes) == 0):
                continue # 图片中没有需要的类别时，跳过该图片

            # 读取图片
            src_img_path = label_file.parent # 图片存放起始路径
            src_img_file_rel = Path(label_content["imagePath"]) # 图片存放相对路径
            src_img_file = Path(src_img_path, src_img_file_rel)
            image = cv2.imread(src_img_file.as_posix(), cv2.IMREAD_GRAYSCALE)
            img_h = image.shape[0]
            img_w = image.shape[1]

            # 计算缩放系数
            fy:float = 0
            fx:float = 0
            if mode == "integer_factor":
                tag = "INT_RESZ"
                fy = math.ceil(img_h / size[0])
                fx = math.ceil(img_w / size[1])
            elif mode == "float_factor":
                tag = "FLT_RESZ"
                fy = img_h / size[0]
                fx = img_w / size[1]
            else:
                print("Unsupport process mode.")
                return None

            # 判断是否让高度和宽度方向的缩放系数保持一致
            if keep_ratio == True:
                _f = max(fy, fx)
                fy = _f
                fx = _f

            # 计算缩放后图片的高度和宽度
            new_img_h = int(img_h / fy)
            new_img_w = int(img_w / fx)

            if interpolation_method == "nearest":
                interpolation = cv2.INTER_NEAREST
            elif interpolation_method == "linear":
                interpolation = cv2.INTER_LINEAR
            else:
                print("Unsupport interpolation method.")
                exit()

            # 缩放图片
            resize_img = cv2.resize(image, (new_img_w, new_img_h), interpolation=interpolation)
            output_img = np.zeros(size, dtype=np.uint8)
            output_img[:resize_img.shape[0],:resize_img.shape[1]] = resize_img[:,:]
            out_h = output_img.shape[0]
            out_w = output_img.shape[1]

            # 转换VOC标签
            voc_labels:list[LabelVOC] = []
            for shape in shapes:
                voc = LabelVOC(cls=self.cls_idx[shape["label"]], x_min=label_content["imageWidth"]-1, y_min=label_content["imageHeight"]-1, x_max=0, y_max=0, is_norm=False)

                # 获取图像缩放后的坐标
                for point in shape["points"]:
                    voc.x_min = min(voc.x_min, point[0])
                    voc.x_max = max(voc.x_max, point[0])
                    voc.y_min = min(voc.y_min, point[1])
                    voc.y_max = max(voc.y_max, point[1])
                voc.x_min = voc.x_min / fx
                voc.x_max = voc.x_max / fx
                voc.y_min = voc.y_min / fy
                voc.y_max = voc.y_max / fy

                # 添加到标签列表
                if (voc.x_max - voc.x_min) <= min_obj_w:
                    continue # 舍弃过小的标签
                voc_labels.append(voc)

            # 跳过空标签
            if len(voc_labels) <= 0:
                continue

            # 将VOC标签转换为LabelmeShape
            new_shapes:list[LabelmeShape] = self._voc_to_labelme_shapes(voc_labels)

            # 生成Labelme标签
            labelme_dict = LabelmeDict(
                version = "5.1.0",
                flags = {},
                shapes = new_shapes,
                imagePath = f"{label_content['imagePath']}",
                imageData = None,
                imageHeight = out_h,
                imageWidth = out_w,
                srcPath=label_content.get("srcPath", ""),
            )

            # 保存图片和标签文件
            final_tag = f"{tag}-{index}" # 数据标签
            self._save_label_and_image(label_file, labelme_dict, final_tag, dst_path, image=output_img)

            index += 1 # 计数值+1

        return dst_path

    def Horizontal_Joint(
            self,
            src_pathes: list[str],
            ref_classes: list[str],
            quantity: int,
            dst_path: str,
            gap: int = 5,
            min_obj_w: float = 1.0,
            seed: int = 20240907,
        ):
        """
        横向拼接图像

        Args:
            src_pathes (list[str]): 源文件路径列表
            ref_classes (list[str]): 参考类别
            quantity (int): 生成图片数量
            dst_path (str): 目标生成路径
            gap (int): 两张拼接图片中间预留的间隙
            min_obj_w (float): 最小目标宽度
            seed (int): 随机种子，用于确保每次拼接的结果相同

        Returns:
            (str | None): 生成数据位置，失败时返回None
        """
        # 判断传入参数是否正确
        if dst_path in src_pathes \
            or gap < 0:
            print("Invalid input parameter")
            return None

        dst = Path(dst_path) # 目标路径

        # 创建存储路径
        shutil.rmtree(dst, ignore_errors=True)
        dst.mkdir(parents=True, exist_ok=True)

        # 获取标签文件
        label_files:list[PosixPath] = []
        for src_path in src_pathes:
            src = Path(src_path)
            label_files.extend(list(src.glob(r"*.json")))

        # 逐个处理图片
        size_set:set[tuple[int, int]] = set() # 图片尺寸集合
        JointType = TypedDict('JointType', {'x_val': float, 'label_file': PosixPath, 'x_mid_limit': float}) # 用于存储连接点的数据类型，分别为边界线x轴坐标、标签路径，拼接界限限制
        left_joints:list[JointType] = [] # 左连接点列表
        right_joints:list[tuple[JointType]] = [] # 右连接点列表
        for label_file in label_files:
            # 读取标签内容和各个类别的形状
            label_content, shapes = self._read_labelme_file(label_file)
            if (len(shapes) == 0):
                continue # 图片中没有需要的类别时，跳过该图片
            size_set.add((label_content["imageHeight"], label_content["imageWidth"]))

            # 获取参考类别
            ref_shapes:list[LabelmeShape] = [x for x in shapes if x["label"] in ref_classes]
            final_x_min = 0
            final_x_max = label_content["imageWidth"]-1
            for ref_shape in ref_shapes:
                # 找到最左和最右的点
                x_min:float = label_content["imageWidth"]-1.0
                x_max:float = 0.0
                for point in ref_shape["points"]:
                    x_min = min(x_min, point[0])
                    x_max = max(x_max, point[0])
                # 找到最左边的右连接点和最右边的左连接点
                final_x_min = max(final_x_min, x_min)
                final_x_max = min(final_x_max, x_max)
            # 找到左右连接点的限制
            final_x_min_limit = 0
            final_x_max_limit = label_content["imageWidth"]-1
            for ref_shape in ref_shapes:
                # 找到最左和最右的点
                x_min:float = label_content["imageWidth"]-1.0
                x_max:float = 0.0
                for point in ref_shape["points"]:
                    x_min = min(x_min, point[0])
                    x_max = max(x_max, point[0])
                if x_max < final_x_min:
                    final_x_min_limit = max(final_x_min_limit, x_max)
                if x_min > final_x_max:
                    final_x_max_limit = min(final_x_max_limit, x_min)
            # 添加左右连接点
            left_joints.append(JointType(x_val=final_x_min, label_file=label_file, x_mid_limit=final_x_min_limit))
            right_joints.append(JointType(x_val=final_x_max, label_file=label_file, x_mid_limit=final_x_max_limit))
            # break

        # 要求拼接的图片尺寸相同
        if len(size_set) > 1:
            print("Joint function require the size of all images must be the same.")
            return None

        # 找到可以拼接的图片
        index = 0
        for idx, right_joint in enumerate(right_joints):
            # 打乱左连接点
            random.seed(seed+idx)
            random.shuffle(left_joints)
            count_gen = 0
            for left_joint in left_joints:
                if count_gen >= quantity:
                    break

                # 避免拼接回原图
                if right_joint["label_file"] == left_joint["label_file"]:
                    continue

                # 判断左右连接点之间是否留有足够的间隙
                if right_joint["x_val"] + gap >= left_joint["x_val"]:
                    continue

                # 计算拼接界限
                x_mid = round((right_joint["x_val"] + left_joint["x_val"]) / 2)
                # 判断拼接界限是否在限制范围内
                if x_mid > right_joint["x_mid_limit"] \
                    or x_mid < left_joint["x_mid_limit"]:
                        # 重新计算拼接界限
                        x_mid = round((right_joint["x_val"] + right_joint["x_mid_limit"]) / 2)
                        # 判断新边界是否符合条件
                        if x_mid < left_joint["x_mid_limit"]:
                            continue # 图片无法拼接，跳过

                # 判断中间界限与左右连接点之间是否留有足够间隙
                if x_mid - right_joint["x_val"] < gap \
                    or left_joint["x_val"] - x_mid < gap:
                        continue

                # 读取标签内容和各个类别的形状
                left_label_content, left_shapes = self._read_labelme_file(left_joint["label_file"])
                right_label_content, right_shapes = self._read_labelme_file(right_joint["label_file"])

                # 读取图片
                src_img_file = Path(left_joint["label_file"].parent, Path(left_label_content["imagePath"]))
                left_image = cv2.imread(src_img_file.as_posix(), cv2.IMREAD_GRAYSCALE)
                src_img_file = Path(right_joint["label_file"].parent, Path(right_label_content["imagePath"]))
                right_image = cv2.imread(src_img_file.as_posix(), cv2.IMREAD_GRAYSCALE)

                # 创建过渡带
                transition_width = gap * 2
                _h, _w = left_image.shape[:2]
                trans_start = x_mid - gap
                transition_mask = np.zeros((_h, _w), dtype=np.float32) # 掩码图像
                transition_mask[:, :trans_start+1] = 1.0
                # 计算过渡权重
                for i in range(transition_width):
                    _w = i / (transition_width - 1)
                    _start = trans_start + i
                    transition_mask[:, _start:_start+1] = 1.0 - _w
                reverse_transition_mask = 1 - transition_mask # 反转掩码图像

                # 拼接图像
                transitioned_left = cv2.multiply(right_image.astype(np.float32) / 255.0, transition_mask)
                transitioned_right = cv2.multiply(left_image.astype(np.float32) / 255.0, reverse_transition_mask)
                # joint_image = cv2.hconcat([right_image.copy()[:, 0:x_mid], left_image.copy()[:, x_mid:]])
                joint_image = (cv2.add(transitioned_left, transitioned_right) * 255.0).astype(np.uint8)
                img_h = joint_image.shape[0] # 裁剪图像的高度
                img_w = joint_image.shape[1] # 裁剪图像的宽度

                # 转换VOC标签
                voc_labels:list[LabelVOC] = []
                # 添加右接口的形状标签
                for shape in right_shapes:
                    voc = LabelVOC(cls=self.cls_idx[shape["label"]], x_min=right_label_content["imageWidth"]-1, y_min=right_label_content["imageHeight"]-1, x_max=0, y_max=0, is_norm=False)

                    # 获取图像裁剪后的坐标
                    for point in shape["points"]:
                        voc.x_min = min(voc.x_min, min(x_mid-1, point[0]))
                        voc.x_max = max(voc.x_max, min(x_mid-1, point[0]))
                        voc.y_min = min(voc.y_min, point[1])
                        voc.y_max = max(voc.y_max, point[1])

                    # 添加到标签列表
                    if (voc.x_max - voc.x_min) <= min_obj_w:
                        continue # 舍弃过小的标签
                    voc_labels.append(voc)
                # 添加左接口的形状标签
                for shape in left_shapes:
                    voc = LabelVOC(cls=self.cls_idx[shape["label"]], x_min=left_label_content["imageWidth"]-1, y_min=left_label_content["imageHeight"]-1, x_max=0, y_max=0, is_norm=False)

                    # 获取图像裁剪后的坐标
                    for point in shape["points"]:
                        voc.x_min = min(voc.x_min, max(x_mid, point[0]))
                        voc.x_max = max(voc.x_max, max(x_mid, point[0]))
                        voc.y_min = min(voc.y_min, point[1])
                        voc.y_max = max(voc.y_max, point[1])

                    # 添加到标签列表
                    if (voc.x_max - voc.x_min) <= min_obj_w:
                        continue # 舍弃过小的标签
                    voc_labels.append(voc)

                # 跳过空标签
                if len(voc_labels) <= 0:
                    continue

                # 将VOC标签转换为LabelmeShape
                new_shapes:list[LabelmeShape] = self._voc_to_labelme_shapes(voc_labels)

                # 生成Labelme标签
                labelme_dict = LabelmeDict(
                    version = "5.1.0",
                    flags = {},
                    shapes = new_shapes,
                    imagePath = f"{right_label_content['imagePath']}",
                    imageData = None,
                    imageHeight = img_h,
                    imageWidth = img_w,
                    srcPath=label_content.get("srcPath", ""),
                )

                # 保存图片和标签文件
                final_tag = f"HOR_JNT-{index}" # 数据标签
                self._save_label_and_image(right_joint["label_file"], labelme_dict, final_tag, dst_path, image=joint_image)

                index += 1 # 计数值+1
                count_gen += 1

        return dst_path

    def Vertical_Crop(
            self,
            src_pathes: list[str],
            tar_w: int,
            retention_ratio: float,
            quantity: int,
            dst_path: str,
            mode: str = "sliding",
            min_obj_w: float = 1.0,
        ) -> str | None:
        """
        垂直方向裁剪图像

        Args:
            src_pathes (list[str]): 源文件路径列表
            tar_w (int): 目标宽度
            retention_ratio (float): 目标最小保留比例
            quantity (int): 生成图片数量
            dst_path (str): 目标生成路径
            mode (str): 裁剪方法，默认"sliding"，可选["sliding", "middle"]
            min_obj_w (float): 最小目标宽度

        Returns:
            (str | None): 生成数据位置，失败时返回None
        """
        # 源路径和目标路径不能一致
        # 生成数量不能低于0
        # 目标最小保留比例要在(0.0, 1.0]
        if dst_path in src_pathes \
            or quantity <= 0 \
            or retention_ratio <= 0.0 \
            or retention_ratio > 1.0:
            print("Invalid input parameter")
            return None

        dst = Path(dst_path) # 目标路径

        # 创建存储路径
        shutil.rmtree(dst, ignore_errors=True)
        dst.mkdir(parents=True, exist_ok=True)

        # 获取标签文件
        label_files:list[PosixPath] = []
        for src_path in src_pathes:
            src = Path(src_path)
            label_files.extend(list(src.glob(r"*.json")))

        # 逐个处理图片
        for label_file in label_files:
            # 读取标签内容和各个类别的形状
            label_content, shapes = self._read_labelme_file(label_file)
            if (len(shapes) == 0):
                continue # 图片中没有需要的类别时，跳过该图片

            # 获取可裁剪的范围
            x_min:float = label_content["imageWidth"]-1
            x_max:float = 0
            for shape in shapes:
                for point in shape["points"]:
                    x_min = min(x_min, point[0])
                    x_max = max(x_max, point[0])

            # 获取裁剪起始坐标和终止坐标
            retention_width:float = (x_max - x_min) * retention_ratio
            x_start:float = min(x_min, x_min + retention_width - tar_w)
            x_end:float = max(x_max, x_max - retention_width + tar_w) - tar_w

            # 滑窗裁剪
            if mode == "sliding":
                tag = "VRT_SLD"
                pass
            # 中心裁剪
            elif mode == "middle":
                tag = "VRT_MID"
                x_start = (label_content["imageWidth"] - tar_w) / 2
                x_end = 0
                quantity = 1
            else:
                print("Unsupport process mode.")
                return None

            # 读取图片
            src_img_path = label_file.parent # 图片存放起始路径
            src_img_file_rel = Path(label_content["imagePath"]) # 图片存放相对路径
            src_img_file = Path(src_img_path, src_img_file_rel)
            image = cv2.imread(src_img_file.as_posix(), cv2.IMREAD_GRAYSCALE)

            # 裁剪图像
            index = 0
            for i in range(quantity):
                ratio = i / (quantity - 1 if quantity != 1 else 1)
                start = round((1.0 - ratio) * x_start + ratio * x_end) # 图像起始x轴坐标
                end = start + (tar_w - 1) # 图像终止x轴坐标

                # 裁剪图像
                crop_img = image.copy()[:, start:start+tar_w] # 裁剪图像
                crop_h = crop_img.shape[0] # 裁剪图像的高度
                crop_w = crop_img.shape[1] # 裁剪图像的宽度

                # 转换VOC标签
                voc_labels:list[LabelVOC] = []
                for shape in shapes:
                    voc = LabelVOC(cls=self.cls_idx[shape["label"]], x_min=label_content["imageWidth"]-1, y_min=label_content["imageHeight"]-1, x_max=0, y_max=0, is_norm=False)

                    # 获取图像裁剪后的坐标
                    for point in shape["points"]:
                        voc.x_min = min(voc.x_min, max(start, point[0]))
                        voc.x_max = max(voc.x_max, min(end, point[0]))
                        voc.y_min = min(voc.y_min, point[1])
                        voc.y_max = max(voc.y_max, point[1])
                    voc.x_min = voc.x_min - start
                    voc.x_max = voc.x_max - start

                    # 添加到标签列表
                    if (voc.x_max - voc.x_min) <= min_obj_w:
                        continue # 舍弃过小的标签
                    voc_labels.append(voc)

                #     # 可视化
                #     cv2.rectangle(crop_img, (round(voc.x_min), round(voc.y_min)), (round(voc.x_max), round(voc.y_max)), color=((voc.cls + 1) * 30, 0, 0), thickness=2)
                # cv2.imshow("m", crop_img)
                # cv2.waitKey()

                # 跳过空标签
                if len(voc_labels) <= 0:
                    continue

                # 将VOC标签转换为LabelmeShape
                new_shapes:list[LabelmeShape] = self._voc_to_labelme_shapes(voc_labels)

                # 生成Labelme标签
                labelme_dict = LabelmeDict(
                    version = "5.1.0",
                    flags = {},
                    shapes = new_shapes,
                    imagePath = f"{label_content['imagePath']}",
                    imageData = None,
                    imageHeight = crop_h,
                    imageWidth = crop_w,
                    srcPath=label_content.get("srcPath", ""),
                )

                # 保存图片和标签文件
                final_tag = f"{tag}-{index}" # 数据标签
                self._save_label_and_image(label_file, labelme_dict, final_tag, dst_path, image=crop_img)

                index += 1 # 计数值+1
            #     break
            # break

        return dst_path

    def Horizontal_Crop_Ref(
            self,
            src_pathes: list[str],
            ref_cls: str,
            start_x: int,
            width: int,
            dst_path: str,
            maintain_classes: dict["cls":dict["ratio":float,"relevance":list[str]]] = {},
            mode: str = "below_ref",
            min_obj_h: float = 1.0,
        ) -> str | None:
        """
        水平方向裁剪图像（保证类别完整）

        Args:
            src_pathes (list[str]): 源文件路径列表
            ref_cls (str): 参考类别
            start_x (int): 裁剪起始x轴坐标
            width (int): 截取区域宽度
            dst_path (str): 目标生成路径
            maintain_classes (dict["cls":dict["ratio":float,"relevance":list[str]]]): 保证完整的类别，索引：类别，值：保持比例和相关联的标签
            mode (str): 裁剪方法，可选["below_ref"]
            min_obj_h (float): 最小目标高度

        Returns:
            (str | None): 生成数据位置，失败时返回None
        """
        if dst_path in src_pathes:
            return None

        dst = Path(dst_path) # 目标路径

        # 创建存储路径
        shutil.rmtree(dst, ignore_errors=True)
        dst.mkdir(parents=True, exist_ok=True)

        # 获取标签文件
        label_files:list[PosixPath] = []
        for src_path in src_pathes:
            src = Path(src_path)
            label_files.extend(list(src.glob(r"*.json")))

        # 逐个处理图片
        index = 0
        for label_file in label_files:
            # 读取标签内容和每个类别的形状
            label_content, shapes = self._read_labelme_file(label_file)
            if (len(shapes) == 0):
                continue # 图片中没有需要的类别时，跳过该图片

            # 遍历参考类别
            ref_shapes:list[LabelmeShape] = [x for x in shapes if x["label"] == ref_cls]
            for ref_shape in ref_shapes:
                # 初始化变量
                start:float = ref_shape["points"][0][1]
                end:float = 0

                # bottom_base裁剪
                if mode == "below_ref":
                    # 获取裁剪起始坐标和终止坐标
                    tag = "HOR_REF_BR"
                    for point in ref_shape["points"]:
                        start = round(max(start, point[1]))
                    end = label_content["imageHeight"]-1
                else:
                    print("Unsupport process mode.")
                    return None

                # 读取图片
                src_img_path = label_file.parent # 图片存放起始路径
                src_img_file_rel = Path(label_content["imagePath"]) # 图片存放相对路径
                src_img_file = Path(src_img_path, src_img_file_rel) # 图片存放路径
                image = cv2.imread(src_img_file.as_posix(), cv2.IMREAD_GRAYSCALE)

                # 裁剪图片
                crop_img = image.copy()[start:end+1, start_x:start_x+width] # 裁剪图像
                crop_h = crop_img.shape[0] # 裁剪图像的高度
                crop_w = crop_img.shape[1] # 裁剪图像的宽度

                # 转换VOC标签
                delete_classes:list[LabelVOC] = [] # 需要删除的标签
                voc_labels:list[LabelVOC] = []
                for shape in shapes:
                    voc = LabelVOC(cls=self.cls_idx[shape["label"]], x_min=label_content["imageWidth"]-1, y_min=label_content["imageHeight"]-1, x_max=0, y_max=0, is_norm=False)
                    ori_x_min = label_content["imageWidth"]-1
                    ori_y_min = label_content["imageHeight"]-1
                    ori_x_max = 0
                    ori_y_max = 0

                    # 获取图像裁剪后的坐标
                    for point in shape["points"]:
                        # 获取裁剪后的最小包围框
                        voc.x_min = min(voc.x_min, max(start_x, point[0]))
                        voc.x_max = max(voc.x_max, min(start_x+width-1, point[0]))
                        voc.y_min = min(voc.y_min, max(start, point[1]))
                        voc.y_max = max(voc.y_max, min(end, point[1]))
                        # 获取裁剪前的最小包围框
                        ori_x_min = min(ori_x_min, point[0])
                        ori_x_max = max(ori_x_max, point[0])
                        ori_y_min = min(ori_y_min, point[1])
                        ori_y_max = max(ori_y_max, point[1])
                    voc.x_min = voc.x_min - start_x
                    voc.x_max = voc.x_max - start_x
                    voc.y_min = voc.y_min - start
                    voc.y_max = voc.y_max - start

                    # 添加到标签列表
                    if (voc.y_max - voc.y_min) <= min_obj_h:
                        continue # 舍弃过小的标签
                    voc_labels.append(voc)

                    # 判断是否是需要保留完整标签
                    if shape["label"] in list(maintain_classes.keys()):
                        maintain_cls = maintain_classes[shape["label"]]
                        # 计算原始标签高度和宽度
                        _ori_h = ori_y_max - ori_y_min
                        _ori_w = ori_x_max - ori_x_min
                        # 计算新标签高度和宽度
                        _h = voc.y_max - voc.y_min
                        _w = voc.x_max - voc.x_min
                        # 判断新标签是否被裁剪
                        if _h < _ori_h * maintain_cls["ratio"] \
                            or _w < _ori_w * maintain_cls["ratio"]:
                            # 新标签被裁剪，将需要删除的标签添加到列表中
                            delete_classes.append(shape["label"])
                            for _cls in maintain_cls["relevance"]:
                                delete_classes.append(_cls)

                # 删除不完整的标签
                new_voc_labels:list[LabelVOC] = []
                for voc_label in voc_labels:
                    if self.classes[voc_label.cls] not in delete_classes:
                        new_voc_labels.append(voc_label)
                voc_labels = new_voc_labels

                # 跳过空标签
                if len(voc_labels) <= 0:
                    continue

                # 将VOC标签转换为LabelmeShape
                new_shapes:list[LabelmeShape] = self._voc_to_labelme_shapes(voc_labels)

                # 生成Labelme标签
                labelme_dict = LabelmeDict(
                    version = "5.1.0",
                    flags = {},
                    shapes = new_shapes,
                    imagePath = f"{label_content['imagePath']}",
                    imageData = None,
                    imageHeight = crop_h,
                    imageWidth = crop_w,
                    srcPath=label_content.get("srcPath", ""),
                )

                # 保存图片和标签文件
                final_tag = f"{tag}-{index}"
                self._save_label_and_image(label_file, labelme_dict, final_tag, dst_path, image=crop_img)

                index += 1 # 计数值+1
            #     break
            # break

        return dst_path


    def Horizontal_Crop(
            self,
            src_pathes: list[str],
            retention_ratio: float,
            quantity: int,
            dst_path: str,
            mode: str = "bottom_base",
            min_obj_h: float = 1.0,
        ) -> str | None:
        """
        水平方向裁剪图像

        Args:
            src_pathes (list[str]): 源文件路径列表
            retention_ratio (float): 目标最小保留比例
            quantity (int): 生成图片数量
            dst_path (str): 目标生成路径
            mode (str): 裁剪方法，可选["bottom_base"]
            min_obj_h (float): 最小目标高度

        Returns:
            (str | None): 生成数据位置，失败时返回None
        """
        # 源路径和目标路径不能一致
        # 生成数量不能低于0
        # 目标最小保留比例要在(0.0, 1.0]
        if dst_path in src_pathes \
            or quantity <= 0 \
            or retention_ratio <= 0.0 \
            or retention_ratio > 1.0:
            print("Invalid input parameter")
            return None

        dst = Path(dst_path) # 目标路径

        # 创建存储路径
        shutil.rmtree(dst, ignore_errors=True)
        dst.mkdir(parents=True, exist_ok=True)

        # 获取标签文件
        label_files:list[PosixPath] = []
        for src_path in src_pathes:
            src = Path(src_path)
            label_files.extend(list(src.glob(r"*.json")))

        # 逐个处理图片
        for label_file in label_files:
            # 读取标签内容和每个类别的形状
            label_content, shapes = self._read_labelme_file(label_file)
            if (len(shapes) == 0):
                continue # 图片中没有需要的类别时，跳过该图片

            # 获取可裁剪的范围
            y_min:float = label_content["imageWidth"]-1
            y_max:float = 0
            for shape in shapes:
                for point in shape["points"]:
                    y_min = min(y_min, point[1])
                    y_max = max(y_max, point[1])

            # 初始化变量
            retention_height:float = (y_max - y_min) * retention_ratio
            y_start:float = label_content["imageHeight"]-1
            y_end:float = 0

            # bottom_base裁剪
            if mode == "bottom_base":
                # 获取裁剪起始坐标和终止坐标
                tag = "HOR_BB"
                y_start:float = y_min
                y_end:float = y_max - retention_height
                get_start = lambda ratio: round((1 - ratio) * y_start + ratio * y_end) # 获取起始y轴坐标
                get_end = lambda ratio: label_content["imageHeight"]-1 # 获取终止y轴坐标
            else:
                print("Unsupport process mode.")
                return None

            # 读取图片
            src_img_path = label_file.parent # 图片存放起始路径
            src_img_file_rel = Path(label_content["imagePath"]) # 图片存放相对路径
            src_img_file = Path(src_img_path, src_img_file_rel) # 图片存放路径
            image = cv2.imread(src_img_file.as_posix(), cv2.IMREAD_GRAYSCALE)

            # 裁剪图像
            index = 0
            for i in range(quantity):
                ratio = i / (quantity - 1 if quantity != 1 else 1)
                start = get_start(ratio) # 图像起始y轴坐标
                end = get_end(ratio) # 图像终止y轴坐标

                # 裁剪图像
                crop_img = image.copy()[start:end+1, :] # 裁剪图像
                crop_h = crop_img.shape[0] # 裁剪图像的高度
                crop_w = crop_img.shape[1] # 裁剪图像的宽度

                # 转换VOC标签
                voc_labels:list[LabelVOC] = []
                for shape in shapes:
                    voc = LabelVOC(cls=self.cls_idx[shape["label"]], x_min=label_content["imageWidth"]-1, y_min=label_content["imageHeight"]-1, x_max=0, y_max=0, is_norm=False)

                    # 获取图像裁剪后的坐标
                    for point in shape["points"]:
                        voc.x_min = min(voc.x_min, point[0])
                        voc.x_max = max(voc.x_max, point[0])
                        voc.y_min = min(voc.y_min, max(start, point[1]))
                        voc.y_max = max(voc.y_max, min(end, point[1]))
                    voc.y_min = voc.y_min - start
                    voc.y_max = voc.y_max - start

                    # 添加到标签列表
                    if (voc.y_max - voc.y_min) <= min_obj_h:
                        continue # 舍弃过小的标签
                    voc_labels.append(voc)

                # 跳过空标签
                if len(voc_labels) <= 0:
                    continue

                # 将VOC标签转换为LabelmeShape
                new_shapes:list[LabelmeShape] = self._voc_to_labelme_shapes(voc_labels)

                # 生成Labelme标签
                labelme_dict = LabelmeDict(
                    version = "5.1.0",
                    flags = {},
                    shapes = new_shapes,
                    imagePath = f"{label_content['imagePath']}",
                    imageData = None,
                    imageHeight = crop_h,
                    imageWidth = crop_w,
                    srcPath=label_content.get("srcPath", ""),
                )

                # 保存图片和标签文件
                final_tag = f"{tag}-{index}"
                self._save_label_and_image(label_file, labelme_dict, final_tag, dst_path, image=crop_img)

                index += 1 # 计数值+1
                # break
            # break

        return dst_path

    def Add_Quantized_Noise(
            self,
            src_pathes: list[str],
            proportion: float,
            bits: int,
            white_noise_intensity: int,
            dst_path: str,
            append_mode: bool = False,
            seed: int = 20240901,
        ) -> str | None:
        """
        添加量化噪声

        Args:
            src_pathes (list[str]): 源文件路径列表
            proportion (float): 加噪图片占比
            bits (int): 模拟RAW8图片缩减至多少bits时产生的量化噪声
            white_noise_intensity(int): 白噪声强度
            dst_path (str): 目标生成路径
            append_mode (bool): 是否采用追加模式，追加模式下会在保留原有数据集的基础上增加新数据
            seed (int): 随机种子，用于确保使用相同的图片加噪

        Returns:
            (str | None): 生成数据位置，失败时返回None
        """
        if bits > 8 or bits <= 0:
            print("Invalid input parameters.")
            return None

        # 初始化输出路径
        dst = Path(dst_path)

        # 非追加模式下，移除原有路径
        if append_mode == False:
            # 非追加模式不允许目标路径与源路径相同
            if dst_path in src_pathes:
                print("Invalid input parameters.")
                return None
            shutil.rmtree(dst, ignore_errors=True)

        # 创建输出路径
        dst.mkdir(parents=True, exist_ok=True)

        # 获取标签文件
        label_files:list[PosixPath] = []
        for src_path in src_pathes:
            src = Path(src_path)
            label_files.extend(list(src.glob(r"*.json")))

        # 打乱标签文件顺序
        random.seed(seed)
        random.shuffle(label_files)

        # 计算要添加白噪声的图片数量
        num_noisy_imgs = round(len(label_files) * proportion)

        # 逐个处理图片
        index = 0
        for label_file in label_files[:num_noisy_imgs]:
            label_content, _ = self._read_labelme_file(label_file)

            # 读取图片
            src_img_path = label_file.parent # 图片存放起始路径
            src_img_file_rel = Path(label_content["imagePath"]) # 图片存放相对路径
            src_img_file = Path(src_img_path, src_img_file_rel) # 图片存放路径
            image = cv2.imread(src_img_file.as_posix(), cv2.IMREAD_GRAYSCALE)

            # 添加量化噪声
            factor = 2 ** (8 - bits)
            quantized_image = (image // factor * factor).astype(np.uint8)

            # 添加白噪声
            noise = np.random.normal(white_noise_intensity, white_noise_intensity/2, image.shape)
            noisy_image = quantized_image.astype(float) + noise
            np.clip(noisy_image, 0, 255).astype(np.uint8)

            # 保存图片和标签
            tag = f"qn-{index}"
            self._save_label_and_image(label_file, label_content, tag, dst_path, image=noisy_image)

            # 计数值+1
            index += 1

        # 返回图片生成路径
        return dst_path


class SegmentYOLO(DatasetYOLO):
    def __init__(self, classes:list[str]) -> None:
        """
        Args:
            classes (list[str]): 检测目标类别
        """
        super().__init__(classes)


    def _labelme_shapes_to_yolo_seg(
            self,
            shapes: list[LabelmeShape],
            width: int,
            height: int,
            normalize: bool,
        ) -> list[LabelYOLOSeg]:
        """
        Labelme Shapes转为YOLO标签

        Args:
            shapes (list[LabelmeShape]): LabelmeShape列表
            width (int): 图像宽度
            height (int): 图像高度
            normalize (bool): 是否归一化

        Returns:
            (list[LabelYOLOSeg]): 转换后的YOLOSeg标签
        """
        yolo_seg_labels:list[LabelYOLOSeg] = [] # YOLO标签
        for shape in shapes:
            points = shape['points']

            # 转换为YOLOseg标签
            yolo_seg = LabelYOLOSeg(self.cls_idx[shape["label"]], points, is_norm=False)

            # 归一化
            if normalize == True:
                yolo_seg.Normalize(width, height)

            # 添加标签
            yolo_seg_labels.append(yolo_seg)

        return yolo_seg_labels


    def Labelme_To_YOLO(
            self,
            src_pathes: list[str],
            dst_path: str
        ) -> str | None:
        """
        垂直方向裁剪图像

        Args:
            src_pathes (list[str]): 源文件路径列表
            dst_path (str): 目标生成路径

        Returns:
            (str | None): 生成数据位置，失败时返回None
        """
        # 源路径和目标路径不能一致
        if dst_path in src_pathes:
            return None

        dst = Path(dst_path) # 目标路径
        shutil.rmtree(dst, ignore_errors=True)
        images_path = Path(dst, "images") # 图像存储路径
        labels_path = Path(dst, "labels") # 标签存储路径

        # 创建存储路径
        images_path.mkdir(parents=True, exist_ok=True)
        labels_path.mkdir(parents=True, exist_ok=True)

        # 获取标签文件
        label_files:list[PosixPath] = []
        for src_path in src_pathes:
            src = Path(src_path)
            label_files.extend(list(src.glob(r"*.json")))

        # 逐个处理图片
        index = 0 # 计数值
        for label_file in label_files:
            # 读取标签内容
            label_content:LabelmeDict = {}
            with open(label_file, "r") as file:
                label_content = json.loads(file.read())

            # 获取需要的类别
            shapes:list[LabelmeShape] = [x for x in label_content["shapes"] if x["label"] in self.classes]
            if (len(shapes) == 0):
                continue # 图片中没有需要的类别时，跳过该图片

            # 将shapes转换为YOLO标签
            yolo_seg_labels:list[LabelYOLOSeg] = self._labelme_shapes_to_yolo_seg(shapes, label_content["imageWidth"], label_content["imageHeight"], True)

            # 获取图片路径
            src_img_path = label_file.parent # 图片存放起始路径
            src_img_file_rel = Path(label_content["imagePath"]) # 图片存放相对路径
            src_img_file = Path(src_img_path, src_img_file_rel)
            # 保存图片
            file_name = f"{index}"
            # file_name = f"{index}-{src_img_file.stem}"
            shutil.copy(src_img_file, Path(images_path, f"{file_name}{src_img_file.suffix}"))
            # 保存标签
            with open(Path(labels_path, f"{file_name}.txt"), "w") as file:
                file.write("\n".join([x.__str__() for x in yolo_seg_labels]))
            index += 1 # 更新计数值

        return dst_path