# 前端代码规范 (TypeScript/React)

本文档为 AI 视觉应用前端 (`Frontend/`) 开发提供代码规范和最佳实践，旨在提高代码质量、一致性、可维护性和团队协作效率。所有前端开发人员应遵循此规范。

## 1. 语言与环境

*   **语言**: TypeScript (使用最新稳定版本)。
*   **框架**: React (使用最新稳定版本)。
*   **包管理器**: npm (或 yarn, pnpm，根据团队决定并保持一致)。
*   **构建工具**: Vite。

## 2. 代码风格与格式化

*   **基础**: 遵循广泛接受的社区标准，如 [Airbnb JavaScript Style Guide](https://github.com/airbnb/javascript) (及其 TypeScript 扩展)。
*   **格式化**: 强制使用 `Prettier` 进行代码自动格式化。Prettier 配置 (`.prettierrc`) 应纳入版本控制。
*   **Linting**: 使用 `ESLint` 进行代码质量和风格检查。ESLint 配置 (`eslint.config.js` 或 `.eslintrc.js`) 应纳入版本控制，并集成 Prettier 插件 (`eslint-config-prettier`) 避免冲突。
*   **插件**: 配置 ESLint 使用 `@typescript-eslint/eslint-plugin`, `eslint-plugin-react`, `eslint-plugin-react-hooks`, `eslint-plugin-jsx-a11y` 等相关插件。

## 3. 命名规范

*   **组件**: 使用大驼峰命名法 (`PascalCase`)，文件名与组件名保持一致 (e.g., `UserInfoCard.tsx`)。
*   **变量/函数**: 使用小驼峰命名法 (`camelCase`)。
*   **常量**: 使用全大写蛇形命名法 (`UPPER_SNAKE_CASE`)，特别是对于全局常量或 Redux Action Types。
*   **类型/接口**: 使用大驼峰命名法 (`PascalCase`)，可考虑添加 `T` 前缀或 `Type`/`Props`/`State` 后缀以区分 (e.g., `interface IUser`, `type ButtonProps`)，根据团队偏好统一。
*   **布尔值变量**: 使用 `is`, `has`, `should` 等前缀 (e.g., `isLoading`, `hasPermission`)。
*   **事件处理函数**: 使用 `handle` 前缀 + 事件名 (e.g., `handleClick`, `handleInputChange`)。

## 4. 组件开发

*   **函数式组件**: 优先使用函数式组件和 React Hooks。
*   **职责单一**: 保持组件功能单一、纯粹。避免创建过于庞大、复杂的组件。
*   **Props**: 
    *   使用 TypeScript 接口 (`interface` 或 `type`) 定义 `Props` 类型。
    *   使用对象解构获取 props。
    *   为非必需 props 提供默认值 (`defaultProps` 或函数参数默认值)。
*   **Hooks**: 
    *   遵循 Hooks 规则 (只能在顶层调用，只能在 React 函数中调用)。
    *   优先使用 React 内置 Hooks (`useState`, `useEffect`, `useContext`, `useCallback`, `useMemo`, `useRef`)。
    *   需要复用的逻辑封装为自定义 Hooks。
*   **目录结构**: 
    *   采用按功能 (`features`) 或按类型 (`components`, `hooks`, `utils`, `pages`) 组织目录结构，保持一致性。
    *   组件内部可包含其自身的样式文件、测试文件、类型定义等 (Colocation)。
*   **性能优化**: 合理使用 `React.memo`, `useCallback`, `useMemo` 避免不必要的重渲染，但避免过度优化。

## 5. TypeScript 使用

*   **充分利用类型**: 尽可能为变量、函数参数、返回值添加明确的类型。
*   **避免 `any`**: 严格限制 `any` 的使用，只在确实无法确定类型或与第三方库交互时谨慎使用，并考虑使用 `unknown` 代替。
*   **接口与类型 (`interface` vs `type`)**: 
    *   定义对象结构或需要继承/实现时，优先使用 `interface`。
    *   定义联合类型、元组、函数类型或简单类型别名时，使用 `type`。
    *   保持团队风格统一。
*   **非空断言 (`!`)**: 避免使用非空断言操作符，优先使用类型守卫 (`if (value)`), 可选链 (`?.`) 或提供默认值。

## 6. 状态管理

*   **局部状态**: 组件内部状态使用 `useState`。
*   **跨组件状态/服务端缓存**: 优先使用 `React Query` 管理服务端状态、缓存、请求同步等。
*   **全局客户端状态**: 对于少量、简单的全局状态（如主题、用户信息），可使用 `useContext` + `useReducer`。
*   **复杂全局状态**: 如果应用状态非常复杂且共享广泛，可以考虑引入 `Redux` (+ `Redux Toolkit`) 或 `Zustand` 等库，但应避免滥用，优先考虑 `React Query` 和 `Context`。

## 7. API 调用

*   **封装**: 创建统一的 API 服务层或 Hooks (e.g., `src/services/api.ts` 或 `src/hooks/useApi.ts`) 来封装 `fetch` 或 `axios` 请求。
*   **配置**: 统一处理 API Base URL、请求头 (如 `Authorization` Token)、超时等配置。
*   **错误处理**: 在封装层统一处理常见的 HTTP 错误，并提供清晰的错误信息给上层组件。
*   **配合状态管理**: 使用 `React Query` 来管理 API 请求的加载状态、错误状态和数据缓存。

## 8. 路由

*   **库**: 使用 `React Router` 进行客户端路由管理。
*   **路由配置**: 集中管理路由配置，考虑使用路由表。
*   **代码分割**: 对不同页面/模块进行代码分割 (Lazy Loading) 以优化初始加载性能。
*   **路由保护**: 实现认证路由守卫，保护需要登录才能访问的页面。

## 9. 样式 (Ant Design)

*   **遵循规范**: 尽量遵循 Ant Design 的设计语言和组件用法。
*   **自定义**: 
    *   优先使用 Ant Design 提供的 ConfigProvider 或 Design Token 进行全局或局部主题定制。
    *   对于组件级别的细微样式调整，推荐使用 **CSS Modules** (`.module.css`) 或 **Styled Components**，以避免全局样式污染。
    *   避免直接使用内联样式 (`style` prop)，除非是动态计算的样式。
    *   避免使用 `!important`。

## 10. 测试

*   **框架**: 使用 `Jest` 作为测试运行器和断言库。
*   **组件测试**: 使用 `@testing-library/react` 进行组件测试，侧重于用户交互和行为，而非内部实现细节。
*   **Mocking**: 使用 Jest 的 Mock 功能模拟 API 调用、依赖项等。
*   **覆盖率**: 关注核心组件和逻辑的测试覆盖率。

## 11. 可访问性 (a11y)

*   **语义化 HTML**: 使用正确的 HTML 标签。
*   **ARIA 属性**: 在需要时为自定义组件或复杂交互添加适当的 ARIA 属性。
*   **键盘导航**: 确保所有交互元素都可以通过键盘访问和操作。
*   **颜色对比度**: 满足 WCAG 对比度要求。
*   **ESLint 插件**: 使用 `eslint-plugin-jsx-a11y` 辅助检查。

## 12. 文档与注释

*   **组件 Props**: 使用 TSDoc 注释 (`/** ... */`) 描述组件及其 Props。
*   **复杂逻辑**: 对复杂的算法、状态逻辑或 `useEffect` 添加必要的代码注释。
*   **README**: 维护 `Frontend/README.md`，说明项目设置、启动、构建和部署流程。

---

*本文档应随着项目进展和技术演进持续更新。* 