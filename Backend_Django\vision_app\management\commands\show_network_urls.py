"""
显示网络访问URL的管理命令

用于显示当前配置下的各种访问地址，方便开发和部署。
"""

from django.core.management.base import BaseCommand
from django.conf import settings
import os
from backend_project.config.network_configs import network_config


class Command(BaseCommand):
    help = '显示网络访问URL'

    def add_arguments(self, parser):
        parser.add_argument(
            '--environment',
            choices=['development', 'production', 'docker'],
            default='development',
            help='指定环境类型 (默认: development)',
        )
        parser.add_argument(
            '--all',
            action='store_true',
            help='显示所有环境的URL',
        )

    def handle(self, *args, **options):
        """处理命令执行"""
        
        if options['all']:
            self.show_all_environments()
        else:
            self.show_environment_urls(options['environment'])

    def show_all_environments(self):
        """显示所有环境的URL"""
        self.stdout.write(self.style.HTTP_INFO('=== 所有环境网络访问地址 ==='))
        
        environments = ['development', 'production', 'docker']
        
        for env in environments:
            self.stdout.write(f"\n{self.style.SUCCESS(f'{env.upper()} 环境:')}")
            self.show_environment_urls(env, show_header=False)

    def show_environment_urls(self, environment, show_header=True):
        """显示指定环境的URL"""
        
        if show_header:
            self.stdout.write(self.style.HTTP_INFO(f'=== {environment.upper()} 环境网络访问地址 ==='))
        
        try:
            # 获取各种URL
            frontend_url = network_config.get_frontend_url(environment)
            backend_url = network_config.get_backend_url(environment)
            websocket_url = network_config.get_websocket_url(environment)
            
            # 显示主要访问地址
            self.stdout.write(self.style.SUCCESS('\n主要访问地址:'))
            self.stdout.write(f"  前端应用: {frontend_url}")
            self.stdout.write(f"  后端API:  {backend_url}")
            
            # 显示API端点
            self.stdout.write(self.style.SUCCESS('\nAPI端点:'))
            self.stdout.write(f"  模型列表: {backend_url}/api/vision/models/")
            self.stdout.write(f"  健康检查: {backend_url}/api/health/")
            self.stdout.write(f"  管理后台: {backend_url}/admin/")
            
            # 显示WebSocket地址
            self.stdout.write(self.style.SUCCESS('\nWebSocket连接:'))
            self.stdout.write(f"  扫描器流: {websocket_url}")
            
            # 显示端口信息
            self.stdout.write(self.style.SUCCESS('\n端口配置:'))
            if environment == 'development':
                self.stdout.write(f"  前端开发: {network_config.frontend_ports['dev_server']}")
            else:
                self.stdout.write(f"  前端服务: {network_config.frontend_ports['production']}")
            self.stdout.write(f"  后端服务: {network_config.backend_ports['django']}")
            
            # 显示环境特定信息
            self.show_environment_specific_info(environment)
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'获取{environment}环境URL失败: {e}'))

    def show_environment_specific_info(self, environment):
        """显示环境特定信息"""
        
        if environment == 'development':
            self.stdout.write(self.style.SUCCESS('\n开发环境说明:'))
            self.stdout.write('  • 前端通过Vite代理访问后端API')
            self.stdout.write('  • 支持热重载和实时调试')
            self.stdout.write('  • CORS配置相对宽松')
            
        elif environment == 'production':
            self.stdout.write(self.style.SUCCESS('\n生产环境说明:'))
            self.stdout.write('  • 前端为静态文件部署')
            self.stdout.write('  • 需要配置反向代理')
            self.stdout.write('  • CORS配置需要精确设置')
            
        elif environment == 'docker':
            self.stdout.write(self.style.SUCCESS('\nDocker环境说明:'))
            self.stdout.write('  • 前端通过nginx代理访问后端')
            self.stdout.write('  • 容器间通过容器名通信')
            self.stdout.write('  • 支持热重载开发模式')
        
        # 显示当前环境变量
        self.stdout.write(self.style.SUCCESS('\n相关环境变量:'))
        env_vars = [
            'BACKEND_PORT', 'FRONTEND_DEV_PORT', 'FRONTEND_PROD_PORT',
            'BACKEND_HOST', 'BACKEND_DOCKER_HOST',
            'CORS_ORIGINS', 'ALLOWED_HOSTS', 'LAN_IPS'
        ]
        
        for var in env_vars:
            value = os.getenv(var, '未设置')
            if value != '未设置':
                self.stdout.write(f"  {var}: {value}")
        
        # 显示访问测试命令
        self.stdout.write(self.style.SUCCESS('\n访问测试:'))
        backend_url = network_config.get_backend_url(environment)
        self.stdout.write(f"  curl {backend_url}/api/vision/models/")
        
        if environment == 'development':
            frontend_url = network_config.get_frontend_url(environment)
            self.stdout.write(f"  浏览器访问: {frontend_url}")
        
        # 显示故障排除提示
        self.stdout.write(self.style.WARNING('\n故障排除:'))
        if environment == 'development':
            self.stdout.write('  • 确保Django服务器运行在配置的端口')
            self.stdout.write('  • 检查Vite代理配置是否正确')
        elif environment == 'docker':
            self.stdout.write('  • 确保Docker容器正常运行')
            self.stdout.write('  • 检查容器网络连接')
            self.stdout.write('  • 验证nginx配置是否正确')
        
        self.stdout.write('  • 检查防火墙设置')
        self.stdout.write('  • 验证CORS配置')
        self.stdout.write(f"  • 运行: python manage.py check_network_config --validate")
