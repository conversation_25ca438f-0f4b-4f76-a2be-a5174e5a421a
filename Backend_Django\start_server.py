#!/usr/bin/env python
"""
智能Django服务器启动脚本

自动检测可用的绑定地址并启动服务器。
"""

import os
import sys
import socket
import subprocess
import django
from pathlib import Path

# 设置Django环境
sys.path.insert(0, str(Path(__file__).parent))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend_project.settings')
django.setup()

from backend_project.config.network_configs import network_config


def test_bind_address(host, port):
    """测试绑定地址是否可用"""
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            s.bind((host, port))
            return True
    except OSError as e:
        return False, str(e)


def start_django_server():
    """启动Django服务器"""
    port = network_config.backend_ports['django']
    
    print(f"🚀 启动Django服务器 (端口: {port})")
    print("=" * 50)
    
    # 尝试不同的绑定地址
    bind_options = [
        ('127.0.0.1', '本地访问'),
        ('localhost', '本地访问(域名)'),
        ('0.0.0.0', '所有接口'),
    ]
    
    for host, description in bind_options:
        print(f"🔍 测试绑定到 {host}:{port} ({description})...", end=' ')
        
        result = test_bind_address(host, port)
        if result is True:
            print("✅ 可用")
            
            # 启动服务器
            bind_address = f"{host}:{port}"
            print(f"\n🎯 使用绑定地址: {bind_address}")
            
            # 显示访问地址
            if host in ['127.0.0.1', 'localhost']:
                access_url = f"http://localhost:{port}"
                print(f"📱 本地访问: {access_url}")
            else:
                print(f"📱 本地访问: http://localhost:{port}")
                
                # 显示局域网访问地址
                lan_ips = network_config._get_lan_ips()
                if lan_ips:
                    print(f"🌐 局域网访问:")
                    for ip in lan_ips:
                        print(f"   http://{ip}:{port}")
            
            print(f"\n🔧 API端点:")
            print(f"   模型列表: http://localhost:{port}/api/vision/models/")
            print(f"   健康检查: http://localhost:{port}/api/health/")
            print(f"   管理后台: http://localhost:{port}/admin/")
            
            print(f"\n🚀 启动服务器...")
            print("=" * 50)
            
            # 启动Django服务器
            try:
                subprocess.run([
                    sys.executable, 'manage.py', 'runserver', bind_address
                ], check=True)
            except KeyboardInterrupt:
                print(f"\n\n👋 服务器已停止")
            except subprocess.CalledProcessError as e:
                print(f"\n❌ 服务器启动失败: {e}")
                return 1
            
            return 0
            
        else:
            error_msg = result[1] if isinstance(result, tuple) else "未知错误"
            print(f"❌ 不可用 ({error_msg})")
    
    print(f"\n❌ 所有绑定地址都不可用")
    print(f"💡 建议:")
    print(f"   1. 检查端口 {port} 是否被其他程序占用")
    print(f"   2. 尝试以管理员权限运行")
    print(f"   3. 使用其他端口: python find_available_port.py")
    print(f"   4. 手动指定绑定地址: python manage.py runserver 127.0.0.1:{port}")
    
    return 1


def main():
    """主函数"""
    try:
        return start_django_server()
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == '__main__':
    sys.exit(main())
