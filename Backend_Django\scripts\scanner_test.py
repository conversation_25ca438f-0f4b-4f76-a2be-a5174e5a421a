import os
import sys
import time
import threading
import datetime

# -- 配置Django环境 --
# 这部分是必需的，因为它允许独立脚本使用Django项目的组件（如此处的SDK）
# 将项目根目录添加到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
# 设置Django设置模块
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend_project.settings')
# 初始化Django
import django
django.setup()
# -- Django环境配置结束 --

from vision_app.scanner.sdk import MdScanner
from vision_app.scanner.image_utils import nv12_to_jpeg

# --- 配置 ---
DEVICE_IP = "************"  # 在这里修改为你的设备IP地址
OUTPUT_DIR = "scanner_test_output"
IMAGE_BUFFER_SIZE = 1024 * 1280 * 2  # 足够大的缓冲区

# --- 全局变量 ---
image_received_event = threading.Event()
image_data_buffer = {}

def image_callback(width, height, format, size, out, ret):
    """图像回调函数"""
    print(f"收到图像数据: ret={ret}, size={size}, width={width}, height={height}, format={format}")
    
    if ret == 0 and size > 0:
        # 存储图像数据
        image_data_buffer['data'] = bytes(out)
        image_data_buffer['width'] = width
        image_data_buffer['height'] = height
        image_data_buffer['format'] = format
        
        # 通知主线程已收到图像
        image_received_event.set()
    else:
        print("接收到的图像无效或为空。")
        # 如果不成功，也设置事件以避免主线程无限等待
        image_received_event.set()

def save_image(image_info: dict):
    """保存图像到文件"""
    if not image_info.get('data'):
        print("没有有效的图像数据可供保存。")
        return

    # 创建输出目录
    if not os.path.exists(OUTPUT_DIR):
        os.makedirs(OUTPUT_DIR)
        print(f"创建目录: {OUTPUT_DIR}")

    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    width = image_info['width']
    height = image_info['height']
    
    # 尝试将NV12转换为JPEG
    jpeg_io = nv12_to_jpeg(image_info['data'], width, height)
    
    if jpeg_io:
        filename = f"test_image_{timestamp}_{width}x{height}.jpg"
        filepath = os.path.join(OUTPUT_DIR, filename)
        with open(filepath, 'wb') as f:
            f.write(jpeg_io.getvalue())
        print(f"成功将图像保存为JPEG: {filepath}")
    else:
        # 如果转换失败，保存为原始格式
        print("JPEG转换失败，将保存为原始NV12数据。")
        filename = f"test_image_{timestamp}_{width}x{height}.nv12"
        filepath = os.path.join(OUTPUT_DIR, filename)
        with open(filepath, 'wb') as f:
            f.write(image_info['data'])
        print(f"原始图像数据已保存: {filepath}")

def main():
    """主测试函数"""
    scanner = None
    try:
        # 1. 连接设备
        print(f"正在连接到设备 {DEVICE_IP}...")
        scanner = MdScanner(DEVICE_IP)
        print("连接成功！")

        # 2. 启动扫描仪
        start_event = threading.Event()
        start_result = -1
        def _start_callback(ret):
            nonlocal start_result
            start_result = ret
            start_event.set()
        
        scanner.MdScanner_Start(_start_callback)
        if not start_event.wait(timeout=5):
            print("错误: 启动扫描仪超时。")
            return
        
        if start_result != 0:
            print(f"错误: 启动扫描仪失败，返回码: {start_result}")
            return
        print("扫描仪启动成功。")
        
        # 等待一秒，让设备准备好
        time.sleep(1)

        # 3. 请求单张图像
        print("正在请求单张图像...")
        image_received_event.clear()  # 重置事件
        scanner.MdScanner_GetImage(IMAGE_BUFFER_SIZE, image_callback)

        # 等待图像回调函数被调用
        print("等待图像数据回调...")
        if image_received_event.wait(timeout=10):  # 等待10秒
            print("图像数据已收到。")
            # 4. 保存图像
            save_image(image_data_buffer)
        else:
            print("错误: 等待图像数据超时。")

    except Exception as e:
        print(f"测试过程中发生错误: {e}")
    finally:
        if scanner:
            # 5. 停止扫描仪并断开连接
            print("正在停止扫描仪...")
            stop_event = threading.Event()
            def _stop_callback(ret):
                print(f"扫描仪停止，返回码: {ret}")
                stop_event.set()
            
            scanner.MdScanner_Stop(_stop_callback)
            stop_event.wait(timeout=5)
            
            print("正在断开连接...")
            scanner.__exit__(None, None, None)
            print("测试完成。")

if __name__ == "__main__":
    main()