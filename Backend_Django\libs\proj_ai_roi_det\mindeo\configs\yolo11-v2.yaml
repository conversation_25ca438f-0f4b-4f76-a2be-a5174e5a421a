# Ultralytics YOLO 🚀, AGPL-3.0 license
# YOLO11 object detection model with P4-P5 outputs (P3 head removed).
# For usage examples see https://docs.ultralytics.com/tasks/detect

# Parameters
nc: 1   # number of classes
ch: 1   # input channels
scales: # model compound scaling constants: [depth, width, max_channels]
  n: [0.33, 0.25, 512]

# YOLO11-lite backbone
backbone:
  # [from, repeats, module, args]
  - [-1, 1, Conv, [32, 3, 2]]        # 减半输出通道
  - [-1, 1, Conv, [64, 3, 2]]
  - [-1, 1, C3k2, [128, False, 0.25]]   # repeats 改为 1
  - [-1, 1, Conv, [128, 3, 2]]
  - [-1, 1, C3k2, [256, False, 0.25]]   # repeats 改为 1
  - [-1, 1, Conv, [256, 3, 2]]
  - [-1, 1, C3k2, [256, True]]         # 继续减通道到256
  - [-1, 1, Conv, [512, 3, 2]]
  - [-1, 1, C3k2, [512, True]]         # repeats 改为 1
  - [-1, 1, SPPF, [512, 5]]           # SPPF 保留，但特征通道数减半
  - [-1, 1, C2PSA, [512]]             # C2PSA 注意力机制

# YOLO11-lite head
head:
  - [-1, 1, nn.Upsample, [None, 2, "nearest"]]
  - [[-1, 6], 1, Concat, [1]]
  - [-1, 1, C3k2, [256, False]]       # 减少 channel
  - [-1, 1, Conv, [256, 3, 2]]
  - [[-1, 10], 1, Concat, [1]]
  - [-1, 1, C3k2, [512, True]]
  - [[13, 16], 1, Detect, [nc]]