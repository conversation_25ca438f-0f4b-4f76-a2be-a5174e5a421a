# Git相关
.git
.gitignore
.gitattributes

# 文档
README.md
*.md
docs/

# 开发工具配置
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统文件
.DS_Store
Thumbs.db

# 日志文件
*.log
logs/

# 临时文件
tmp/
temp/
.tmp/

# 缓存文件
.cache/
__pycache__/
*.pyc
*.pyo
*.pyd
.Python

# 虚拟环境
Backend_Django/venv/
Backend_Django/env/
Backend_Django/.venv/

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.yarn-integrity

# 前端构建产物（在多阶段构建中会单独处理）
Frontend/dist/
Frontend/build/

# 后端相关
Backend_Django/db.sqlite3
Backend_Django/media/
Backend_Django/static/
Backend_Django/.env

# 模型文件（太大，需要单独处理）
Backend_Django/models/
*.pt
*.onnx
*.pdmodel
*.pdiparams

# 测试文件
test/
tests/
*_test.py
test_*.py

# 覆盖率报告
htmlcov/
.coverage
.coverage.*
coverage.xml

# Docker相关（保留Dockerfile用于构建）
# Dockerfile*
# docker-compose*.yml
# .dockerignore

# 部署相关（保留scripts用于管理）
deployment-package/
docker-images/
# scripts/

# 备份文件
*.backup
*.bak

# IDE配置
.project
.pydevproject
.settings/

# 其他
.pytest_cache/
.mypy_cache/
.tox/
