#ifndef _DATA_TRANSFER_H
#define _DATA_TRANSFER_H

//-----------------------------------------------------------------------------
//  Includes

#include <cstring>
#include <time.h>
#include <malloc.h>
#include <unistd.h>
#include <sys/socket.h>
#include <sys/types.h>
#include <arpa/inet.h>

#include <fstream>
#include <vector>
#include <iostream>
#include <sstream>
#include <memory>

#include "Log.hpp"
#include "AIEngineCommon.h"
//-----------------------------------------------------------------------------
//  Definitions


//-----------------------------------------------------------------------------
//  Declarations

/**
 * @brief    
 *           图像传输类
 *           
 * @date     2024-07-25 Created by HuangJP
 */
class ImgTransfer
{
public:
    /**
     * @brief    
     *           构造函数
     *           
     * @param    server_ip:     服务端IP
     * @param    pack_size:     单次传输包大小
     *           
     * @date     2024-07-25 Created by HuangJP
     */
    ImgTransfer(const char *server_ip, int pack_size = 40000)
    {
        is_ready = false;
        this->pack_size = pack_size; // 设置每次传输的包大小

        // 创建socket
        this->socket_fd = -1;
        if (!Create_Socket())
        {
            LOGE("Failed to create socket\n");
            return;
        }

        // 配置服务端信息
        memset(&this->server_addr, 0, sizeof(this->server_addr));
        this->server_addr.sin_family = AF_INET; // IPv4
        this->server_addr.sin_port = htons(8600); // 端口号
        inet_pton(AF_INET, server_ip, &this->server_addr.sin_addr);

        is_ready = true;
    }

    /**
     * @brief    
     *           发送图片
     *           
     * @param    img_ptr:       指向图像存储地址的指针
     * @param    rows:          图像高度
     * @param    cols:          图像宽度
     * @param    row_stride:    存储图像数组单行元素个数
     * @param    rsn:           右移位数
     *           
     * @retval   错误码
     *           
     * @date     2024-07-25 Created by HuangJP
     */
    template<typename pixel_t>
    int Send_Image(pixel_t *img_ptr, int rows, int cols, int row_stride, uint8_t rsn)
    {
        // 发送命令，准备开始接收
        char cmd[12] = "CMD";
        cmd[3] = CMD_SEND_IMAGE;
        memcpy(&cmd[4], &rows, sizeof(rows));
        memcpy(&cmd[8], &cols, sizeof(cols));

        this->Send_Data(cmd, sizeof(cmd));
        
        // 将图像转为8位灰度图像
        std::unique_ptr<uint8_t[]>img_buffer(new uint8_t[rows * cols]{});
        uint8_t *img = img_buffer.get();
        if (img == nullptr)
        {
            return AIENGINE_OUT_OF_MEMORY;
        }

        uint8_t *dst = img;
        for (int i = 0; i < rows; i++)
        {
            pixel_t *src = img_ptr + i * row_stride;
            for (int j = 0; j < cols; j++)
            {
                *dst = *src >> rsn;
                dst++;
                src++;
            }
        }
        this->Send_Data(img, rows * cols); // 发送图像

        // 发送结束标志
        this->Send_Done_Flag();

        return AIENGINE_NO_ERROR;
    }

    /**
     * @brief    
     *           发送掩码图像
     *           
     * @param    img_ptr:       指向掩码图像存储地址的指针
     * @param    rows:          图像高度
     * @param    cols:          图像宽度
     * @param    row_stride:    存储图像数组单行元素个数
     *           
     * @retval   错误码
     *           
     * @date     2024-10-22 Created by HuangJP
     */
    int Send_Mask_Image(uint8_t *img_ptr, int rows, int cols, int row_stride)
    {
        // 发送命令，准备开始接收
        char cmd[12] = "CMD";
        cmd[3] = CMD_SEND_MASK_IMAGE;
        memcpy(&cmd[4], &rows, sizeof(rows));
        memcpy(&cmd[8], &cols, sizeof(cols));

        // 发送接收命令
        this->Send_Data(cmd, sizeof(cmd));

        // 截取图像
        std::unique_ptr<uint8_t[]>img_buffer(new uint8_t[rows * cols]{});
        uint8_t *img = img_buffer.get();
        if (img == nullptr)
        {
            return AIENGINE_OUT_OF_MEMORY;
        }

        uint8_t *dst = img;
        for (int i = 0; i < rows; i++)
        {
            uint8_t *src = img_ptr + i * row_stride;
            for (int j = 0; j < cols; j++)
            {
                *dst = *src;
                dst++;
                src++;
            }
        }

        // 发送掩码图像
        this->Send_Data(img, rows * cols);

        // 发送结束标志
        this->Send_Done_Flag();

        return AIENGINE_NO_ERROR;
    }


    /**
     * @brief    
     *           发送目标检测结果
     *           
     * @param    detections:        目标检测结果结构体
     *           
     * @retval   错误码
     *           
     * @date     2024-07-25 Created by HuangJP
     */
    int Send_Detection_Results(std::vector<DetectionBBoxInfo> *detections)
    {
        // 发送命令，准备开始接收
        char cmd[12] = "CMD";
        cmd[3] = CMD_SEND_DETECTION_RESULTS;
        int num_rs = detections->size();
        int data_size = sizeof(DetectionBBoxInfo) * num_rs;
        memcpy(&cmd[4], &num_rs, sizeof(num_rs));
        memcpy(&cmd[8], &data_size, sizeof(data_size));

        this->Send_Data(cmd, sizeof(cmd));

        // 发送结果
        uint8_t *buffer = (uint8_t *)calloc(data_size, sizeof(uint8_t));
        if (buffer == nullptr)
        {
            return AIENGINE_OUT_OF_MEMORY;
        }

        int pos = 0;
        for (auto x: *detections)
        {
            memcpy(buffer + pos, &x, sizeof(DetectionBBoxInfo));
            pos += sizeof(DetectionBBoxInfo);
        }
        this->Send_Data(buffer, data_size);

        free(buffer);
        buffer = nullptr;

        // 发送结束标志
        this->Send_Done_Flag();

        return AIENGINE_NO_ERROR;
    }

    /**
     * @brief    
     *           发送多边形坐标点
     *           
     * @param    polygons:  多边形坐标点
     *           
     * @retval   错误码
     *           
     * @date     2024-10-29 Created by HuangJP
     */
    int Send_Polygons(std::vector<std::vector<Point>> polygons, std::vector<int> classes)
    {
        // 用于打包的点坐标
        struct PackagePoint
        {
            int x;
            int y;
        };

        // 定义变量
        char cmd[16] = "CMD";
        cmd[3] = CMD_SEND_POLYGONS;
        int num_rs = polygons.size();

        // 计算数据大小
        int data_size = 0;
        char start[4] = "NEW"; // 起始符
        for (auto polygon: polygons) { data_size += sizeof(start) + sizeof(int) + polygon.size() * sizeof(PackagePoint); }

        // 发送命令，准备开始接收
        memcpy(&cmd[4], &num_rs, sizeof(num_rs)); // 结果数量
        memcpy(&cmd[8], &data_size, sizeof(data_size)); // 数据大小
        memcpy(&cmd[12], start, sizeof(start)); // 起始符
        this->Send_Data(cmd, sizeof(cmd));

        // 组织数据
        std::unique_ptr<uint8_t[]>buffer(new uint8_t[data_size]{});
        int pos = 0;
        for (int i = 0; i < (int)polygons.size(); i++)
        {
            auto polygon = polygons[i];
            // 加入起始符
            memcpy(buffer.get() + pos, start, sizeof(start));
            pos += sizeof(start);

            // 加入类别
            memcpy(buffer.get() + pos, &classes[i], sizeof(int));
            pos += sizeof(int);

            // 加入坐标点
            for (auto point: polygon)
            {
                PackagePoint package{point.x, point.y}; // 打包坐标点
                memcpy(buffer.get() + pos, &package, sizeof(PackagePoint));
                pos += sizeof(PackagePoint);
            }
        }

        // 发送多边形坐标
        this->Send_Data(buffer.get(), data_size);

        // 发送结束标志
        this->Send_Done_Flag();

        return AIENGINE_NO_ERROR;
    }

    bool is_ready;
private:
    int socket_fd;
    int pack_size;
    struct sockaddr_in server_addr;

    /**
     * @brief    
     *           命令枚举
     *           
     * @date     2024-10-22 Created by HuangJP
     */
    enum _cmd{
        CMD_SEND_IMAGE = 0,                 // 发送图像
        CMD_SEND_DETECTION_RESULTS = 1,     // 发送目标检测结果
        CMD_SEND_MASK_IMAGE = 2,            // 发送掩码图像
        CMD_SEND_POLYGONS = 3,              // 发送多边形坐标点
    };

    /**
     * @brief    
     *           创建socket
     *           
     * @retval   是否创建成功
     *           
     * @date     2024-10-29 Created by HuangJP
     */
    bool Create_Socket(void)
    {
        // 删除socket
        if (this->socket_fd != -1)
        {
            int rev = close(this->socket_fd);
            if (rev != 0)
            {
                LOGE("Failed to close socket\n");
            }

            this->socket_fd = -1;
        }

        // 创建socket
        this->socket_fd = socket(AF_INET, SOCK_DGRAM, IPPROTO_UDP); // 使用UDP发送数据 | AF_INET: IPv4 | SOCK_DGRAM: 无保障传输 | IPPROTO_UDP: UDP通信
        if (this->socket_fd == -1)
        {
            LOGE("Failed to create socket\n");
            return false;
        }

        return true;
    }

    /**
     * @brief    
     *           发送数据
     *           
     * @param    buffer:        要发送数据的存储地址
     * @param    len:           发送数据长度
     *           
     * @retval   错误码
     *           
     * @date     2024-07-25 Created by HuangJP
     */
    int Send_Data(void *buffer, ssize_t len)
    {
        int num_pack = len / pack_size;
        int remain = len % pack_size;

        for (int i = 0; i < num_pack; i++)
        {
            ssize_t bytes_sent = sendto(this->socket_fd, (uint8_t *)buffer + (i * pack_size), pack_size, 0, (struct sockaddr*)&this->server_addr, sizeof(this->server_addr));
            if (bytes_sent == -1)
            {
                LOGE("Failed to send bytes\n");
                if (!Create_Socket()) LOGE("Failed to recreate socket\n");
                return AIENGINE_OPEN_FILE_ERROR;
            }
        }

        if (remain != 0)
        {
            ssize_t bytes_sent = sendto(this->socket_fd, (uint8_t *)buffer + (num_pack * pack_size), remain, 0, (struct sockaddr*)&this->server_addr, sizeof(this->server_addr));
            if (bytes_sent == -1)
            {
                LOGE("Failed to send bytes\n");
                if (!Create_Socket()) LOGE("Failed to recreate socket\n");
                return AIENGINE_OPEN_FILE_ERROR;
            }
        }

        return AIENGINE_NO_ERROR;
    }

    /**
     * @brief    
     *           发送数据传输完成标志
     *           
     * @date     2024-07-25 Created by HuangJP
     */
    inline void Send_Done_Flag(void)
    {
        // 发送数据传输完成标志
        char done[5] = "DONE";
        this->Send_Data(done, sizeof(done));
    }
};

#endif
//-----------------------------------------------------------------------------
//  End of file