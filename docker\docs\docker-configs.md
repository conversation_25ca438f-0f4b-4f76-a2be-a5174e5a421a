# Docker 配置文件说明

本文档详细介绍 Docker 相关的配置文件和其用途。

---

## 🔧 Docker Compose 配置文件

### `docker-compose.yml` - 标准生产环境
**用途**: 完整的生产环境配置
**特性**:
- 完整的生产环境配置
- 包含健康检查和重启策略
- 数据持久化挂载

**主要配置**:
```yaml
services:
  backend:
    image: web_ai_vision_app-backend:latest
    restart: unless-stopped
    volumes:
      - ./data/db:/app/db
      - ./data/models:/app/models
      - ./data/media:/app/media
      - ./data/logs:/app/logs
    ports:
      - "8000:8000"

  frontend:
    image: web_ai_vision_app-frontend:latest
    restart: unless-stopped
    ports:
      - "8080:80"
    depends_on:
      - backend
```

### `docker-compose.hotreload.yml` - 热重载开发环境
**用途**: 开发环境的热重载配置
**特性**:
- 代码热重载支持
- 挂载 `code-sync` 目录
- Django 自动重载模式

**主要配置**:
```yaml
services:
  backend:
    volumes:
      # 代码热更新挂载 - 直接挂载后端代码
      - ./code-sync/Backend_Django:/app:rw
      # 持久化数据目录 - 在代码挂载之后，避免被覆盖
      - ./data/db:/app/db
      - ./data/models:/app/models
    command: >
      sh -c "
        echo 'Starting Django with hot reload...' &&
        python manage.py collectstatic --noinput --clear &&
        python manage.py migrate &&
        python manage.py runserver 0.0.0.0:8000
      "

  frontend:
    volumes:
      # 代码热更新挂载 - 前端构建产物
      - ./code-sync/Frontend/dist:/usr/share/nginx/html:rw
      # 使用热重载专用的nginx配置
      - ./nginx-hotreload.conf:/etc/nginx/nginx.conf:ro
```

### `docker-compose.simple.yml` - 简化部署
**用途**: 最小化配置，快速启动测试
**特性**:
- 最小化配置
- 快速启动测试
- 适合演示和验证

---

## 🐳 Dockerfile 配置

### `Dockerfile.frontend` - 前端镜像构建
**功能**: 构建前端 Nginx 镜像

**构建阶段**:
```dockerfile
# 阶段1: 构建阶段
FROM node:18-alpine as builder
WORKDIR /app
COPY Frontend/package*.json ./
RUN npm ci --only=production
COPY Frontend/ .
RUN npm run build

# 阶段2: 生产阶段
FROM nginx:alpine
COPY --from=builder /app/dist /usr/share/nginx/html
COPY docker/nginx.conf /etc/nginx/nginx.conf
```

**特性**:
- 多阶段构建，减小镜像大小
- 使用 Alpine Linux，安全轻量
- 自动配置 Nginx

### `Dockerfile.backend` - 后端镜像构建
**功能**: 构建后端 Django 镜像

**主要配置**:
```dockerfile
FROM python:3.11-slim

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 设置工作目录
WORKDIR /app

# 复制并安装Python依赖
COPY Backend_Django/requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 复制后端代码
COPY Backend_Django/ /app/

# 创建启动脚本
COPY docker/scripts/start-backend.sh /app/start-backend.sh
RUN chmod +x /app/start-backend.sh

# 启动命令
CMD ["/app/start-backend.sh"]
```

**特性**:
- 基于官方 Python 镜像
- 包含健康检查
- 自动初始化脚本

---

## 🌐 Nginx 配置

### `nginx.conf` - 标准 Nginx 配置
**功能**: 生产环境的 Nginx 配置，包含 API 代理

**主要配置**:
```nginx
server {
    listen 80;
    server_name localhost;

    # 前端静态文件
    location / {
        root /usr/share/nginx/html;
        index index.html index.htm;
        try_files $uri $uri/ /index.html;
    }

    # API 代理
    location /api/ {
        proxy_pass http://backend:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 媒体文件代理
    location /media/ {
        proxy_pass http://backend:8000;
    }
}
```

### `nginx-hotreload.conf` - 热重载 Nginx 配置
**功能**: 热重载模式的 Nginx 配置

**特殊配置**:
- 优化的缓存策略
- 开发环境错误页面
- 热重载友好的配置

---

## ⚙️ 环境配置

### 🌍 环境变量配置
```yaml
# Django 后端配置
DJANGO_SETTINGS_MODULE: backend_project.settings_docker
DJANGO_ALLOWED_HOSTS: "*"
CORS_ALLOW_ALL_ORIGINS: "True"
DJANGO_DEBUG: "True"
PYTHONUNBUFFERED: "1"

# 数据库配置
DATABASE_PATH: /app/db/db.sqlite3

# 模型文件路径
MEDIA_ROOT: /app/models/custom_models
SYSTEM_MODELS_ROOT: /app/models/system_models
```

### 💾 数据持久化
项目使用绑定挂载方式持久化数据：
```yaml
volumes:
  - ./data/db:/app/db           # SQLite 数据库
  - ./data/models:/app/models   # AI 模型文件
  - ./data/media:/app/media     # 媒体文件
  - ./data/logs:/app/logs       # 应用日志
```

### 🌐 网络配置
```yaml
# 端口映射
ports:
  - "8080:80"    # 前端：主机8080 → 容器80
  - "8000:8000"  # 后端：主机8000 → 容器8000

# 内部网络
networks:
  ai-vision-network:
    driver: bridge
```

---

## 🌍 国内网络优化配置

### 🐳 Docker 镜像源 (`daemon.json`)
**功能**: 配置 Docker 国内镜像源，提升拉取速度

```json
{
  "registry-mirrors": [
    "https://docker.mirrors.ustc.edu.cn",
    "https://hub-mirror.c.163.com",
    "https://mirror.baidubce.com"
  ]
}
```

**使用方法**:
1. 复制到 Docker Desktop 配置目录
2. 重启 Docker Desktop
3. 验证配置: `docker info`

### 🐍 Python 包镜像源 (`pip.conf`)
**功能**: 配置 Python 包国内镜像源

```ini
[global]
index-url = https://pypi.tuna.tsinghua.edu.cn/simple
trusted-host = pypi.tuna.tsinghua.edu.cn
```

**效果**: 显著提升 Python 包安装速度

### 📦 Node.js 包镜像源
**配置**: 已在 Dockerfile.frontend 中预配置

```bash
# 已在构建过程中配置
npm config set registry https://registry.npmmirror.com/
```

---

## 🔄 配置文件选择指南

### 开发场景选择
- **本地开发**: `docker-compose.hotreload.yml`
- **快速验证**: `docker-compose.simple.yml`
- **生产部署**: `docker-compose.yml`

### 使用命令
```powershell
# 使用特定配置文件
docker-compose -f docker-compose.hotreload.yml up -d
docker-compose -f docker-compose.simple.yml up -d

# 使用默认配置文件
docker-compose up -d  # 使用 docker-compose.yml
```

---

## 📝 配置自定义

### 端口自定义
如果默认端口冲突，可以修改端口映射：
```yaml
ports:
  - "8081:80"    # 前端改为8081端口
  - "8001:8000"  # 后端改为8001端口
```

### 环境变量自定义
创建 `.env` 文件自定义环境变量：
```env
FRONTEND_PORT=8081
BACKEND_PORT=8001
DJANGO_DEBUG=False
```

### 数据目录自定义
修改数据挂载路径：
```yaml
volumes:
  - /custom/path/db:/app/db
  - /custom/path/models:/app/models
```

---

## 🔍 配置验证

### 检查配置语法
```powershell
# 验证 docker-compose 配置
docker-compose config

# 验证特定配置文件
docker-compose -f docker-compose.hotreload.yml config
docker-compose -f docker-compose.simple.yml config
```

### 检查网络配置
```powershell
# 查看网络
docker network ls

# 检查容器网络
docker inspect ai-vision-network
```

### 检查挂载配置
```powershell
# 查看挂载点
docker inspect ai-vision-backend | grep -A 10 "Mounts"
```
