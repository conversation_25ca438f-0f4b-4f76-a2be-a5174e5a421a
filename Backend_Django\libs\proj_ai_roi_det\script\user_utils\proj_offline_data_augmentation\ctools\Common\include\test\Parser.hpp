#ifndef _PARSER_H
#define _PARSER_H

//-----------------------------------------------------------------------------
//  Includes

#include <string>
#include <sstream>
#include <unordered_map>

#include "Log.hpp"
#include "AIEngineCommon.h"
//-----------------------------------------------------------------------------
//  Definitions


//-----------------------------------------------------------------------------
//  Declarations

class Parser {
public:
    Parser() {} // 构造函数
    ~Parser() {} // 析构函数

    /**
     * @brief    
     *           添加有值参数
     *           
     * @param    name_of_flags:  需要解析的参数
     * @param    default_value:  参数默认值
     *           
     * @retval   true:  添加成功
     * @retval   false: 添加失败
     *           
     * @date     2024-04-15 Created by HuangJP
     */
    bool Add_Argument(std::string name_of_flags, std::string default_value)
    {
        // 确保参数没有添加过
        if (_check_is_args_added(name_of_flags) == true)
        {
            LOGE("Argument has been added");
            LOGD("Add argument failure: %s", name_of_flags.c_str());
            return false;
        }

        _args_list[name_of_flags] = default_value;
        return true;
    }

    /**
     * @brief    
     *           添加布尔参数
     *           
     * @param    name_of_flags:  需要解析的参数
     *           
     * @retval   true:  添加成功
     * @retval   false: 添加失败
     *           
     * @date     2024-04-22 Created by HuangJP
     */
    bool Add_Boolean_Argument(std::string name_of_flags)
    {
        // 确保参数没有添加过
        if (_check_is_args_added(name_of_flags) == true)
        {
            LOGE("Argument has been added");
            LOGD("Add argument failure: %s", name_of_flags.c_str());
            return false;
        }

        _bool_args_list[name_of_flags] = false;
        return true;
    }

    /**
     * @brief    
     *           解析参数
     *           
     * @param    argc:      输入命令行的参数数量
     * @param    argv:      命令行参数
     *           
     * @retval   true:      解析成功
     * @retval   false:     解析失败
     *           
     * @date     2024-04-15 Created by HuangJP
     */
    bool Parse_Args(int argc, char *argv[])
    {
        for (int i = 1; i < argc; i++)
        {
            std::string parse_arg(argv[i]);

            // 判断参数是否有效
            if (_check_is_args_added(parse_arg) == false)
            {
                LOGI("Unsupported arguments: %s", parse_arg.c_str());
                continue;
            }

            // 判断是否为布尔参数
            if (_bool_args_list.find(parse_arg) != _bool_args_list.end())
            {
                _bool_args_list[parse_arg] = true;
            }
            // 判断是否为有值参数
            else if (_args_list.find(parse_arg) != _args_list.end())
            {
                // 判断参数值是否为空
                if (i + 1 >= argc)
                {
                    LOGE("Empty arguments has been detected");
                    LOGD("Empty arguments: %s", parse_arg.c_str());
                    return false;
                }

                // 获取参数值
                std::string arg_value(argv[i + 1]);
                
                // 参数值不能是参数选项
                if (_check_is_args_added(arg_value) == true)
                {
                    LOGE("Empty arguments has been detected");
                    LOGD("Empty arguments: %s", parse_arg.c_str());
                    return false;
                }

                // 记录参数值
                _args_list[parse_arg] = arg_value;
                i += 1; // 跳过参数值，解析下一个参数
            }
        }

        return true;
    }

    /**
     * @brief    
     *           获取指定参数
     *           
     * @param    name_of_flags:     指定参数
     * @param    value:             找不到指定参数时返回的值
     *           
     * @retval   指定参数值，获取失败时，返回输入参数value
     *           
     * @date     2024-04-15 Created by HuangJP
     */
    template <typename type>
    type Get_Args_Value(std::string name_of_flags, type value)
    {
        // 检查参数是否添加过
        if (_check_is_args_added(name_of_flags) == false)
        {
            LOGE("Get arguments failure: flags not found");
            LOGD("Get arguments failure: %s", name_of_flags.c_str());
            return value;
        }

        // 转换参数类型
        type args_value;
        std::stringstream ss;
        if (_args_list.find(name_of_flags) != _args_list.end())
        {
            ss << _args_list[name_of_flags]; // 参数在有值参数列表中
        }
        else if (_bool_args_list.find(name_of_flags) != _bool_args_list.end())
        {
            ss << _bool_args_list[name_of_flags]; // 参数在布尔参数列表中
        }
        ss >> args_value;
        return args_value;
    }

private:
    std::unordered_map<std::string, std::string> _args_list; // 有值参数列表
    std::unordered_map<std::string, bool> _bool_args_list; // 布尔参数列表

    /**
     * @brief    
     *           确认参数是否添加过
     *           
     * @param    name_of_flags:     指定参数
     *           
     * @retval   true:      参数已添加
     * @retval   false:     参数未添加
     *           
     * @date     2024-04-22 Created by HuangJP
     */
    inline bool _check_is_args_added(std::string name_of_flags)
    {
        // 检查有值参数列表中是否存在指定参数
        if (_args_list.find(name_of_flags) != _args_list.end())
        {
            return true;
        }

        // 检查布尔参数列表中是否存在指定参数
        if (_bool_args_list.find(name_of_flags) != _bool_args_list.end())
        {
            return true;
        }

        return false;
    }
};
#endif
//-----------------------------------------------------------------------------
//  End of file