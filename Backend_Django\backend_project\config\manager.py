"""
配置管理器

统一管理Django项目的所有配置，提供类型安全的配置访问接口。
"""

from pathlib import Path
from typing import Dict, Any, List
import os
from .ocr_configs import get_ocr_task_configs
from .storage_configs import get_storage_configs
from .environment import get_environment_configs
from .network_configs import get_network_configs


class ConfigManager:
    """
    配置管理器类
    
    统一管理项目的所有配置，包括Django核心配置、业务配置等。
    提供类型安全的配置访问接口。
    """
    
    def __init__(self, base_dir: Path):
        """
        初始化配置管理器
        
        Args:
            base_dir: Django项目的BASE_DIR路径
        """
        self.base_dir = base_dir
        self._ocr_configs = None
        self._storage_configs = None
        self._django_configs = None
        self._security_configs = None
        self._environment_configs = None
        self._network_configs = None
    
    @property
    def ocr_configs(self) -> Dict[str, Any]:
        """获取OCR任务配置"""
        if self._ocr_configs is None:
            self._ocr_configs = get_ocr_task_configs(self.base_dir)
        return self._ocr_configs
    
    @property
    def storage_configs(self) -> Dict[str, Any]:
        """获取存储配置"""
        if self._storage_configs is None:
            self._storage_configs = get_storage_configs(self.base_dir)
        return self._storage_configs
    
    @property
    def django_configs(self) -> Dict[str, Any]:
        """获取Django核心配置"""
        if self._django_configs is None:
            self._django_configs = self._get_django_configs()
        return self._django_configs
    
    @property
    def security_configs(self) -> Dict[str, Any]:
        """获取安全和会话配置"""
        if self._security_configs is None:
            self._security_configs = self._get_security_configs()
        return self._security_configs

    @property
    def environment_configs(self) -> Dict[str, Any]:
        """获取环境相关配置"""
        if self._environment_configs is None:
            self._environment_configs = get_environment_configs(self.base_dir)
        return self._environment_configs

    @property
    def network_configs(self) -> Dict[str, Any]:
        """获取网络相关配置"""
        if self._network_configs is None:
            self._network_configs = get_network_configs()
        return self._network_configs
    
    def _get_django_configs(self) -> Dict[str, Any]:
        """获取Django核心配置"""
        return {
            'ASGI_APPLICATION': "backend_project.asgi.application",
            'INSTALLED_APPS': [
                "channels",
                "django.contrib.admin",
                "django.contrib.auth",
                "django.contrib.contenttypes",
                "django.contrib.sessions",
                "django.contrib.messages",
                "django.contrib.staticfiles",
                "corsheaders",  # 添加 corsheaders
                "vision_app",
            ],
            
            'MIDDLEWARE': [
                "corsheaders.middleware.CorsMiddleware",  # 添加 corsheaders 中间件
                "django.middleware.security.SecurityMiddleware",
                "django.contrib.sessions.middleware.SessionMiddleware",
                "django.middleware.common.CommonMiddleware",
                "django.middleware.csrf.CsrfViewMiddleware",
                "django.contrib.auth.middleware.AuthenticationMiddleware",
                "django.contrib.messages.middleware.MessageMiddleware",
                "django.middleware.clickjacking.XFrameOptionsMiddleware",
            ],
            
            'ROOT_URLCONF': "backend_project.urls",
            
            'TEMPLATES': [
                {
                    "BACKEND": "django.template.backends.django.DjangoTemplates",
                    "DIRS": [],
                    "APP_DIRS": True,
                    "OPTIONS": {
                        "context_processors": [
                            "django.template.context_processors.request",
                            "django.contrib.auth.context_processors.auth",
                            "django.contrib.messages.context_processors.messages",
                        ],
                    },
                },
            ],
            
            'WSGI_APPLICATION': "backend_project.wsgi.application",

            'CHANNEL_LAYERS': {
                "default": {
                    "BACKEND": "channels.layers.InMemoryChannelLayer",
                    "CONFIG": {
                        "capacity": 300,    # 限制容量避免积压
                        "expiry": 1,        # 快速过期清理旧消息
                    },
                }
            },
            
            'DATABASES': {
                "default": {
                    "ENGINE": "django.db.backends.sqlite3",
                    "NAME": self.base_dir / "db" / "db.sqlite3",
                }
            },
            
            'AUTH_PASSWORD_VALIDATORS': [
                {
                    "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
                },
                {
                    "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
                },
                {
                    "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
                },
                {
                    "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
                },
            ],
            
            'LANGUAGE_CODE': "en-us",
            'TIME_ZONE': "UTC",
            'USE_I18N': True,
            'USE_TZ': True,
            'DEFAULT_AUTO_FIELD': "django.db.models.BigAutoField",
        }
    
    def _get_security_configs(self) -> Dict[str, Any]:
        """获取安全和会话配置（不包含网络相关配置）"""
        secret_key = os.environ.get('SECRET_KEY')
        if not secret_key and os.environ.get('DJANGO_ENV') != 'production':
            secret_key = 'django-insecure-fallback-key-for-development'
            print("WARNING: SECRET_KEY not set, using a default insecure key for development.")
        
        return {
            'SECRET_KEY': secret_key,
            
            # 会话配置
            'SESSION_COOKIE_AGE': 86400,  # 会话有效期：24小时（秒）
            'SESSION_SAVE_EVERY_REQUEST': True,  # 每次请求都保存会话
            'SESSION_EXPIRE_AT_BROWSER_CLOSE': False,  # 浏览器关闭时不过期会话
            'SESSION_COOKIE_SAMESITE': "Lax",  # 本地开发推荐Lax，跨域如需可用None+https
            'SESSION_COOKIE_SECURE': False,  # 本地开发用False，生产https用True
        }
    
    def get_all_configs(self) -> Dict[str, Any]:
        """
        获取所有配置的合并字典

        Returns:
            包含所有配置的字典
        """
        all_configs = {}

        # 按优先级合并各种配置（后面的会覆盖前面的）
        all_configs.update(self.django_configs)
        all_configs.update(self.storage_configs)
        all_configs.update(self.security_configs)
        all_configs.update(self.network_configs)  # 网络配置
        all_configs.update(self.environment_configs)  # 环境配置优先级最高

        # 添加特殊配置
        all_configs['OCR_TASK_CONFIGS'] = self.ocr_configs

        return all_configs
