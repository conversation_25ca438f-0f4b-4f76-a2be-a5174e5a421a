"""
基础服务类

提供所有服务类的基础功能和通用方法。

完整的BaseService架构：

class BaseService:
    # ==================== 日志记录模块 ====================
    def log_info()                         # 记录信息日志
    def log_warning()                      # 记录警告日志
    def log_error()                        # 记录错误日志
    def log_debug()                        # 记录调试日志
    
    # ==================== 配置管理模块 ====================
    def get_setting()                      # 获取Django设置

    # ==================== 响应构建模块 ====================
    def create_error_response()            # 创建标准错误响应
    def create_success_response()          # 创建标准成功响应

"""

import logging
from typing import Any, Dict, Optional
from django.conf import settings


class BaseService:
    """
    基础服务类
    
    所有服务类的基类，提供通用的功能和方法。
    """
    
    def __init__(self):
        """初始化基础服务"""
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def log_info(self, message: str, **kwargs) -> None:
        """记录信息日志"""
        self.logger.info(message, **kwargs)
    
    def log_warning(self, message: str, **kwargs) -> None:
        """记录警告日志"""
        self.logger.warning(message, **kwargs)
    
    def log_error(self, message: str, **kwargs) -> None:
        """记录错误日志"""
        self.logger.error(message, **kwargs)
    
    def log_debug(self, message: str, **kwargs) -> None:
        """记录调试日志"""
        self.logger.debug(message, **kwargs)
    
    def get_setting(self, setting_name: str, default: Any = None) -> Any:
        """
        获取Django设置
        
        Args:
            setting_name: 设置名称
            default: 默认值
            
        Returns:
            设置值
        """
        return getattr(settings, setting_name, default)
    
