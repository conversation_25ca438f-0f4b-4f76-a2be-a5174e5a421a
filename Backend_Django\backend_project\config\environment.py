"""
环境配置模块

根据不同的运行环境提供相应的配置参数。
"""

import os
from pathlib import Path
from typing import Dict, Any


def get_environment_configs(base_dir: Path) -> Dict[str, Any]:
    """
    根据环境变量获取环境相关配置
    
    Args:
        base_dir: Django项目的BASE_DIR路径
        
    Returns:
        环境配置字典
    """
    # 从环境变量获取运行环境，默认为开发环境
    environment = os.getenv('DJANGO_ENV', 'development').lower()
    
    if environment == 'production':
        return _get_production_configs(base_dir)
    elif environment == 'testing':
        return _get_testing_configs(base_dir)
    else:
        return _get_development_configs(base_dir)


def _get_development_configs(base_dir: Path) -> Dict[str, Any]:
    """开发环境配置"""
    return {
        'DEBUG': True,
        # 注意：ALLOWED_HOSTS配置已移至network_configs.py统一管理
        
        # 开发环境的安全密钥（仅用于开发）
        'SECRET_KEY': "django-insecure-q!qvev@pe__qs170saztzqy%^6msnq11&@yt(_@-!%(mc=($h-",
        
        # 注意：CORS配置已移至network_configs.py统一管理
        
        # 开发环境的会话配置
        'SESSION_COOKIE_SECURE': False,  # HTTP环境下设为False
        'SESSION_COOKIE_SAMESITE': "Lax",
    }


def _get_production_configs(base_dir: Path) -> Dict[str, Any]:
    """生产环境配置"""
    return {
        'DEBUG': False,
        # 注意：ALLOWED_HOSTS配置已移至network_configs.py统一管理
        
        # 生产环境必须从环境变量获取密钥
        'SECRET_KEY': os.getenv('SECRET_KEY', ''),
        
        # 注意：CORS配置已移至network_configs.py统一管理
        
        # 生产环境的会话配置
        'SESSION_COOKIE_SECURE': True,  # HTTPS环境下设为True
        'SESSION_COOKIE_SAMESITE': "None",  # 跨域需要设为None
        
        # 生产环境的数据库配置
        'DATABASES': {
            "default": {
                "ENGINE": "django.db.backends.postgresql",
                "NAME": os.getenv('DB_NAME', 'vision_app'),
                "USER": os.getenv('DB_USER', 'postgres'),
                "PASSWORD": os.getenv('DB_PASSWORD', ''),
                "HOST": os.getenv('DB_HOST', 'localhost'),
                "PORT": os.getenv('DB_PORT', '5432'),
            }
        } if os.getenv('DB_NAME') else {
            "default": {
                "ENGINE": "django.db.backends.sqlite3",
                "NAME": base_dir / "db" / "db.sqlite3",
            }
        },
    }


def _get_testing_configs(base_dir: Path) -> Dict[str, Any]:
    """测试环境配置"""
    return {
        'DEBUG': True,
        # 注意：ALLOWED_HOSTS配置已移至network_configs.py统一管理
        
        # 测试环境的安全密钥
        'SECRET_KEY': "test-secret-key-for-testing-only",
        
        # 测试环境使用内存数据库
        'DATABASES': {
            "default": {
                "ENGINE": "django.db.backends.sqlite3",
                "NAME": ":memory:",
            }
        },
        
        # 注意：CORS配置已移至network_configs.py统一管理
        
        # 测试环境的会话配置
        'SESSION_COOKIE_SECURE': False,
        'SESSION_COOKIE_SAMESITE': "Lax",
    }
