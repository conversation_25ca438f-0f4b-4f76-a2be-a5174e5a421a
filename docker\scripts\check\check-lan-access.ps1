Write-Host "=== AI Vision App LAN Access ===" -ForegroundColor Green
Write-Host ""

# 显示所有IP地址
Write-Host "Available IP addresses:" -ForegroundColor Yellow
ipconfig | findstr "IPv4"

Write-Host ""
Write-Host "Based on your IP addresses, LAN access URLs are:" -ForegroundColor Cyan
Write-Host ""

# 动态检测IP地址
$networkIPs = Get-NetIPAddress -AddressFamily IPv4 | Where-Object {
    $_.IPAddress -ne "127.0.0.1" -and
    $_.IPAddress -notlike "169.254.*"
} | Select-Object IPAddress, InterfaceAlias

# 查找主要LAN IP (192.168.1.x)
$mainLanIP = $networkIPs | Where-Object { $_.IPAddress -like "192.168.1.*" } | Select-Object -First 1
$otherLanIPs = $networkIPs | Where-Object { $_.IPAddress -like "192.168.*" -and $_.IPAddress -notlike "192.168.1.*" }

if ($mainLanIP) {
    Write-Host "Primary LAN IP ($($mainLanIP.IPAddress)):" -ForegroundColor Green
    Write-Host "  Frontend: http://$($mainLanIP.IPAddress):8080" -ForegroundColor White
    Write-Host "  Backend:  http://$($mainLanIP.IPAddress):8000" -ForegroundColor White
    Write-Host ""
} else {
    Write-Host "No primary LAN IP (192.168.1.x) found" -ForegroundColor Red
    Write-Host ""
}

# 显示其他LAN IP
if ($otherLanIPs.Count -gt 0) {
    Write-Host "Alternative IPs:" -ForegroundColor Yellow
    foreach ($ip in $otherLanIPs) {
        $interfaceType = if ($ip.InterfaceAlias -like "*VMware*") { "VMware" }
                        elseif ($ip.InterfaceAlias -like "*Docker*") { "Docker" }
                        else { $ip.InterfaceAlias }
        Write-Host "  $interfaceType ($($ip.IPAddress)): http://$($ip.IPAddress):8080" -ForegroundColor Gray
    }
    Write-Host ""
}

# 检查防火墙
Write-Host "Checking firewall rules..." -ForegroundColor Yellow
$frontendRule = netsh advfirewall firewall show rule name="Docker Frontend" 2>$null
$backendRule = netsh advfirewall firewall show rule name="Docker Backend" 2>$null

if ($frontendRule -and $frontendRule -match "Enabled") {
    Write-Host "✓ Frontend firewall rule exists" -ForegroundColor Green
} else {
    Write-Host "⚠ Frontend firewall rule missing" -ForegroundColor Red
    Write-Host "  Run as Administrator:" -ForegroundColor Yellow
    Write-Host "  netsh advfirewall firewall add rule name=`"Docker Frontend`" dir=in action=allow protocol=TCP localport=8080" -ForegroundColor Gray
}

if ($backendRule -and $backendRule -match "Enabled") {
    Write-Host "✓ Backend firewall rule exists" -ForegroundColor Green
} else {
    Write-Host "⚠ Backend firewall rule missing" -ForegroundColor Red
    Write-Host "  Run as Administrator:" -ForegroundColor Yellow
    Write-Host "  netsh advfirewall firewall add rule name=`"Docker Backend`" dir=in action=allow protocol=TCP localport=8000" -ForegroundColor Gray
}

Write-Host ""
Write-Host "Service status:" -ForegroundColor Yellow

# Get the docker directory path (two levels up from scripts/check/)
$dockerDir = Split-Path (Split-Path $PSScriptRoot -Parent) -Parent
Push-Location $dockerDir
docker-compose ps 2>$null
Pop-Location

Write-Host ""
Write-Host "To test from another device:" -ForegroundColor Cyan
Write-Host "  1. Connect to the same WiFi network" -ForegroundColor White
if ($mainLanIP) {
    Write-Host "  2. Open browser and visit: http://$($mainLanIP.IPAddress):8080" -ForegroundColor White
} else {
    Write-Host "  2. Open browser and visit: http://YOUR_LAN_IP:8080" -ForegroundColor White
}
Write-Host "  3. If it doesn't work, configure firewall rules above" -ForegroundColor White
