"""
OCR任务配置模块

包含各种OCR任务的详细配置参数，从settings.py中分离出来以提高可维护性。
每个配置项对应一个特定的OCR任务类型。
"""

from pathlib import Path


def get_ocr_task_configs(base_dir: Path) -> dict:
    """
    获取OCR任务配置字典
    
    Args:
        base_dir: Django项目的BASE_DIR路径
        
    Returns:
        dict: OCR任务配置字典
    """
    return {
        'license_plate_cn': {  # 车牌识别任务配置
            'use_angle_cls': False,  # 是否使用方向分类器
            'det_algorithm': 'DB',     # 检测算法
            'rec_algorithm': 'CRNN',   # 识别算法
            'lang': 'ch',  # 语言
            'use_gpu': False,  # 默认 False，可在 API 请求中覆盖
            'rec_image_shape': "3,64,320",  # 识别模型输入形状
            'det_limit_type': "max",  # 检测尺寸限制类型
            'det_limit_side_len': 320,  # 检测尺寸限制长度
            'det_db_thresh': 0.01,  # DB检测阈值
            'det_db_box_thresh': 0.01,  # DB框阈值
            'use_onnx': False,  # 是否使用 ONNX 推理
            'use_npu': False,  # 是否使用 NPU 推理
            'use_mlu': False,  # 是否使用 MLU 推理
            'use_xpu': False,  # 是否使用 XPU 推理
            'return_word_box': False,  # 是否返回单字框
            # 识别字典文件路径
            'rec_char_dict_path': str(base_dir / 'models' / 'system_models' / 'ocr' / 'car_liencese_ch' / 'AI_OCR_Rec_CHNLP_NCHW_1x3x64x320' / 'ppocr_keys_v1.txt'),
            # PaddleOCRSystemPredictor 中未在此处显式设置的默认参数：
            # det_box_type: "quad", enable_mkldnn: False, cpu_threads: 10, precision: "fp32",
            # benchmark: False, save_log_path: "./log_output/", show_log: True (但会被 Django 日志覆盖),
            # use_tensorrt: False, gpu_id: 0, rec_batch_num: 6,
            # rec_char_dict_path: (由 predictor 根据 paddle_ocr_lib_path 构建),
            # use_space_char: True, vis_font_path: (由 predictor 构建),
            # crop_res_save_dir: "./output", save_crop_res: False, warmup: False,
            # det_db_unclip_ratio: 1.5, det_db_score_mode: "slow", use_dilation: False,
            # cls_image_shape: "3, 48, 192", label_list: ['0', '180'], cls_batch_num: 6, cls_thresh: 0.9
        },

        'id_card_en': {  # 证件号码识别任务配置（英文）
            'use_angle_cls': False,  # 根据你的模型是否包含分类器调整
            # 'cls_model_dir': str(SYSTEM_MODELS_ROOT / 'ocr' / 'Identity_card_ch' / 'Identity_card_cls_model' / 'inference'), # 如果有分类器模型且使用，添加此行
            'det_algorithm': 'DB',     # 根据你的模型实际使用的算法调整
            'rec_algorithm': 'CRNN',   # 根据你的模型实际使用的算法调整
            'lang': 'ch',  # 根据你的模型语言调整
            'use_gpu': False,  # 默认 False，可在 API 请求中覆盖
            # 注意：rec_image_shape, det_limit_side_len, det_db_thresh 等参数应根据你的身份证模型特性进行配置
            'rec_image_shape': "3,48,320",  # rec_image_shape 是识别模型的输入形状，通常为 "3,48,320"
            'det_limit_side_len': 640,    # det_limit_side_len 是检测模型的输入尺寸限制，通常为 640
            'det_db_thresh': 0.3,        # det_db_thresh 是检测模型的阈值，通常为 0.3
            'det_db_box_thresh': 0.6,    # det_db_box_thresh 是检测模型的框阈值，通常为 0.6
            'det_db_unclip_ratio': 1.5,  # det_db_unclip_ratio 是检测模型的非裁剪比率，通常为 1.5
            'use_space_char': True,      # use_space_char 是是否使用空格符，通常为 True
            # **明确指定身份证识别模型的字典文件路径**
            'rec_char_dict_path': str(base_dir / 'models' / 'system_models' / 'ocr' / 'Identity_card_number_en' / 'Identity_card_rec_model' / 'en_dict.txt'),
            # 其他默认参数将从 ocr_paddle_predictor.py 中的 PaddleOCRSystemPredictor 默认值获取
            'use_onnx': False,  # 是否使用 ONNX 推理
            'use_npu': False,  # 是否使用 NPU 推理
            'use_mlu': False,  # 是否使用 MLU 推理
            'use_xpu': False,  # 是否使用 XPU 推理
            'return_word_box': False,  # 根据需要调整，通常OCR不需要这个
        },

        'general_text_mobile_ch_en': {  # PP-OCRv4 mobile模型配置（中英文）
            'use_angle_cls': False,  # 轻量级模型通常不使用方向分类器
            'det_algorithm': 'DB',     # PP-OCRv4使用DB检测算法
            'rec_algorithm': 'SVTR_LCNet',  # PP-OCRv4使用SVTR_LCNet识别算法
            'lang': 'ch',  # 中文语言
            'use_gpu': False,  # 默认CPU推理，适合移动端部署
            'rec_image_shape': "3,48,320",  # PP-OCRv4 mobile标准输入尺寸
            'det_limit_side_len': 640,    # 检测模型输入限制
            'det_db_thresh': 0.3,        # DB检测阈值
            'det_db_box_thresh': 0.6,    # DB框阈值
            'det_db_unclip_ratio': 1.5,  # DB非裁剪比率
            'use_space_char': True,      # 使用空格符
            # PP-OCRv4 mobile使用标准中英文字典
            'rec_char_dict_path': str(base_dir / 'models' / 'system_models' / 'ocr' / 'general_ocr_mobile_ch_en' / 'PP-OCRv4_mobile_rec_inference_ch_en' / 'ppocr_keys_v1.txt'),
            'use_onnx': False,  # 使用PaddlePaddle推理
            'use_npu': False,
            'use_mlu': False,
            'use_xpu': False,
            'return_word_box': False,
            # PP-OCRv4 mobile特有参数
            'det_db_score_mode': 'fast',  # 快速模式，适合移动端
            'rec_batch_num': 6,  # 批处理数量
            'max_text_length': 25,  # 最大文本长度
        },

        'general_text_mobile_en': {  # PP-OCRv4 mobile模型配置（纯英文）
            'use_angle_cls': False,  # 轻量级模型通常不使用方向分类器
            'det_algorithm': 'DB',     # PP-OCRv4使用DB检测算法
            'rec_algorithm': 'SVTR_LCNet',  # PP-OCRv4使用SVTR_LCNet识别算法
            'lang': 'en',  # 英文语言
            'use_gpu': False,  # 默认CPU推理，适合移动端部署
            'rec_image_shape': "3,48,320",  # PP-OCRv4 mobile标准输入尺寸
            'det_limit_side_len': 640,    # 检测模型输入限制
            'det_db_thresh': 0.3,        # DB检测阈值
            'det_db_box_thresh': 0.6,    # DB框阈值
            'det_db_unclip_ratio': 1.5,  # DB非裁剪比率
            'use_space_char': True,      # 使用空格符
            # PP-OCRv4 mobile使用英文字典
            'rec_char_dict_path': str(base_dir / 'models' / 'system_models' / 'ocr' / 'general_ocr_mobile_en' / 'PP-OCRv4_mobile_rec_inference_en' / 'en_dict.txt'),
            'use_onnx': False,  # 使用PaddlePaddle推理
            'use_npu': False,
            'use_mlu': False,
            'use_xpu': False,
            'return_word_box': False,
            # PP-OCRv4 mobile特有参数
            'det_db_score_mode': 'fast',  # 快速模式，适合移动端
            'rec_batch_num': 6,  # 批处理数量
            'max_text_length': 25,  # 最大文本长度
        },

        'default': {  # Fallback default parameters, can be more generic
            'use_angle_cls': False,
            'det_algorithm': 'DB',
            'rec_algorithm': 'SVTR_LCNet',  # A common general purpose algorithm
            'lang': 'ch',
            'use_gpu': False,
            'rec_image_shape': "3,48,320",
            'det_limit_side_len': 960,  # Changed from 640 to 960 for a more general default
            'det_db_thresh': 0.3,
            'det_db_box_thresh': 0.6,
            'use_onnx': False,
            'use_npu': False,
            'use_mlu': False,
            'use_xpu': False,
            'return_word_box': False,
        }
    }
