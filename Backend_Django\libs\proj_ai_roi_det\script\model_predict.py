﻿from ultralytics import YOLO
from pathlib import Path
import argparse
from tqdm import tqdm
import cv2
import torch

def parse_args():
    parser = argparse.ArgumentParser(description='YOLOv8推理脚本')
    parser.add_argument('--model', type=str, required=True, help='模型路径(best.pt)')
    parser.add_argument('--source', type=str, required=True, help='图像目录路径')
    parser.add_argument('--output', type=str, default='results', help='结果保存目录')
    parser.add_argument('--conf', type=float, default=0.25, help='置信度阈值')
    parser.add_argument('--device', type=str, default='', help='推理设备 (cpu/0/1/...)')
    return parser.parse_args()

def main():
    # 解析参数
    args = parse_args()
    
    # 检查路径
    model_path = Path(args.model)
    source_dir = Path(args.source)
    output_dir = Path(args.output)
    output_dir.mkdir(exist_ok=True)
    
    if not model_path.exists():
        raise FileNotFoundError(f"模型文件不存在: {model_path}")
    if not source_dir.exists():
        raise FileNotFoundError(f"图像目录不存在: {source_dir}")
        
    # 加载模型
    print(f"正在加载模型: {model_path}")
    model = YOLO(model_path)
    
    # 打印当前模型的类别标签
    print("模型类别标签:", model.names)
    
    # 获取所有图像文件
    image_files = list(source_dir.glob("*.jpg")) + list(source_dir.glob("*.png"))
    if not image_files:
        print(f"警告: 目录 {source_dir} 中没有找到图像文件")
        return
        
    # 执行推理
    print(f"开始处理 {len(image_files)} 张图像...")
    for img_path in tqdm(image_files):
        # 推理
        results = model.predict(
            source=str(img_path),
            conf=args.conf,
            device=args.device,
            save=True,
            project=str(output_dir),
            name=''
        )
        
        # 打印检测结果
        for r in results:
            if len(r.boxes) > 0:
                print(f"\n{img_path.name} 检测到 {len(r.boxes)} 个目标:")
                # 获取所有检测框的信息
                boxes = r.boxes
                for box in boxes:
                    cls = int(box.cls)
                    conf = float(box.conf)
                    # 获取边界框坐标
                    x1, y1, x2, y2 = box.xyxy[0].tolist()
                    print(f"类别: {model.names[cls]}, 置信度: {conf:.2f}")
                    print(f"边界框坐标: ({int(x1)}, {int(y1)}) - ({int(x2)}, {int(y2)})")
                
                # 打印当前使用的类别标签
                print("\n当前使用的类别标签:")
                for idx, name in model.names.items():
                    print(f"类别 {idx}: {name}")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"发生错误: {e}")