# Simple Code Sharing Setup Script for Development Machine (A Computer)
# 简化版代码共享设置脚本

param(
    [Parameter(Mandatory=$false, Position=0)]
    [string]$Action = "",

    [Parameter(Mandatory=$false)]
    [string]$ShareUser = "shareuser",

    [Parameter(Mandatory=$false)]
    [string]$SharePassword = "docker",

    [Parameter(Mandatory=$false)]
    [string]$ProjectPath = "",

    [Parameter(Mandatory=$false)]
    [string]$ManualIP = ""
)

$ErrorActionPreference = "Continue"

function Write-ColorOutput {
    param([string]$Message, [string]$Color = "White")
    Write-Host $Message -ForegroundColor $Color
}

function Test-AdminRights {
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

function Setup-NetworkSharing {
    Write-ColorOutput "=== Simple Network Sharing Setup ===" "Green"

    # Check admin rights
    if (!(Test-AdminRights)) {
        Write-ColorOutput "Error: Administrator rights required" "Red"
        Write-ColorOutput "Please run PowerShell as Administrator" "Yellow"
        exit 1
    }

    # Get project path
    if ([string]::IsNullOrEmpty($ProjectPath)) {
        $ProjectPath = Split-Path -Parent (Split-Path -Parent (Split-Path -Parent $PSScriptRoot))
    }

    Write-ColorOutput "Project path: $ProjectPath" "Yellow"

    # Skip user creation and permissions for simplicity
    Write-ColorOutput "Skipping user creation and permissions for simplicity..." "Cyan"
    Write-ColorOutput "Using existing Windows user for sharing" "Cyan"

    # Create network share
    Write-ColorOutput "Creating network share..." "Yellow"
    $ShareName = "ai_vision_shared"

    try {
        Write-ColorOutput "Checking for existing share..." "Gray"
        $existingShare = Get-SmbShare -Name $ShareName -ErrorAction SilentlyContinue
        if ($existingShare) {
            Write-ColorOutput "Share $ShareName already exists, removing old share" "Cyan"
            Remove-SmbShare -Name $ShareName -Force -Confirm:$false
            Write-ColorOutput "Old share removed" "Gray"
        }

        Write-ColorOutput "Creating new SMB share..." "Gray"
        Write-ColorOutput "Share name: $ShareName" "Gray"
        Write-ColorOutput "Share path: $ProjectPath" "Gray"

        # Create share with Everyone access for simplicity
        New-SmbShare -Name $ShareName -Path $ProjectPath -ReadAccess "Everyone" -ChangeAccess "Everyone"
        Write-ColorOutput "Network share $ShareName created successfully" "Green"
    }
    catch {
        Write-ColorOutput "Failed to create share: $($_.Exception.Message)" "Red"
        Write-ColorOutput "Trying alternative method..." "Yellow"

        # Try using net share command as fallback
        try {
            $netShareCmd = "net share $ShareName=`"$ProjectPath`" /grant:everyone,full"
            Write-ColorOutput "Running: $netShareCmd" "Gray"
            Invoke-Expression $netShareCmd
            Write-ColorOutput "Share created using net share command" "Green"
        }
        catch {
            Write-ColorOutput "Alternative method also failed: $($_.Exception.Message)" "Red"
            return
        }
    }

    # Get local IP address
    Write-ColorOutput "Getting local IP address..." "Yellow"
    $LocalIP = $null

    # Check if manual IP is provided
    if (-not [string]::IsNullOrEmpty($ManualIP)) {
        $LocalIP = $ManualIP
        Write-ColorOutput "Using manually specified IP: $LocalIP" "Green"
    } else {
        try {
            # Get all IPv4 addresses and show them for selection
            $allIPs = Get-NetIPAddress -AddressFamily IPv4 | Where-Object {
                $_.IPAddress -ne "127.0.0.1" -and
                $_.IPAddress -notlike "169.254.*" -and
                $_.AddressState -eq "Preferred"
            } | Select-Object IPAddress, InterfaceAlias

            Write-ColorOutput "Available IP addresses:" "Cyan"
            foreach ($ip in $allIPs) {
                Write-ColorOutput "  $($ip.IPAddress) ($($ip.InterfaceAlias))" "White"
            }

            # Try to find the most likely LAN IP (prioritize 192.168.1.x)
            $LocalIP = ($allIPs | Where-Object {
                $_.IPAddress -like "192.168.1.*"
            }).IPAddress | Select-Object -First 1

            if (-not $LocalIP) {
                $LocalIP = ($allIPs | Where-Object {
                    $_.IPAddress -like "192.168.*"
                }).IPAddress | Select-Object -First 1
            }

            if (-not $LocalIP) {
                $LocalIP = ($allIPs | Where-Object {
                    $_.IPAddress -like "10.*"
                }).IPAddress | Select-Object -First 1
            }

            if (-not $LocalIP) {
                $LocalIP = $allIPs[0].IPAddress
            }

            if ($LocalIP) {
                Write-ColorOutput "Auto-detected IP: $LocalIP" "Green"
                Write-ColorOutput "If this is incorrect, use: -ManualIP '*************'" "Yellow"
            } else {
                $LocalIP = "*************"
                Write-ColorOutput "Could not auto-detect, using default: $LocalIP" "Yellow"
            }
        }
        catch {
            Write-ColorOutput "Could not detect IP automatically, using default" "Yellow"
            $LocalIP = "*************"
        }
    }

    # Display configuration info
    Write-ColorOutput "" "White"
    Write-ColorOutput "=== Network Share Configuration Complete ===" "Green"
    Write-ColorOutput "" "White"
    Write-ColorOutput "Share Name: $ShareName" "Cyan"
    Write-ColorOutput "Share Path: $ProjectPath" "Cyan"
    Write-ColorOutput "Local IP: $LocalIP" "Cyan"
    Write-ColorOutput "Access Address: \\$LocalIP\$ShareName" "Cyan"
    Write-ColorOutput "" "White"
    Write-ColorOutput "B Computer Connection Info:" "Yellow"
    Write-ColorOutput "Network Path: \\$LocalIP\$ShareName" "White"
    Write-ColorOutput "Use your Windows username and password to connect" "White"
    Write-ColorOutput "" "White"

    # Test share
    Write-ColorOutput "Testing network share..." "Yellow"
    try {
        $TestPath = "\\$env:COMPUTERNAME\$ShareName"
        if (Test-Path $TestPath) {
            Write-ColorOutput "Network share test successful!" "Green"
        } else {
            Write-ColorOutput "Network share test failed, but share may still work" "Yellow"
        }
    }
    catch {
        Write-ColorOutput "Could not test share, but it may still work" "Yellow"
    }

    Write-ColorOutput "" "White"
    Write-ColorOutput "Setup complete!" "Green"
    Write-ColorOutput "Next steps:" "Cyan"
    Write-ColorOutput "1. Note down the network path: \\$LocalIP\$ShareName" "White"
    Write-ColorOutput "2. On B computer, run sync script with this IP: $LocalIP" "White"
    Write-ColorOutput "3. Use your Windows credentials when prompted" "White"
}

function Remove-NetworkSharing {
    Write-ColorOutput "=== Removing Network Share ===" "Yellow"

    if (!(Test-AdminRights)) {
        Write-ColorOutput "Error: Administrator rights required" "Red"
        exit 1
    }

    $ShareName = "ai_vision_shared"

    try {
        Remove-SmbShare -Name $ShareName -Force -Confirm:$false -ErrorAction SilentlyContinue
        Write-ColorOutput "Network share removed" "Green"
    }
    catch {
        Write-ColorOutput "Failed to remove share: $($_.Exception.Message)" "Red"
        # Try net share command
        try {
            Invoke-Expression "net share $ShareName /delete"
            Write-ColorOutput "Share removed using net share command" "Green"
        }
        catch {
            Write-ColorOutput "Could not remove share" "Red"
        }
    }
}

function Show-ShareStatus {
    Write-ColorOutput "=== Network Share Status ===" "Green"

    Write-ColorOutput "SMB Share List:" "Yellow"
    try {
        Get-SmbShare | Where-Object { $_.Name -like "*ai_vision*" } | Format-Table -AutoSize
    }
    catch {
        Write-ColorOutput "Could not get SMB shares, trying net share..." "Yellow"
        net share
    }

    Write-ColorOutput "Local IP Addresses:" "Yellow"
    try {
        Get-NetIPAddress -AddressFamily IPv4 | Where-Object {
            $_.IPAddress -ne "127.0.0.1" -and $_.PrefixOrigin -ne "WellKnown"
        } | Select-Object IPAddress, InterfaceAlias | Format-Table -AutoSize
    }
    catch {
        Write-ColorOutput "Could not get IP addresses" "Red"
        ipconfig
    }
}

# Main menu
if ([string]::IsNullOrEmpty($Action)) {
    Write-ColorOutput "=== Simple Code Sharing Setup ===" "Green"
    Write-ColorOutput "" "White"
    Write-ColorOutput "1. setup    - Setup network sharing (simple version)" "Cyan"
    Write-ColorOutput "2. remove   - Remove network sharing" "Cyan"
    Write-ColorOutput "3. status   - Show sharing status" "Cyan"
    Write-ColorOutput "" "White"
    Write-ColorOutput "Usage:" "Yellow"
    Write-ColorOutput ".\scripts\setup-code-sharing-simple.ps1 setup" "White"
    Write-ColorOutput ".\scripts\setup-code-sharing-simple.ps1 setup -ManualIP '*************'" "White"
    Write-ColorOutput "" "White"
    Write-ColorOutput "Parameters:" "Yellow"
    Write-ColorOutput "-ManualIP    Manually specify your computer's IP address" "Gray"
    Write-ColorOutput "" "White"
    Write-ColorOutput "Note: This is a simplified version that skips complex permissions" "Gray"
    exit 0
}

switch ($Action) {
    "setup" { Setup-NetworkSharing }
    "remove" { Remove-NetworkSharing }
    "status" { Show-ShareStatus }
    default {
        Write-ColorOutput "Invalid parameter: $Action" "Red"
        Write-ColorOutput "Available parameters: setup, remove, status" "Yellow"
    }
}
