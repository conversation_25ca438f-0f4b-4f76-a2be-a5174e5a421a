# Quick Enable Docker Container Auto-Start Script
# One-click setup for existing containers to auto-start after system reboot

Write-Host "=== Enable Docker Container Auto-Start ===" -ForegroundColor Cyan
Write-Host ""

# Check Docker service
Write-Host "Checking Docker service..." -ForegroundColor Yellow
try {
    docker version | Out-Null
    Write-Host "✓ Docker service is running" -ForegroundColor Green
} catch {
    Write-Host "✗ Docker service is not running, please start Docker Desktop first" -ForegroundColor Red
    exit 1
}

# Show current container status
Write-Host "`nCurrent container status:" -ForegroundColor Yellow
docker ps -a --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"

Write-Host "`nSetting container auto-restart policy..." -ForegroundColor Yellow

# Set backend container
Write-Host "Setting backend container..." -ForegroundColor Cyan
docker update --restart=unless-stopped ai-vision-backend-hotreload
if ($LASTEXITCODE -eq 0) {
    Write-Host "✓ Backend container configured successfully" -ForegroundColor Green
} else {
    Write-Host "✗ Backend container configuration failed" -ForegroundColor Red
}

# Set frontend container
Write-Host "Setting frontend container..." -ForegroundColor Cyan
docker update --restart=unless-stopped ai-vision-frontend-hotreload
if ($LASTEXITCODE -eq 0) {
    Write-Host "✓ Frontend container configured successfully" -ForegroundColor Green
} else {
    Write-Host "✗ Frontend container configuration failed" -ForegroundColor Red
}

Write-Host "`n=== Configuration Complete ===" -ForegroundColor Green
Write-Host "Containers will now auto-start when Docker service starts" -ForegroundColor Yellow
Write-Host ""
Write-Host "Verify settings:" -ForegroundColor Cyan
Write-Host "docker inspect ai-vision-backend-hotreload --format '{{.HostConfig.RestartPolicy.Name}}'" -ForegroundColor Gray
Write-Host "docker inspect ai-vision-frontend-hotreload --format '{{.HostConfig.RestartPolicy.Name}}'" -ForegroundColor Gray
Write-Host ""
Write-Host "Restart policy explanation:" -ForegroundColor Cyan
Write-Host "• unless-stopped: Always restart unless manually stopped (recommended)" -ForegroundColor Gray
Write-Host "• After system reboot, containers will auto-start when Docker service starts" -ForegroundColor Gray
