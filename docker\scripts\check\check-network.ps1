# Network Configuration Check for AI Vision App
# Encoding: UTF-8

Write-Host "=== Network Configuration Check ===" -ForegroundColor Green
Write-Host ""

# Get server IP addresses
Write-Host "1. Server IP Addresses:" -ForegroundColor Yellow
$networkIPs = Get-NetIPAddress -AddressFamily IPv4 | Where-Object {
    $_.IPAddress -ne "127.0.0.1" -and
    $_.IPAddress -notlike "169.254.*"
} | Select-Object IPAddress, InterfaceAlias

# Prioritize main LAN IP (192.168.1.x)
$mainLanIP = $null
$otherLanIPs = @()
$otherIPs = @()

foreach ($ip in $networkIPs) {
    if ($ip.IPAddress -like "192.168.1.*") {
        $mainLanIP = $ip
        Write-Host "  Main LAN IP: $($ip.IPAddress) ($($ip.InterfaceAlias))" -ForegroundColor Green
    } elseif ($ip.IPAddress -like "192.168.*") {
        $otherLanIPs += $ip
    } else {
        $otherIPs += $ip
    }
}

# Display other LAN IPs
foreach ($ip in $otherLanIPs) {
    Write-Host "  Other LAN IP: $($ip.IPAddress) ($($ip.InterfaceAlias))" -ForegroundColor Yellow
}

# Display other IPs (Docker, VMware, etc.)
foreach ($ip in $otherIPs) {
    Write-Host "  Other IP: $($ip.IPAddress) ($($ip.InterfaceAlias))" -ForegroundColor Gray
}

# Check port bindings
Write-Host ""
Write-Host "2. Port Bindings:" -ForegroundColor Yellow
$port8080 = netstat -an | findstr ":8080" | findstr "LISTENING"
$port8000 = netstat -an | findstr ":8000" | findstr "LISTENING"

if ($port8080) {
    Write-Host "  Port 8080 (Frontend): LISTENING" -ForegroundColor Green
    Write-Host "    $port8080" -ForegroundColor Gray
} else {
    Write-Host "  Port 8080 (Frontend): NOT LISTENING" -ForegroundColor Red
}

if ($port8000) {
    Write-Host "  Port 8000 (Backend): LISTENING" -ForegroundColor Green
    Write-Host "    $port8000" -ForegroundColor Gray
} else {
    Write-Host "  Port 8000 (Backend): NOT LISTENING" -ForegroundColor Red
}

# Check Docker containers
Write-Host ""
Write-Host "3. Docker Container Status:" -ForegroundColor Yellow
try {
    $containers = docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | findstr "ai-vision"
    if ($containers) {
        $containers | ForEach-Object {
            Write-Host "  $_" -ForegroundColor White
        }
    } else {
        Write-Host "  No AI Vision containers running" -ForegroundColor Red
    }
} catch {
    Write-Host "  Could not check Docker containers" -ForegroundColor Yellow
}

# Show access URLs
Write-Host ""
Write-Host "4. Access URLs:" -ForegroundColor Yellow
Write-Host "  Local access:" -ForegroundColor Cyan
Write-Host "    Frontend: http://localhost:8080" -ForegroundColor White
Write-Host "    Backend:  http://localhost:8000" -ForegroundColor White

# Use main LAN IP if available, otherwise fall back to any 192.168.x.x IP
$preferredLanIP = $null
if ($mainLanIP) {
    $preferredLanIP = $mainLanIP.IPAddress
} elseif ($otherLanIPs.Count -gt 0) {
    $preferredLanIP = $otherLanIPs[0].IPAddress
}

if ($preferredLanIP) {
    Write-Host "  LAN access (recommended):" -ForegroundColor Cyan
    Write-Host "    Frontend: http://${preferredLanIP}:8080" -ForegroundColor Green
    Write-Host "    Backend:  http://${preferredLanIP}:8000" -ForegroundColor Green

    # Show alternative IPs if available
    if ($otherLanIPs.Count -gt 0 -and $mainLanIP) {
        Write-Host "  Alternative LAN IPs:" -ForegroundColor Cyan
        foreach ($ip in $otherLanIPs) {
            Write-Host "    http://$($ip.IPAddress):8080 ($($ip.InterfaceAlias))" -ForegroundColor Yellow
        }
    }
} else {
    Write-Host "  LAN access: No 192.168.x.x IP found" -ForegroundColor Red
}

# Firewall configuration
Write-Host ""
Write-Host "5. Firewall Configuration:" -ForegroundColor Yellow
Write-Host "  To allow LAN access, run these commands as Administrator:" -ForegroundColor Cyan
Write-Host "    netsh advfirewall firewall add rule name=`"AI Vision Frontend`" dir=in action=allow protocol=TCP localport=8080" -ForegroundColor Gray
Write-Host "    netsh advfirewall firewall add rule name=`"AI Vision Backend`" dir=in action=allow protocol=TCP localport=8000" -ForegroundColor Gray

Write-Host ""
Write-Host "=== Check Complete ===" -ForegroundColor Green
