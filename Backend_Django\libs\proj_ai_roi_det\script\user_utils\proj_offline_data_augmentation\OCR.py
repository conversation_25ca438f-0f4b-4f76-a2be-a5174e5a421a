import cv2
import math
import json
import tqdm
import random
import shutil
import subprocess
import numpy as np
from pathlib import Path, PosixPath
from typing import TypedDict, Callable, get_type_hints
from multiprocessing import Pool

class LabelPaddleOCR(TypedDict):
    """
    PaddleOCR标签

    Args:
        transcription (str): 内容描述
        points (list[list[int, int]]): 坐标点
        difficult (bool): 是否为难例
    """
    transcription: str
    points: list[list[int, int]]
    difficult: bool


class DatasetOCR():
    def __init__(
            self,
            label_file: str = "Label.txt",
            suffix: str = ".bmp",
        ) -> None:
        """
        初始化

        Args:
            label_file (str): 标签文件名
            suffix (str): 保存图片后缀
        """
        self.label_file = label_file
        self.suffix = suffix


    def _read_original_label(
            self,
            src_path: str,
            shuffle: bool = False
        ) -> dict[str, list[LabelPaddleOCR]]:
        """
        读取PaddleOCR标签

        Args:
            src_path (str): 标签文件存放路径
            shuffle (bool): 打乱标签顺序
        """
        # 检查标签文件是否存在
        labels_dict:dict[str, list[LabelPaddleOCR]] = {}
        label_file = Path(src_path, self.label_file)
        if not label_file.exists():
            print(f"Label file not found in: {src_path}")
            return labels_dict

        # 按行读取标签内容
        content_list = []
        with open(label_file, 'r', encoding='utf-8') as file:
            content_list = file.read().split("\n")
        # 打乱标签顺序
        if shuffle:
            random.shuffle(content_list)
        label_path_abs:PosixPath = Path(label_file).parent.absolute()
        for line in content_list:
            # 跳过没有分隔符的行
            if "\t" not in line:
                continue
            file, content = line.split("\t")
            labels_dict[f"{Path(label_path_abs, file)}"] = json.loads(content)

        return labels_dict


    def _write_original_label(
            self,
            labels_dict: dict[str, list[LabelPaddleOCR]],
            dst_path: str,
            append_mode: bool = False,
        ) -> None:
        """
        写PaddleOCR标签文件

        Args:
            labels_dict (dict[str, list[LabelPaddleOCR]]): 标签文件内容
            dst_path (str): 标签文件保存路径
            append_mode (bool): 追加模式
        """
        # 将内容转换成字符串
        label_content:list[str] = []
        for img in labels_dict.keys():
            content = json.dumps(labels_dict[img], ensure_ascii=False)
            label_content.append(f"{img}\t{content}")
        # 写回标签文件
        flag = 'a' if append_mode else 'w'
        with open(Path(dst_path, self.label_file), flag, encoding='utf-8') as file:
            file.write("\n".join(label_content) + '\n')


    def _write_rec_label(
            self,
            labels_list: list[str],
            dst_path: str,
            modify_func: Callable[[str], str] | None = None,
        ) -> None:
        """
        写PaddleOCR Rec标签文件

        Args:
            labels_dict (list[str]): 标签文件内容
            dst_path (str): 标签文件保存路径
        """
        # 合并标签相同的结果
        combined_labels: dict[str, list[str]] = {}
        for line in labels_list:
            if '\t' not in line:
                continue
            # 将内容相同的图片放入同一个列表
            img, content = line.split('\t')
            img_list = combined_labels.get(content, [])
            img_list.append(img)
            combined_labels[content] = img_list
        # 转换成PaddleOCR能识别的标签格式
        new_labels_list: list[str] = []
        for content in combined_labels.keys():
            img_list = json.dumps(combined_labels[content])
            final_content = content
            if modify_func is not None:
                final_content = modify_func(content)
            new_labels_list.append(f"{img_list}\t{final_content}")
        # 写回标签文件
        with open(Path(dst_path, self.label_file), 'w', encoding='utf-8') as file:
            file.write("\n".join(new_labels_list) + '\n')


    def _generate_dataset_sample_based(
            self,
            labels_dict: dict[str, list[LabelPaddleOCR]],
            img_file_dict: dict[str, list[PosixPath]],
            sample_idxes: list[str],
            dst_path: str,
            index: int,
            only_label: bool,
        ) -> int:
        """
        基于样本生成数据集

        Args:
            labels_dict (dict[str, list[LabelPaddleOCR]]): 原始标签字典，键：图片名：值：标签
            img_file_dict (dict[str, list[PosixPath]]): 样本字典，键：样本名，值：图片列表
            sample_idxes (list[str]): 样本索引列表
            dst_path (str): 生成路径
            index (int): 标签编号
            only_label (bool): 仅生成label

        Returns:
            (int): 最新索引值
        """
        new_labels_dict:dict[str, list[LabelPaddleOCR]] = {} # 标签字典
        for idx in sample_idxes:
            # 逐个添加标签
            img_list = img_file_dict.get(idx, [])
            for img in img_list:
                if not only_label:
                    dst_file = Path(dst_path, f"{index}-{img.stem}{img.suffix}")
                    shutil.copy(img, dst_file)
                    new_labels_dict[dst_file.relative_to(dst_file.parent)] = labels_dict[img.as_posix()]
                else:
                    new_labels_dict[img.absolute()] = labels_dict[img.as_posix()]
                index += 1
        # 写标签文件
        self._write_original_label(new_labels_dict, dst_path, append_mode=True)
        return index


    def _generate_dataset(
            self,
            labels_dict: dict[str, list[LabelPaddleOCR]],
            dst_path: str,
            only_label: bool,
        ) -> None:
        """
        基于样本生成数据集

        Args:
            labels_dict (dict[str, list[LabelPaddleOCR]]): 原始标签字典，键：图片名：值：标签
            dst_path (str): 生成路径
        """
        index = 0
        new_labels_dict:dict[str, list[LabelPaddleOCR]] = {} # 标签字典
        for img in labels_dict.keys():
            if not only_label:
                dst_file = Path(dst_path, f"{index}-{img.stem}{img.suffix}")
                shutil.copy(img, dst_file)
                new_labels_dict[dst_file.relative_to(dst_file.parent)] = labels_dict[img.as_posix()]
            else:
                new_labels_dict[Path(img).absolute()] = labels_dict[Path(img).as_posix()]
            index += 1
        # 写标签文件
        self._write_original_label(new_labels_dict, dst_path, append_mode=True)


    def Distribute_Data_Set(
            self,
            src_pathes: dict[str, float],
            train: float,
            val: float,
            dst_path: str,
            append_mode: bool = False,
            seed: int = 20000330,
            only_label: bool = False,
        ) -> tuple[str] | None:
        """
        基于PaddleOCR标签划分训练集、验证集和测试集

        Args:
            src_pathes (list[str]): 源文件路径列表
            train (float): 训练数据集比例
            val (float): 验证数据集比例
            dst_path (str): 目标生成路径
            append_mode (bool): 是否采用追加模式，追加模式下会在保留原有数据集的基础上增加新数据
            seed (int): 随机种子，用于确保数据集划分的结果不变
            only_label (bool): 仅生成label

        Returns:
            (tuple[str] | None): 训练集、验证集和测试集位置，失败时返回None
        """
        if dst_path in src_pathes.keys():
            print("src_path and dst_path can't be the same.")
            return None

        # 初始化输出路径
        train_path = Path(dst_path, "train") # 训练集路径
        val_path = Path(dst_path, "val") # 验证集路径
        test_path = Path(dst_path, "test") # 测试集路径

        # 非追加模式下，移除原有路径
        if append_mode == False:
            shutil.rmtree(train_path, ignore_errors=True)
            shutil.rmtree(val_path, ignore_errors=True)
            shutil.rmtree(test_path, ignore_errors=True)

        # 创建输出路径
        train_path.mkdir(parents=True, exist_ok=True)
        val_path.mkdir(parents=True, exist_ok=True)
        test_path.mkdir(parents=True, exist_ok=True)

        # 遍历标签
        index = 0
        for src_path in src_pathes.keys():
            proportion = min(1.0, src_pathes[src_path]) # 数据分配比例

            # 读取标签
            labels_dict:dict[str, list[LabelPaddleOCR]] = self._read_original_label(src_path)

            # 区分样本
            img_file_dict:dict[str, list[PosixPath]] = {}
            for key in labels_dict.keys():
                key = Path(key)
                name = key.stem # 获取文件名
                # 当文件名通过'-'划分时，认为该样本对应多张图片
                if '-' not in name:
                    idx = f"{Path(key.parent, name)}"
                    img_file_dict[idx] = [key] # 单样本单图片
                    continue
                # 单样本多图片
                stem =  name.split("-")[0]
                idx = f"{Path(key.parent, stem)}"
                img_list = img_file_dict.get(idx, [])
                img_list.append(key)
                img_file_dict[idx] = img_list
            total_samples:int = len(img_file_dict) # 样本总数

            # 计算训练集图像数量
            num_train_samples:int = round(total_samples * train)
            num_train_samples = max(0, num_train_samples)
            remain_samples:int = total_samples - num_train_samples # 计算剩余图像数量

            # 计算验证集图像数量
            num_val_samples:int = round(total_samples * val)
            num_val_samples = max(0, num_val_samples)
            num_val_samples = min(num_val_samples, remain_samples) # 不能分配超过剩余图像数量的图片
            remain_samples = remain_samples - num_val_samples

            # 计算测试集图像数量
            num_test_samples = remain_samples # 剩余图像分配给测试集

            # 打乱样本顺序
            random.seed(seed)
            idxes = list(img_file_dict.keys())
            random.shuffle(idxes)

            # 分配训练集
            start = 0 # 起始位置
            end = start + int(num_train_samples * proportion) # 终止位置
            index = self._generate_dataset_sample_based(
                labels_dict,
                img_file_dict,
                idxes[start: end],
                train_path,
                index,
                only_label
            )

            # 分配验证集
            start = num_train_samples
            end = start + int(num_val_samples * proportion)
            index = self._generate_dataset_sample_based(
                labels_dict,
                img_file_dict,
                idxes[start: end],
                val_path,
                index,
                only_label
            )

            # 分配测试集
            start = num_train_samples + num_val_samples
            end = start + int(num_test_samples * proportion)
            index = self._generate_dataset_sample_based(
                labels_dict,
                img_file_dict,
                idxes[start: end],
                test_path,
                index,
                only_label
            )

        return (train_path, val_path, test_path)


    def Classify_Data(
            self,
            src_pathes: dict[str, float],
            classify_func: Callable[[str], str],
        ) -> dict[str, dict[str, list[LabelPaddleOCR]]] | None:
        """
        对数据进行分类

        Args:
            src_pathes (list[str]): 源文件路径列表
            classify_func (Callable[[str], str]): 数据分类函数，输入：字符内容，输出：分类ID

        Returns:
            (dict[str, list[LabelPaddleOCR]] | None): 分类好的数据，键：类别ID，值：图片及对应标签，失败时返回None
        """
        # 获取标签文件
        labels_dict:dict[str, list[LabelPaddleOCR]] = {}
        for src_path in src_pathes:
            labels_dict.update(self._read_original_label(src_path))
        classified_labels_dict:dict[str, dict[str, list[LabelPaddleOCR]]] = {}

        # 逐个处理文件
        for img in labels_dict.keys():
            # 将全部字符内容转换为字符串
            boxes = labels_dict[img]
            transcriptions = [box['transcription'] for box in boxes]
            transcriptions.sort()
            contents = "".join(transcriptions)
            # 获取分类ID
            classify_id = classify_func(contents)
            # 分类数据
            new_labels_dict = classified_labels_dict.get(classify_id, {})
            new_labels_dict[img] = labels_dict[img]
            classified_labels_dict[classify_id] = new_labels_dict

        return classified_labels_dict


    def Write_Classified_Data(
            self,
            modified_proportion: dict[str, float],
            classified_labels_dict: dict[str, dict[str, list[LabelPaddleOCR]]],
            dst_path: str,
            only_label: bool = False,
        ) -> str | None:
        """
        保存分类数据

        Args:
            modified_proportion (dict[str, float]): 调控比例，用于控制数据占比
            classified_labels_dict (dict[str, list[LabelPaddleOCR]]): 分类好的数据，键：类别ID，值：图片及对应标签
            dst_path (str): 目标生成路径
            only_label (bool): 仅生成label

        Returns:
            (str | None): 生成数据位置，失败时返回None
        """
        dst = Path(dst_path) # 目标路径

        # 创建存储路径
        shutil.rmtree(dst, ignore_errors=True)
        dst.mkdir(parents=True, exist_ok=True)

        # 逐个存储每个类别的标签
        new_labels_dict: dict[str, list[LabelPaddleOCR]] = {}
        for id in classified_labels_dict.keys():
            # 获取需要存储的标签
            labels_dict = classified_labels_dict[id]
            imgs = list(labels_dict.keys())
            proportion = 1.0
            if id in modified_proportion.keys():
                proportion = min(1.0, modified_proportion[id])
            imgs = imgs[0: round(len(imgs) * proportion)]
            for img in imgs:
                new_labels_dict[img] = labels_dict[img]

        self._generate_dataset(new_labels_dict, dst_path, only_label)
        return dst_path


    def Affine_Transform(
            self,
            src_pathes: list[str],
            quantity: int,
            dst_path: str,
            scale_range: tuple[float] = (0.8, 1.2),
            shear_range: tuple[float] = (-0.1, 0.1),
            angle_range: float = (-10, 10),
            seed: int = 20241108,
        ) -> str | None:
        """
        对图像做仿射变换

        Args:
            src_pathes (list[str]): 源文件路径列表
            quantity (int): 生成图片数量
            dst_path (str): 目标生成路径
            scale_range (tuple[float]): 缩放范围（单位：百分比）
            shear_range (tuple[float]): 剪切范围（单位：百分比）
            angle_range (tuple[int]): 旋转范围（单位：度）
            seed (int): 随机种子，用于确保随机过程一致

        Returns:
            (str | None): 生成数据位置，失败时返回None
        """
        if dst_path in src_pathes:
            return None

        dst = Path(dst_path)

        # 创建存储路径
        shutil.rmtree(dst, ignore_errors=True)
        dst.mkdir(parents=True, exist_ok=True)

        # 设置随机种子
        random.seed(seed)

        # 获取标签文件
        labels_dict:dict[str, list[LabelPaddleOCR]] = {}
        for src_path in src_pathes:
            labels_dict.update(self._read_original_label(src_path))
        new_labels_dict:dict[str, list[LabelPaddleOCR]] = {}

        # 逐个处理文件
        index = 0
        for img in labels_dict.keys():
            for i in range(quantity):
                # 读取图片
                image = cv2.imread(img, cv2.IMREAD_GRAYSCALE)
                height, width = image.shape[:2]
                
                # 随机选择缩放、剪切和旋转参数
                _angle = random.uniform(*angle_range)
                _scale = random.uniform(*scale_range)
                _shear_x = random.uniform(*shear_range)
                _shear_y = random.uniform(*shear_range)

                # 计算仿射变换矩阵
                cos_a = np.cos(np.radians(_angle))
                sin_a = np.sin(np.radians(_angle))
                
                # 构建仿射矩阵（不包含平移）
                affine_matrix = np.array([
                    [_scale * cos_a + _shear_x * sin_a, -_scale * sin_a + _shear_y * cos_a, 0],
                    [_scale * sin_a + _shear_x * cos_a,  _scale * cos_a + _shear_y * sin_a, 0]
                ], dtype=np.float32)

                # 计算变换后图像的边界
                # 获取图像的四个角点
                corners = np.array([
                    [0, 0],
                    [width, 0],
                    [width, height],
                    [0, height]
                ], dtype=np.float32)
                
                # 将角点变换为齐次坐标
                corners = np.c_[corners, np.ones(4)]
                
                # 应用仿射变换（不包含平移）到角点
                transformed_corners = np.dot(corners, affine_matrix.T)
                
                # 计算新图像的边界
                min_x = np.floor(transformed_corners[:, 0].min()).astype(int)
                max_x = np.ceil(transformed_corners[:, 0].max()).astype(int)
                min_y = np.floor(transformed_corners[:, 1].min()).astype(int)
                max_y = np.ceil(transformed_corners[:, 1].max()).astype(int)
                
                # 计算新的尺寸和中心点偏移
                new_w = max_x - min_x
                new_h = max_y - min_y
                
                # 更新仿射矩阵，加入平移分量
                # 平移量需要确保变换后的图像完全包含在新的边界内
                affine_matrix[0, 2] = -min_x  # 水平平移
                affine_matrix[1, 2] = -min_y  # 垂直平移

                # 应用完整的仿射变换到图像
                transformed_image = cv2.warpAffine(
                    src=image,
                    M=affine_matrix,
                    dsize=(new_w, new_h),
                    flags=cv2.INTER_LINEAR,
                    borderMode=cv2.BORDER_CONSTANT,
                    borderValue=0  # 设置背景为黑色
                )

                # 转换标注框坐标
                boxes = labels_dict[img]
                transformed_boxes = []
                for box in boxes:
                    points = np.array(box['points'])
                    points = points.astype(np.float32).reshape(-1, 1, 2)
                    transformed_points = cv2.transform(points, affine_matrix).reshape(-1, 2).tolist()
                    transformed_box = box.copy()
                    transformed_box['points'] = transformed_points
                    transformed_boxes.append(transformed_box)

                # 保存结果
                saved_image = Path(dst, f"{index}{self.suffix}").absolute()
                cv2.imwrite(saved_image.as_posix(), transformed_image)
                new_labels_dict[saved_image] = transformed_boxes
                index += 1

        self._write_original_label(new_labels_dict, dst_path)
        return dst_path


    def Add_Shadow(
            self,
            src_pathes: list[str],
            quantity: int,
            dst_path: str,
            num_circles: int = 5,
            max_radius: int = 50,
            shadow_intensity_range: tuple[float] = (0.3, 0.6),
            brightness_adjust_range: tuple[float] = (0.5, 3.5),
            seed: int = 20241108,
        ) -> str | None:
        """
        添加阴影

        Args:
            src_pathes (list[str]): 源文件路径列表
            quantity (int): 生成图片数量
            dst_path (str): 目标生成路径
            num_circles (int): 用于生成阴影的随机圆的个数
            max_radius (int): 圆形阴影的最大半径
            shadow_intensity_range (tuple[float]): 阴影的强度范围
            brightness_adjust_range (tuple[float]): 亮度调整范围
            seed (int): 随机种子，用于确保随机过程一致

        Returns:
            (str | None): 生成数据位置，失败时返回None
        """
        if dst_path in src_pathes:
            return None

        dst = Path(dst_path) # 目标路径

        # 创建存储路径
        shutil.rmtree(dst, ignore_errors=True)
        dst.mkdir(parents=True, exist_ok=True)

        # 设置随机种子
        random.seed(seed)

        # 获取标签文件
        labels_dict:dict[str, list[LabelPaddleOCR]] = {}
        for src_path in src_pathes:
            labels_dict.update(self._read_original_label(src_path))
        new_labels_dict:dict[str, list[LabelPaddleOCR]] = {}

        # 逐个处理文件
        index = 0
        for img in labels_dict.keys():
            for i in range(quantity):
                # 读取图片
                image = cv2.imread(img, cv2.IMREAD_GRAYSCALE)
                height = image.shape[0]
                width = image.shape[1]

                # 调整亮度
                gamma = random.uniform(*brightness_adjust_range)
                # 创建查找表
                invGamma = 1.0 / gamma
                table = np.array([((i / 255.0) ** invGamma) * 255
                                for i in range(256)]).astype(np.uint8)
                image = cv2.LUT(image, table)

                # 创建黑色阴影蒙版
                shadow_mask = np.zeros_like(image, dtype=np.uint8)

                # 随机生成多个圆形阴影区域
                center_x = random.randint(0, width)
                for _ in range(num_circles):
                    # 随机选择圆心位置
                    bias_x = random.randint(round(-width * 0.1), round(width * 0.1))
                    center_y = random.randint(0, height)
                    # 随机半径
                    radius = random.randint(10, max_radius)
                    
                    # 在阴影蒙版上绘制圆形
                    cv2.circle(shadow_mask, (center_x + bias_x, center_y), radius, 255, -1)  # 255 表示白色填充的圆

                # 将阴影叠加到原图像上
                shadow_intensity = random.uniform(*shadow_intensity_range)
                shadow_area = (shadow_mask == 255)
                image_with_shadow = image.copy()
                image_with_shadow[shadow_area] = (image[shadow_area] * (1 - shadow_intensity)).astype(np.uint8)
                # # 可视化
                # cv2.imshow("", image_with_shadow)
                # cv2.waitKey()

                # 保存图像和标签
                saved_image = Path(dst, f"{index}{self.suffix}").absolute()
                cv2.imwrite(saved_image.as_posix(), image_with_shadow)
                new_labels_dict[saved_image] = labels_dict[img]
                index += 1

        self._write_original_label(new_labels_dict, dst_path)
        return dst_path


    def Add_Noise(
            self,
            src_pathes: list[str],
            proportion: float,
            dst_path: str,
            noise_type: dict[str, tuple[float]] = {'poisson': (4.0, 5.0), 'gaussian': (10.0, 15.0), 'halo': (0.1, 0.3)},
            seed: int = 20241114,
        ):
        """
        往图片中添加噪声

        Args:
            src_pathes (list[str]): 源文件路径列表
            proportion (float): 加噪图片占比
            dst_path (str): 目标生成路径
            noise_type: (dict[str, tuple[float]]): 添加的噪声类型及噪声强度范围，可选['poisson', 'gaussian', 'halo']
            seed (int): 随机种子，用于确保随机过程一致

        Returns:
            (str | None): 生成数据位置，失败时返回None
        """
        if dst_path in src_pathes:
            return None

        dst = Path(dst_path) # 目标路径

        # 创建存储路径
        shutil.rmtree(dst, ignore_errors=True)
        dst.mkdir(parents=True, exist_ok=True)

        # 设置随机种子
        random.seed(seed)

        # 获取标签文件（打乱标签顺序，注意提前设置随机种子）
        labels_dict:dict[str, list[LabelPaddleOCR]] = {}
        for src_path in src_pathes:
            labels_dict.update(self._read_original_label(src_path, shuffle=True))
        new_labels_dict:dict[str, list[LabelPaddleOCR]] = {}

        # 计算要添加噪声的图片数量
        proportion = max(0.0, proportion)
        proportion = min(proportion, 1.0)
        num_noisy_imgs = round(len(labels_dict) * proportion)
        noise_list = list(noise_type.keys())
        # 逐个处理文件
        index = 0
        tag = "_".join(noise_list)
        for img in list(labels_dict.keys())[:num_noisy_imgs]:
            # 读取图片
            image = cv2.imread(img, cv2.IMREAD_GRAYSCALE)
            height = image.shape[0]
            width = image.shape[1]

            # 创建噪声图像
            noisy_image = image.copy()

            # 添加光晕
            if 'halo' in noise_list:
                blur_strength = 10
                halo = cv2.GaussianBlur(image, (0, 0), sigmaX=blur_strength, sigmaY=blur_strength)
                alpha = random.uniform(*(noise_type['halo']))
                alpha = min(0.5, alpha)
                alpha = max(0.0, alpha)
                noisy_image = cv2.addWeighted(noisy_image, 1, halo, alpha, 0)
            # 添加高斯噪声
            if 'gaussian' in noise_list:
                noise_level = random.uniform(*(noise_type['gaussian']))
                noise = np.random.normal(0, round(noise_level), image.shape)
                noisy_image = noisy_image + noise
                noisy_image = np.clip(noisy_image, 0, 255).astype(np.uint8)
            # 添加泊松噪声
            if 'poisson' in noise_list:
                sacling_factor = random.uniform(*(noise_type['poisson']))
                scaled_image = noisy_image * sacling_factor
                noisy_image = np.random.poisson(scaled_image).astype(np.float32)
                noisy_image = noisy_image / sacling_factor
                noisy_image = np.clip(noisy_image, 0, 255).astype(np.uint8)

            # 保存图像和标签
            saved_image = Path(dst, f"{tag}_{index}{self.suffix}").absolute()
            cv2.imwrite(saved_image.as_posix(), noisy_image)
            new_labels_dict[saved_image] = labels_dict[img]
            index += 1

        self._write_original_label(new_labels_dict, dst_path)
        return dst_path


class ocrDet(DatasetOCR):
    """
    字符检测数据创建类
    """
    def __init__(
            self,
            label_file: str = "Label.txt",
            suffix: str = ".bmp",
        ) -> None:
        """
        初始化
        """
        super().__init__(label_file, suffix)


    def Resize_Image(
            self,
            src_pathes: list[str],
            size:tuple[int, int],
            dst_path: str,
            keep_ratio: bool = True,
            mode: str = "float_factor",
        ) -> str | None:
        """
        缩放图像

        Args:
            src_pathes (list[str]): 源文件路径列表
            size: (tuple[int, int]): 目标缩放尺寸（H, W）
            dst_path (str): 目标生成路径
            keep_ratio (bool): 保持高度和宽度方向上的缩放系数一致
            mode (str): 缩放模式，可选["float_factor"]

        Returns:
            (str | None): 生成数据位置，失败时返回None
        """
        if dst_path in src_pathes:
            return None

        dst = Path(dst_path) # 目标路径

        # 创建存储路径
        shutil.rmtree(dst, ignore_errors=True)
        dst.mkdir(parents=True, exist_ok=True)

        # 获取标签文件
        labels_dict:dict[str, list[LabelPaddleOCR]] = {}
        for src_path in src_pathes:
            labels_dict.update(self._read_original_label(src_path))
        new_labels_dict:dict[str, list[LabelPaddleOCR]] = {}

        # 逐个处理文件
        index = 0
        for img in labels_dict.keys():
            # 读取图片
            image = cv2.imread(img, cv2.IMREAD_GRAYSCALE)
            img_h = image.shape[0]
            img_w = image.shape[1]

            # 计算缩放系数
            fy:float = 0
            fx:float =0
            if mode == "float_factor":
                tag = "FLT_RESZ"
                fy = img_h / size[0]
                fx = img_w / size[1]
            else:
                print("Unsupport process mode.")
                return None
            
            # 判断是否让高度和宽度方向的缩放系数保持一致
            if keep_ratio == True:
                _f = max(fy, fx)
                fy = _f
                fx = _f

            # 计算缩放后图片的高度和宽度
            new_img_h = int(img_h / fy)
            new_img_w = int(img_w / fx)

            # 缩放图片
            resize_img = cv2.resize(image, (new_img_w, new_img_h), interpolation=cv2.INTER_NEAREST)
            output_img = np.zeros(size, dtype=np.uint8)
            output_img[:resize_img.shape[0],:resize_img.shape[1]] = resize_img[:,:]

            # 转换标签
            new_boxes:list[LabelPaddleOCR] = []
            for box in labels_dict[img]:
                box:LabelPaddleOCR
                for point in box['points']:
                    point[0] = math.floor(point[0] / fx)
                    point[1] = math.floor(point[1] / fy)
                new_boxes.append(box)

            # 保存图片
            dst_img = Path(dst, f"{index}{self.suffix}").absolute().as_posix()
            cv2.imwrite(dst_img, output_img)
            new_labels_dict[dst_img] = new_boxes
            index += 1

        # 保存标签文件
        self._write_original_label(new_labels_dict, dst_path)


    def Crop_Image(
            self,
            src_pathes: list[str],
            size: tuple[int, int],
            dst_path: str,
            seed: int = 20241108,
        ) -> str | None:
        """
        裁剪图像

        Args:
            src_pathes (list[str]): 源文件路径列表
            size: (tuple[int, int]): 目标裁剪尺寸（H, W）
            dst_path (str): 目标生成路径
            seed (int): 随机种子，用于确保随机过程一致

        Returns:
            (str | None): 生成数据位置，失败时返回None
        """
        if dst_path in src_pathes:
            return None

        dst = Path(dst_path) # 目标路径

        # 创建存储路径
        shutil.rmtree(dst, ignore_errors=True)
        dst.mkdir(parents=True, exist_ok=True)

        # 设置随机种子
        random.seed(seed)

        # 获取标签文件
        labels_dict:dict[str, list[LabelPaddleOCR]] = {}
        for src_path in src_pathes:
            labels_dict.update(self._read_original_label(src_path))
        new_labels_dict:dict[str, list[LabelPaddleOCR]] = {}

        # 逐个处理文件
        index = 0
        for img in labels_dict.keys():
            # 读取图片
            image = cv2.imread(img, cv2.IMREAD_GRAYSCALE)
            img_h = image.shape[0]
            img_w = image.shape[1]

            # 图像尺寸小于目标尺寸时不裁剪
            if img_h < size[0] or img_w < size[1]:
                continue

            # 暂时不支持目标个数大于1的图像
            if len(labels_dict[img]) > 1:
                continue
            # 逐个处理目标
            for box in labels_dict[img]:
                # 获取包围框坐标
                xmin = img_w-1
                ymin = img_h-1
                xmax = 0
                ymax = 0
                for point in box['points']:
                    xmin = min(xmin, point[0])
                    ymin = min(ymin, point[1])
                    xmax = max(xmax, point[0])
                    ymax = max(ymax, point[1])
                # 计算包围框尺寸
                bbox_size = (ymax-ymin+1, xmax-xmin+1)
                # 包围框大于裁剪尺寸时不裁剪
                if bbox_size[0] > size[0] or bbox_size[1] > size[1]:
                    continue

                # 计算中心点坐标
                center = [math.floor((xmin+xmax)/2), math.floor((ymin+ymax)/2)]
                # 计算高度和宽度可偏移范围
                h_bias_range = math.floor((size[0] - bbox_size[0]) / 2)
                w_bias_range = math.floor((size[1] - bbox_size[1]) / 2)
                # 获取偏移值
                h_bias = random.randint(-h_bias_range, h_bias_range)
                w_bias = random.randint(-w_bias_range, w_bias_range)
                # 获取裁剪中心
                center[0] += w_bias
                center[1] += h_bias
                
                # 计算裁剪坐标
                crop_xmin = max(0, round(center[0] - (size[1]/2)))
                crop_ymin = max(0, round(center[1] - (size[0]/2)))
                crop_xmax = min(img_w-1, round(crop_xmin+size[1]-1))
                crop_ymax = min(img_h-1, round(crop_ymin+size[0]-1))
                # 裁剪图片
                crop_image = image.copy()[crop_ymin:crop_ymax+1, crop_xmin:crop_xmax+1]
                # 保存图片
                dst_img = Path(dst, f"{index}{self.suffix}").absolute().as_posix()
                cv2.imwrite(dst_img, crop_image)
                # 计算裁剪后的边框坐标
                new_points: list[int] = []
                for point in box['points']:
                    new_points.append([point[0] - crop_xmin, point[1] - crop_ymin])
                new_box = box.copy()
                new_box['points'] = new_points
                # 添加标签
                boxes = new_labels_dict.get(dst_img, [])
                boxes.append(new_box)
                new_labels_dict[dst_img] = boxes
                index += 1

        # 保存标签文件
        self._write_original_label(new_labels_dict, dst_path)

class ocrRec(DatasetOCR):
    """
    字符识别数据创建类
    """
    def __init__(
            self,
            label_file: str = "Label.txt",
            suffix: str = ".bmp",
        ) -> None:
        """
        初始化
        """
        super().__init__(label_file, suffix)


    def _rac_process_single_box(
            self,
            args: tuple,
            ) -> str | None:
        """
        旋转和裁剪图像，处理单个包围框

        Args:
            img (str): 图片路径
            box: (LabelPaddleOCR): 标签信息
            dst_path (str): 目标生成路径
            size (tuple[int, int]): 输出图片尺寸
            index (str): 图片索引
            exe (str): 可执行文件路径

        Returns:
            (str | None): 生成数据位置，失败时返回None
        """
        img, box, dst_path, size, index, exe = args
        content: str = box['transcription'] # 字符内容
        points: list = box['points'] # 四边形四个点坐标
        area = f"{round(points[0][0])},{round(points[0][1])},{round(points[1][0])},{round(points[1][1])},{round(points[2][0])},{round(points[2][1])},{round(points[3][0])},{round(points[3][1])}" # 转换四边形坐标为执行文件可以解析的格式
        out_img_path = Path(dst_path, f"{index}{self.suffix}") # 输出图片路径

        # 执行图片转换
        cmd = f"{exe} --area {area} --image '{img}' --content \"{content}\" --output {out_img_path} --output-size {size[1]}x{size[0]}"
        cmd_rs = subprocess.run(cmd, shell=True, stdout=subprocess.PIPE)
        # 获取输出内容
        bytes = cmd_rs.stdout
        output_content = bytes.decode("utf-8").rstrip('\n')
        if cmd_rs.returncode != 0:
            print(f"转换图片'{img}'失败。")
            print(output_content)
            return None

        return f"{out_img_path.absolute().as_posix()}\t{output_content}"


    def Rotate_And_Crop(
            self,
            src_pathes: list[str],
            size:tuple[int, int],
            dst_path: str,
            num_processes: int = 32,
            modify_func: Callable[[str], str] | None = None,
        ) -> str | None:
        """
        旋转和裁剪图像

        Args:
            src_pathes (list[str]): 源文件路径列表
            size: (tuple[int, int]): 目标缩放尺寸（H, W）
            dst_path (str): 目标生成路径
            num_processes (int): 进程数
            modify_func (Callable[[str], str] | None): 字符编辑函数

        Returns:
            (str | None): 生成数据位置，失败时返回None
        """
        if dst_path in src_pathes:
            return None

        dst = Path(dst_path) # 目标路径
        exe = Path(Path(__file__).parent.absolute(), "bin", "ocr_rec_prep_rotate_and_crop") # 执行文件目录

        # 创建存储路径
        shutil.rmtree(dst, ignore_errors=True)
        dst.mkdir(parents=True, exist_ok=True)

        # 获取标签文件
        labels_dict:dict[str, list[LabelPaddleOCR]] = {}
        for src_path in src_pathes:
            labels_dict.update(self._read_original_label(src_path))
        labels_new:list[str] = []

        # 准备多进程参数
        process_args = []
        index = 0
        for img in labels_dict.keys():
            for box in labels_dict[img]:
                process_args.append((img, box, dst_path, size, index, exe))
                index += 1

        # 使用进程池处理图片
        with Pool(processes=num_processes) as pool:
            # 使用tqdm显示进度
            results = list(tqdm.tqdm(
                pool.imap(self._rac_process_single_box, process_args),
                total=len(process_args),
                desc="Processing images"
            ))

        # 过滤掉None结果并写入标签
        labels_new = [r for r in results if r is not None]
        self._write_rec_label(labels_new, dst, modify_func)

        return dst_path