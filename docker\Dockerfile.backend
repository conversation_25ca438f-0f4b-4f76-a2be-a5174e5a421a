# 使用官方Python 3.11镜像作为基础镜像
FROM python:3.11-slim

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    DEBIAN_FRONTEND=noninteractive

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    wget \
    libpq-dev \
    libgl1-mesa-glx \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libgomp1 \
    libfontconfig1 \
    libgtk-3-0 \
    libgstreamer1.0-0 \
    libgstreamer-plugins-base1.0-0 \
    libxcb-xinerama0 \
    libxcb-cursor0 \
    && rm -rf /var/lib/apt/lists/*

# 安装Python依赖
COPY code-sync/Backend_Django/requirements_docker.txt /app/requirements.txt
RUN pip install --no-cache-dir -i https://pypi.org/simple --trusted-host pypi.org --trusted-host files.pythonhosted.org -r requirements.txt

# 复制项目代码
COPY code-sync/Backend_Django/ /app/

# 安装本地依赖并创建目录
RUN cd /app/libs/proj_ai_roi_det && pip install . && \
    mkdir -p /app/db /app/models /app/media /app/logs && \
    chmod -R 755 /app/db /app/models /app/media /app/logs

# 暴露端口
EXPOSE 8000

# 健康检查 - 增加启动时间和超时时间
HEALTHCHECK --interval=30s --timeout=30s --start-period=120s --retries=5 \
    CMD curl -f http://localhost:8000/api/vision/models/ || exit 1

# 创建启动脚本
COPY scripts/start-backend.sh /app/start-backend.sh
RUN chmod +x /app/start-backend.sh

# 启动命令
CMD ["/app/start-backend.sh"]
