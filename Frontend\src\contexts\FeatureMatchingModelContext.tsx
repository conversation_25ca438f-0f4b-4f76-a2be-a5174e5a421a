import React, { createContext, useState, ReactNode } from 'react';
import { FeatureMatchingModelResult } from '../services/api';

// --- 类型定义 ---

/**
 * @interface FeatureMatchingModelState
 * @description 定义了基于模型的特征匹配功能的状态。
 *
 * @property {FeatureMatchingModelResult | null} matchingResult - 存储后端返回的匹配结果。
 * @property {boolean} isLoading - 标识匹配过程是否正在进行中。
 * @property {(result: FeatureMatchingModelResult | null) => void} setMatchingResult - 更新匹配结果的函数。
 * @property {(loading: boolean) => void} setIsLoading - 设置加载状态的函数。
 */
export interface FeatureMatchingModelState {
  matchingResult: FeatureMatchingModelResult | null;
  isLoading: boolean;
  setMatchingResult: (result: FeatureMatchingModelResult | null) => void;
  setIsLoading: (loading: boolean) => void;
}

// --- 上下文创建 ---

/**
 * @const FeatureMatchingModelContext
 * @description 为基于模型的特征匹配功能创建的 React 上下文。
 *
 * 提供了一个默认状态，其中包含初始的匹配结果 (null)、加载状态 (false)
 * 以及用于更新这些状态的空函数。
 */
export const FeatureMatchingModelContext = createContext<FeatureMatchingModelState>({
  matchingResult: null,
  isLoading: false,
  setMatchingResult: () => {},
  setIsLoading: () => {},
});

// --- 上下文提供者组件 ---

/**
 * @component FeatureMatchingModelProvider
 * @description 一个 React 组件，为应用中的所有子组件提供 FeatureMatchingModelContext。
 *
 * @param {{ children: ReactNode }} props - 组件的 props，应包含子节点。
 * @returns {JSX.Element} - 返回包裹了子组件的上下文提供者。
 *
 * 该组件内部使用 useState 来管理匹配结果和加载状态，
 * 并将这些状态和更新函数通过上下文提供给其所有子组件。
 */
export const FeatureMatchingModelProvider = ({ children }: { children: ReactNode }) => {
  const [matchingResult, setMatchingResult] = useState<FeatureMatchingModelResult | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const contextValue = {
    matchingResult,
    isLoading,
    setMatchingResult,
    setIsLoading,
  };

  return (
    <FeatureMatchingModelContext.Provider value={contextValue}>
      {children}
    </FeatureMatchingModelContext.Provider>
  );
};

/**
 * @hook useFeatureMatchingModel
 * @description 一个自定义 hook，用于在组件中方便地访问 FeatureMatchingModelContext。
 *
 * @returns {FeatureMatchingModelState} - 返回模型匹配上下文的状态。
 * @throws {Error} - 如果在 FeatureMatchingModelProvider 外部使用，则抛出错误。
 */
export const useFeatureMatchingModel = (): FeatureMatchingModelState => {
  const context = React.useContext(FeatureMatchingModelContext);
  if (context === undefined) {
    throw new Error('useFeatureMatchingModel must be used within a FeatureMatchingModelProvider');
  }
  return context;
};