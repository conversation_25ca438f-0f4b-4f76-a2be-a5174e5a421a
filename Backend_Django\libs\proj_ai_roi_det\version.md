# 更新日志

记录更新内容。

## 2025年01月22日

**V1.3.0.5** @TangHL 删除无用README.md文件

- 删除无用的README.md文件

**V1.3.0.4** @TangHL 修改数据集增强脚本、新增数据集分配可视化脚本、模型验证可视化脚本

- 数据集增强、划分脚本位于data_processed.py，主要功能为：
    - 通过调用YOLO.py、以及自定义离线增强方法，进行数据集的离线增强和Labelme到YOLO的格式转换
    - 在分配数据集之后，会进行数据集分配的可视化
- 模型验证可视化脚本位于model_evaluation.py，主要功能为：
    - 通过调用YOLO的推理接口，加载模型和测试集进行验证
    - 在验证之后，会进行模型验证的可视化
- 新增roi.yaml、roi2.yaml文件，用于配置训练、验证、测试数据集的路径，两个文件分别适用与新版数据集划分脚本和旧版数据集划分脚本。

## 2025年01月13日

**V1.3.0.3** @TangHL 修改数据集增强脚本

- 修改data_augmentation.py脚本，删除数据集划分的逻辑，只保留了数据集离线增强的逻辑，模块化代码，可以更好的被其他脚本集成。
- 修改数据集增强脚本data_processed.py，主要功能有：
    - 通过调用YOLO.py和data_augmentation.py的方法，进行数据集的离线增强和Labelme到YOLO的格式转换
    - 新增数据集可视化的功能。
- 新增data_path.yaml文件，用于配置训练、验证、测试数据集的路径。

## 2025年01月10日

**V1.3.0.2** @TangHL 新增脚本，删除多余的模型网络结构配置文件

- 新增脚本data_processed.py，通过调用create_dataset.py、data_augmentation.py的方法，用于分配数据集，并进行离线增强
- 删除多余的模型网络结构配置文件

**V1.3.0.1** @TangHL 恢复原始推送的数据集内容，新增脚本

- 之前新增数据集太多雷同脚本，且命名格式没有按照proj_offline_data_augmentation要求的修改，导致脚本无法对数据集进行区分
- 新增脚本extract_target_label.py，可以匹配JSON文件的字段，用于查找数据集当中需要的数据内容

## 2025年01月09日

**V1.3.0.0** @TangHL 推送整理后的数据集dvc追踪文件

- 新增PDF-417数据集，位于路径：mindeo\data\DS_1D2D_ROI\2D_Others_Industry2、mindeo\data\DS_1D2D_ROI\2D_Others_Industry3、mindeo\data\DS_1D2D_ROI\2D_Others_Life1、mindeo\data\DS_1D2D_ROI\MutilCodes1。

**V1.3.0** @TangHL 新建开发分支。

- 新建分支用于M50-Android平台模型开发：
    - 分支名：dev/tanghl/android_v1.3。
    - 分支用途：开发M50-Android平台的条码检测模型。

## 2024年12月18日

**V1.2.0** @HuangJP 初始化dvc仓库，新建开发分支。

- 初始化主线的dvc仓库，避免切换至主线时出现大量文件未追踪的情况。
- 新建分支用于SSC377平台模型开发：
    - 分支名：dev/huangjp/ssc377_v1.2。
    - 分支用途：开发SSC377平台的条码检测模型。

## 2024年10月22日

**V1.1.0** @ZhouW 新建分支。

- Android模型开发。
- 分支名：dev/zhouw/android_v1.1。
- 分支用途：开发Android平台的条码检测模型。

**V1.0.1** @ZhouW 新建分支。

- SSC377模型开发。
- 分支名：dev/zhouw/ssc377_v1.0。
- 分支用途：开发SSC377平台的条码检测模型。

**V1.0.0** @ZhouW 重置版本号。

**V1.1.1** @HuangJP 支持蒸馏训练。

支持蒸馏训练。
- 描述：
    - 使代码支持单GPU蒸馏训练（备注：暂未支持多GPU蒸馏训练）。
- 方案：
    - 模型训练时，通过model_t参数指定教师模型，以启用蒸馏训练。

## 2024年09月30日

**V1.1.0** @HuangJP 同步ultralytics代码，支持yolo11模型训练。

- 同步社区代码，主要是支持yolo11模型训练、验证和转换。
- 按照“代码提交规范”调整版本号定义，从四位数字版本号调整为三位数字版本号。

## 2024年09月18日

**V1.0.2** @HuangJP 调整更新日志格式。

- 移除"version.txt"。
- 添加"version.md"，启用新格式记录更新内容。

## 2024年09月14日

**V1.0.1** @HuangJP 支持单GPU训练单通道模型。

- 描述：
    - 使代码支持单GPU训练单通道模型（备注：暂未支持多GPU训练单通道模型）。
- 方案：
    - 在网络结构文件（yaml格式）中通过ch参数指定模型通道数。
    - 网络结构文件中指定通道数后，在代码执行时会根据此参数决定是否将“训练”、“验证”等任务的输入修改为单通道数据。
- 修改：
    - 调整"exporter.py"、"predictor.py"、"trainer.py"和"validator.py"，使这些任务支持在单通道模型上运行。
    - 添加"ultralytics/cfg/models/v8/yolov8-gray.yaml"网络结构文件，该文件是单通道模型的示例文件。
    - "yolov8-gray.yaml"中新增了两个模型尺寸，分别为"quark(q)"和"electron(e)"，它们是比"nano(n)"更小的模型。
- 说明：
    - 对于单通道模型，请在模型的网络结构文件中的"nc(number of classes)"参数前面添加"ch:1"参数，用于告诉代码这是一个单通道模型。

**V1.0.0** @HuangJP 初始化仓库内容。

- 拉取"ultralytics"代码。
