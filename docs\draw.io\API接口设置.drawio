<mxfile host="Electron" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/26.2.15 Chrome/134.0.6998.205 Electron/35.2.1 Safari/537.36" version="26.2.15">
  <diagram name="第 1 页" id="GVMtVGNMAnvgv6ok5Wbl">
    <mxGraphModel dx="1722" dy="716" grid="0" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1654" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="50kApEQKkNr_ckermHjL-2" value="认证API" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=32.92307692307692;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=0;marginBottom=0;" vertex="1" parent="1">
          <mxGeometry x="602" y="134" width="203" height="205.9230769230769" as="geometry" />
        </mxCell>
        <mxCell id="50kApEQKkNr_ckermHjL-3" style="line;strokeWidth=1;fillColor=none;align=left;verticalAlign=middle;spacingTop=-1;spacingLeft=3;spacingRight=3;rotatable=0;labelPosition=right;points=[];portConstraint=eastwest;strokeColor=inherit;" vertex="1" parent="50kApEQKkNr_ckermHjL-2">
          <mxGeometry y="32.92307692307692" width="203" height="8" as="geometry" />
        </mxCell>
        <mxCell id="50kApEQKkNr_ckermHjL-4" value="+login()" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="50kApEQKkNr_ckermHjL-2">
          <mxGeometry y="40.92307692307692" width="203" height="33" as="geometry" />
        </mxCell>
        <mxCell id="50kApEQKkNr_ckermHjL-5" value="+refresh()" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="50kApEQKkNr_ckermHjL-2">
          <mxGeometry y="73.92307692307692" width="203" height="33" as="geometry" />
        </mxCell>
        <mxCell id="50kApEQKkNr_ckermHjL-6" value="+register()" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="50kApEQKkNr_ckermHjL-2">
          <mxGeometry y="106.92307692307692" width="203" height="33" as="geometry" />
        </mxCell>
        <mxCell id="50kApEQKkNr_ckermHjL-7" value="+passwordRecovery()" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="50kApEQKkNr_ckermHjL-2">
          <mxGeometry y="139.9230769230769" width="203" height="33" as="geometry" />
        </mxCell>
        <mxCell id="50kApEQKkNr_ckermHjL-8" value="+resetPassword()" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="50kApEQKkNr_ckermHjL-2">
          <mxGeometry y="172.9230769230769" width="203" height="33" as="geometry" />
        </mxCell>
        <mxCell id="50kApEQKkNr_ckermHjL-9" value="用户管理API" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=34.54545454545455;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=0;marginBottom=0;" vertex="1" parent="1">
          <mxGeometry x="312" y="430" width="222" height="182.54545454545456" as="geometry" />
        </mxCell>
        <mxCell id="50kApEQKkNr_ckermHjL-10" style="line;strokeWidth=1;fillColor=none;align=left;verticalAlign=middle;spacingTop=-1;spacingLeft=3;spacingRight=3;rotatable=0;labelPosition=right;points=[];portConstraint=eastwest;strokeColor=inherit;" vertex="1" parent="50kApEQKkNr_ckermHjL-9">
          <mxGeometry y="34.54545454545455" width="222" height="8" as="geometry" />
        </mxCell>
        <mxCell id="50kApEQKkNr_ckermHjL-11" value="+getCurrentUser()" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="50kApEQKkNr_ckermHjL-9">
          <mxGeometry y="42.54545454545455" width="222" height="35" as="geometry" />
        </mxCell>
        <mxCell id="50kApEQKkNr_ckermHjL-12" value="+getAllUsers()" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="50kApEQKkNr_ckermHjL-9">
          <mxGeometry y="77.54545454545455" width="222" height="35" as="geometry" />
        </mxCell>
        <mxCell id="50kApEQKkNr_ckermHjL-13" value="+updateUser(user_id)" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="50kApEQKkNr_ckermHjL-9">
          <mxGeometry y="112.54545454545455" width="222" height="35" as="geometry" />
        </mxCell>
        <mxCell id="50kApEQKkNr_ckermHjL-14" value="+deleteUser(user_id)" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="50kApEQKkNr_ckermHjL-9">
          <mxGeometry y="147.54545454545456" width="222" height="35" as="geometry" />
        </mxCell>
        <mxCell id="50kApEQKkNr_ckermHjL-15" value="权限API" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=31.733333333333334;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=0;marginBottom=0;" vertex="1" parent="1">
          <mxGeometry x="584" y="406" width="239" height="231.73333333333335" as="geometry" />
        </mxCell>
        <mxCell id="50kApEQKkNr_ckermHjL-16" style="line;strokeWidth=1;fillColor=none;align=left;verticalAlign=middle;spacingTop=-1;spacingLeft=3;spacingRight=3;rotatable=0;labelPosition=right;points=[];portConstraint=eastwest;strokeColor=inherit;" vertex="1" parent="50kApEQKkNr_ckermHjL-15">
          <mxGeometry y="31.733333333333334" width="239" height="8" as="geometry" />
        </mxCell>
        <mxCell id="50kApEQKkNr_ckermHjL-17" value="+getUserPermissions()" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="50kApEQKkNr_ckermHjL-15">
          <mxGeometry y="39.733333333333334" width="239" height="32" as="geometry" />
        </mxCell>
        <mxCell id="50kApEQKkNr_ckermHjL-18" value="+getAllGroups()" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="50kApEQKkNr_ckermHjL-15">
          <mxGeometry y="71.73333333333333" width="239" height="32" as="geometry" />
        </mxCell>
        <mxCell id="50kApEQKkNr_ckermHjL-19" value="+createGroup()" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="50kApEQKkNr_ckermHjL-15">
          <mxGeometry y="103.73333333333333" width="239" height="32" as="geometry" />
        </mxCell>
        <mxCell id="50kApEQKkNr_ckermHjL-20" value="+updateGroup(group_id)" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="50kApEQKkNr_ckermHjL-15">
          <mxGeometry y="135.73333333333335" width="239" height="32" as="geometry" />
        </mxCell>
        <mxCell id="50kApEQKkNr_ckermHjL-21" value="+deleteGroup(group_id)" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="50kApEQKkNr_ckermHjL-15">
          <mxGeometry y="167.73333333333335" width="239" height="32" as="geometry" />
        </mxCell>
        <mxCell id="50kApEQKkNr_ckermHjL-22" value="+getGroupUsers(group_id)" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="50kApEQKkNr_ckermHjL-15">
          <mxGeometry y="199.73333333333335" width="239" height="32" as="geometry" />
        </mxCell>
        <mxCell id="50kApEQKkNr_ckermHjL-23" value="图像处理API" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=32.92307692307692;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=0;marginBottom=0;" vertex="1" parent="1">
          <mxGeometry x="873" y="418" width="253" height="205.9230769230769" as="geometry" />
        </mxCell>
        <mxCell id="50kApEQKkNr_ckermHjL-24" style="line;strokeWidth=1;fillColor=none;align=left;verticalAlign=middle;spacingTop=-1;spacingLeft=3;spacingRight=3;rotatable=0;labelPosition=right;points=[];portConstraint=eastwest;strokeColor=inherit;" vertex="1" parent="50kApEQKkNr_ckermHjL-23">
          <mxGeometry y="32.92307692307692" width="253" height="8" as="geometry" />
        </mxCell>
        <mxCell id="50kApEQKkNr_ckermHjL-25" value="+detect() : 待实现" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="50kApEQKkNr_ckermHjL-23">
          <mxGeometry y="40.92307692307692" width="253" height="33" as="geometry" />
        </mxCell>
        <mxCell id="50kApEQKkNr_ckermHjL-26" value="+segment() : 待实现" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="50kApEQKkNr_ckermHjL-23">
          <mxGeometry y="73.92307692307692" width="253" height="33" as="geometry" />
        </mxCell>
        <mxCell id="50kApEQKkNr_ckermHjL-27" value="+pose() : 待实现" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="50kApEQKkNr_ckermHjL-23">
          <mxGeometry y="106.92307692307692" width="253" height="33" as="geometry" />
        </mxCell>
        <mxCell id="50kApEQKkNr_ckermHjL-28" value="+createDataset() : 待实现" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="50kApEQKkNr_ckermHjL-23">
          <mxGeometry y="139.9230769230769" width="253" height="33" as="geometry" />
        </mxCell>
        <mxCell id="50kApEQKkNr_ckermHjL-29" value="+getModels() : 待实现" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="50kApEQKkNr_ckermHjL-23">
          <mxGeometry y="172.9230769230769" width="253" height="33" as="geometry" />
        </mxCell>
        <mxCell id="50kApEQKkNr_ckermHjL-30" value="" style="curved=1;startArrow=block;startSize=16;startFill=0;endArrow=none;exitX=0;exitY=0.72;entryX=0.5;entryY=0;rounded=0;" edge="1" parent="1" source="50kApEQKkNr_ckermHjL-2" target="50kApEQKkNr_ckermHjL-9">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="423" y="381" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="50kApEQKkNr_ckermHjL-31" value="" style="curved=1;startArrow=block;startSize=16;startFill=0;endArrow=none;exitX=0.5;exitY=1;entryX=0.5;entryY=0;rounded=0;" edge="1" parent="1" source="50kApEQKkNr_ckermHjL-2" target="50kApEQKkNr_ckermHjL-15">
          <mxGeometry relative="1" as="geometry">
            <Array as="points" />
          </mxGeometry>
        </mxCell>
        <mxCell id="50kApEQKkNr_ckermHjL-32" value="" style="curved=1;startArrow=block;startSize=16;startFill=0;endArrow=none;exitX=1;exitY=0.71;entryX=0.5;entryY=0;rounded=0;" edge="1" parent="1" source="50kApEQKkNr_ckermHjL-2" target="50kApEQKkNr_ckermHjL-23">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="999" y="381" />
            </Array>
          </mxGeometry>
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
