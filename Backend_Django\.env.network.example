# 网络配置环境变量示例
# 复制此文件为 .env.network 并根据实际情况修改
# 包含所有可能的配置选项，主要用作参考

# =============================================================================
# 端口配置
# =============================================================================

# 前端端口配置
FRONTEND_DEV_PORT=5173          # 前端开发服务器端口
FRONTEND_PROD_PORT=8080         # 前端生产环境端口
FRONTEND_PREVIEW_PORT=4173      # 前端预览端口

# 后端端口配置
BACKEND_PORT=8000               # Django后端端口
WEBSOCKET_PORT=8001             # WebSocket端口（预留）

# =============================================================================
# 主机和IP配置
# =============================================================================

# 允许的主机列表（逗号分隔）
# 开发环境示例
ALLOWED_HOSTS=localhost,127.0.0.1,*

# 生产环境示例（取消注释并修改）
# ALLOWED_HOSTS=yourdomain.com,www.yourdomain.com,*************

# 局域网IP地址列表（逗号分隔）
# 用于自动生成CORS源地址
LAN_IPS=*************,*************

# =============================================================================
# CORS配置
# =============================================================================

# CORS允许的源地址列表（逗号分隔）
# 如果设置了此变量，将覆盖自动生成的CORS源地址
# CORS_ORIGINS=http://localhost:5173,http://localhost:8080,http://*************:8080

# 是否允许所有源（仅开发环境使用）
CORS_ALLOW_ALL_ORIGINS=False

# =============================================================================
# WebSocket配置
# =============================================================================

# WebSocket路径配置
WEBSOCKET_PATH=/ws/
WEBSOCKET_SCANNER_PATH=/ws/scanner/

# WebSocket连接配置
WEBSOCKET_CAPACITY=300          # 连接容量
WEBSOCKET_EXPIRY=1              # 消息过期时间（秒）

# =============================================================================
# 扫码枪设备配置
# =============================================================================

# 是否启用扫码枪功能
SCANNER_ENABLED=true

# 扫码枪默认IP地址
SCANNER_DEFAULT_IP=************

# 扫码枪默认端口
SCANNER_DEFAULT_PORT=9999

# 扫码枪连接超时时间（秒）
SCANNER_TIMEOUT=30

# 扫码枪图像质量设置
SCANNER_IMAGE_QUALITY=high

# 扫码枪帧率设置（fps）
SCANNER_FPS=30

# 扫码枪重连间隔（秒）
SCANNER_RECONNECT_INTERVAL=5

# 扫码枪最大重连次数
SCANNER_MAX_RECONNECT_ATTEMPTS=3

# =============================================================================
# 环境特定配置
# =============================================================================

# 前端主机配置
FRONTEND_DEV_HOST=localhost     # 开发环境前端主机
FRONTEND_PROD_HOST=localhost    # 生产环境前端主机
FRONTEND_DOCKER_HOST=localhost  # Docker环境前端主机

# 后端主机配置
BACKEND_HOST=localhost          # 后端主机
BACKEND_DOCKER_HOST=backend     # Docker环境后端主机（容器名）

# 是否使用HTTPS
USE_HTTPS=false

# =============================================================================
# 使用说明
# =============================================================================

# 1. 本地开发环境
#    - 使用默认配置即可
#    - 前端: http://localhost:5173
#    - 后端: http://localhost:8000

# 2. 局域网访问
#    - 设置 LAN_IPS 为你的局域网IP
#    - 前端: http://你的IP:8080
#    - 后端: http://你的IP:8000

# 3. Docker环境
#    - 设置 BACKEND_DOCKER_HOST=backend
#    - 设置 CORS_ALLOW_ALL_ORIGINS=True

# 4. 生产环境
#    - 设置具体的 ALLOWED_HOSTS
#    - 设置具体的 CORS_ORIGINS
#    - 设置 USE_HTTPS=true
#    - 设置 CORS_ALLOW_ALL_ORIGINS=False

# =============================================================================
# 常用配置组合
# =============================================================================

# 开发环境（默认）
# FRONTEND_DEV_PORT=5173
# BACKEND_PORT=8000
# ALLOWED_HOSTS=*
# CORS_ALLOW_ALL_ORIGINS=False

# 局域网开发
# LAN_IPS=*************
# FRONTEND_PROD_PORT=8080
# BACKEND_PORT=8000

# Docker热重载
# BACKEND_DOCKER_HOST=backend
# CORS_ALLOW_ALL_ORIGINS=True
# FRONTEND_DOCKER_HOST=localhost

# 生产部署
# ALLOWED_HOSTS=yourdomain.com
# CORS_ORIGINS=https://yourdomain.com
# USE_HTTPS=true
# CORS_ALLOW_ALL_ORIGINS=False
