# 局域网访问配置示例
# 适用于：需要其他设备访问前端应用

# =============================================================================
# 网络配置
# =============================================================================

# API基础URL - 开发环境通过Vite代理
VITE_API_BASE_URL=/api

# 后端配置 (修改为您的实际IP地址)
VITE_BACKEND_HOST=**************
VITE_BACKEND_PORT=9000
VITE_BACKEND_URL=http://**************:9000

# 前端配置 (修改为您的实际IP地址)
VITE_FRONTEND_HOST=**************
VITE_FRONTEND_PORT=5173

# 环境配置
VITE_ENVIRONMENT=development
VITE_USE_HTTPS=false

# =============================================================================
# 使用方法
# =============================================================================

# 1. 复制此文件为 .env.local
#    cp .env.lan.example .env.local

# 2. 修改IP地址为您的实际局域网IP
#    - 查看IP: ipconfig (Windows) 或 ifconfig (Linux/Mac)
#    - 修改上面的 ************** 为您的IP

# 3. 确保后端也配置了相同的IP
#    - 后端 .env 文件中设置 LAN_IPS=您的IP

# 4. 启动开发服务器
#    npm run dev

# 5. 局域网访问地址
#    - 前端: http://您的IP:5173
#    - 后端: http://您的IP:9000

# =============================================================================
# 故障排除
# =============================================================================

# 1. 无法访问前端
#    - 检查防火墙设置
#    - 确保端口5173未被占用
#    - 验证IP地址配置正确

# 2. API请求失败
#    - 检查后端是否运行在配置的端口
#    - 验证后端CORS配置包含前端IP
#    - 检查网络连接

# 3. 端口被占用
#    - 修改 VITE_FRONTEND_PORT 为其他端口
#    - 修改 VITE_BACKEND_PORT 为其他端口
#    - 确保前后端端口配置一致
