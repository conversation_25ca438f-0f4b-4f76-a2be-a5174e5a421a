# AI Vision App - Docker 部署文档

本目录包含 AI Vision App 的完整 Docker 部署解决方案，支持标准生产部署和开发热重载模式。

---

## 📁 目录结构

```
docker/
├── 📄 Docker 配置文件
│   ├── docker-compose.*.yml            # 多种部署配置
│   ├── Dockerfile.*                    # 镜像构建文件
│   ├── nginx*.conf                     # Nginx 配置文件
│   └── *.json, *.conf                  # 环境配置文件
├── 🛠️ 管理脚本
│   └── scripts/                        # 完整的脚本工具集
│       ├── build-images.ps1            # 镜像构建
│       ├── deploy-hotreload.ps1        # 热重载服务管理
│       ├── enable-auto-start.ps1       # 容器自动启动配置 ⭐
│       ├── export-images.ps1           # 镜像导出
│       ├── import-images.ps1           # 镜像导入
│       ├── package-for-server.ps1      # 服务器部署打包
│       ├── start-container.ps1         # 标准容器启动
│       ├── stop-container.ps1          # 容器停止
│       ├── setup-code-sharing-simple.ps1 # 网络共享设置
│       ├── sync-from-dev.ps1           # 代码同步工具
│       ├── setup-firewall.bat          # 防火墙配置
│       ├── start-backend.sh            # 后端启动脚本
│       ├── health.json                 # 健康检查配置
│       └── check/                      # 诊断工具
│           ├── check-docker-env.ps1    # Docker环境检查
│           ├── check-lan-access.ps1    # 局域网访问检查
│           └── check-network.ps1       # 网络配置检查
├── 📚 文档目录
│   └── docs/                           # 详细文档和指南
├── 💾 数据目录
│   └── data/                           # 持久化数据存储
└── 🔄 热重载目录
    └── code-sync/                      # 热重载代码同步
```

---

## 🚀 快速开始

### 🎯 **选择部署方式**

#### 方式一：标准生产部署
**适用场景**: 生产环境、演示环境、稳定测试

```powershell
# 1. 构建 Docker 镜像
.\scripts\build-images.ps1

# 2. 启动标准服务
.\scripts\start-container.ps1

# 3. 访问应用
# 前端: http://localhost:8080 或 http://你的IP:8080
# 后端: http://localhost:8000 或 http://你的IP:8000
```

#### 方式二：热重载部署 (推荐)
**适用场景**: 开发环境、代码同步、远程开发

```powershell
# 1. 构建 Docker 镜像
.\scripts\build-images.ps1

# 2. 启动热重载服务
.\scripts\deploy-hotreload.ps1 -Mode start

# 3. 访问应用
# 前端: http://localhost:8080 或 http://你的IP:8080
# 后端: http://localhost:8000 或 http://你的IP:8000
```

#### 方式三：跨电脑同步开发
**适用场景**: A电脑开发，B电脑部署，实时同步

```powershell
# A电脑（开发机）设置
.\scripts\setup-code-sharing-simple.ps1 setup

# B电脑（部署机）同步
.\scripts\sync-from-dev.ps1 -DevMachineIP "A电脑IP" -Mode watch
```

#### 方式四：容器自动启动设置
**适用场景**: 生产服务器、无人值守部署

```powershell
# 设置容器开机自动启动
.\scripts\enable-auto-start.ps1

# 验证设置
docker inspect ai-vision-backend-hotreload --format '{{.HostConfig.RestartPolicy.Name}}'
docker inspect ai-vision-frontend-hotreload --format '{{.HostConfig.RestartPolicy.Name}}'
```

---

## 🌐 访问地址

### 标准部署访问地址
- **前端应用**: http://localhost:8080 或 http://你的IP:8080
- **后端API**: http://localhost:8000 或 http://你的IP:8000
- **API模型列表**: http://localhost:8000/api/vision/models/
- **健康检查**: http://localhost:8080/health

### 热重载部署访问地址
- **前端应用**: http://localhost:8080 或 http://你的IP:8080
- **后端API**: 通过nginx代理访问 http://localhost:8080/api/
- **直接后端**: http://localhost:8000 (仅调试用)

### 局域网访问
脚本会自动检测并显示局域网IP，其他设备可通过以下方式访问：
- **手机/平板**: http://192.168.1.xxx:8080
- **其他电脑**: http://192.168.1.xxx:8080

---

## 📚 详细文档

- **[脚本功能详解](./docs/scripts-guide.md)** - 所有脚本的详细使用说明
- **[Docker配置说明](./docs/docker-configs.md)** - Docker配置文件详解
- **[部署指南](./docs/deployment-guide.md)** - 完整的部署工作流指南
- **[故障排除](./docs/troubleshooting.md)** - 常见问题和解决方案
- **[网络访问配置](./docs/network-access.md)** - 网络配置和局域网访问

---

## 📊 系统要求

### 💻 最低要求
- **操作系统**: Windows 10+ / macOS 10.14+ / Ubuntu 18.04+
- **Docker**: Docker Desktop 4.0+
- **内存**: 4GB+ 可用内存
- **磁盘**: 20GB+ 可用空间
- **网络**: 稳定的互联网连接（首次构建）

### 🚀 推荐配置
- **内存**: 8GB+ 可用内存
- **磁盘**: 50GB+ 可用空间（包含AI模型文件）
- **CPU**: 4核心以上（AI推理性能更佳）
- **网络**: 千兆局域网（多设备访问）

---

## 🆘 获取帮助

如果遇到问题，请按以下顺序排查：

1. **查看日志**: `docker-compose logs -f`
2. **运行诊断**: `.\scripts\check\check-docker-env.ps1`
3. **检查文档**: 查看相关详细文档
4. **重新构建**: `.\scripts\build-images.ps1 -NoCache`
5. **清理重启**: `docker system prune -a` 然后重新部署

---

**🎉 恭喜！您已成功配置 AI Vision App 的 Docker 部署环境！**
