from user_utils.proj_offline_data_augmentation.YOLO import ObjectDetectionYOLO
import os
from pathlib import Path
import shutil
from tqdm import tqdm
import json
from collections import defaultdict
import matplotlib.pyplot as plt
import numpy as np
from imgaug import augmenters as iaa
from imgaug.augmentables.bbs import BoundingBox, BoundingBoxesOnImage
import cv2

def process_dataset(data_sources, output_dir, train_ratio=0.8, val_ratio=0.1):
    """处理数据集：划分数据集并转换为YOLO格式
    
    Args:
        data_sources: 数据源路径字典
        output_dir: 输出根目录路径
        train_ratio: 训练集比例
        val_ratio: 验证集比例
    """
    # 创建目录结构
    dataset_dir = Path(output_dir, "DataSet")
    dataset_aug_dir = Path(output_dir, "DataSet_Augmented")
    dataset_yolo_dir = Path(output_dir, "DataSet_YOLO")
    
    for dir_path in [dataset_dir, dataset_aug_dir, dataset_yolo_dir]:
        if dir_path.exists():
            shutil.rmtree(dir_path)
        dir_path.mkdir(parents=True, exist_ok=True)
    
    # 初始化YOLO对象
    yolo = ObjectDetectionYOLO(["1D", "2D_QR", "2D_DM", "2D_others"])
    
    # 分配数据集
    print("正在分配数据集...")
    train_path, val_path, test_path = yolo.Distribute_Data_Set(
        data_sources, train_ratio, val_ratio, 
        str(dataset_dir), only_label=False, seed=20241004
    )
    
    # 可视化数据集分布
    visualize_dataset_distribution(
        data_sources,
        str(train_path),
        str(val_path),
        str(test_path),
        str(dataset_dir)
    )
    
    # 数据增强处理
    print("\n进行数据增强...")
    process_augmentation(dataset_dir, dataset_aug_dir)
    
    # 转换为YOLO格式
    print("\n转换为YOLO格式...")
    for split in ['train', 'val', 'test']:
        source_dirs = [str(dataset_aug_dir / split)]
        yolo.Labelme_To_YOLO(source_dirs, str(dataset_yolo_dir / split))

def visualize_dataset_distribution(data_sources: dict, train_path: str, val_path: str, test_path: str, output_dir: str):
    """可视化数据集分布情况"""
    # 初始化统计字典
    stats = defaultdict(lambda: {"total": 0, "train": 0, "val": 0, "test": 0})
    
    # 获取训练集、验证集、测试集的图像路径
    train_files = get_image_annotation_pairs(train_path)
    val_files = get_image_annotation_pairs(val_path)
    test_files = get_image_annotation_pairs(test_path)
    
    print(f"找到训练集图像：{len(train_files)}张")
    print(f"找到验证集图像：{len(val_files)}张")
    print(f"找到测试集图像：{len(test_files)}张")
    
    # 获取工作目录的绝对路径
    workspace_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
    
    # 规范化源路径（转换为绝对路径）
    normalized_sources = {}
    for src in data_sources.keys():
        abs_path = os.path.normpath(os.path.join(workspace_dir, src))
        normalized_sources[abs_path] = src
    
    # 统计每个源路径的分配情况
    def count_distribution(files, dataset_type):
        for img_path, json_path in tqdm(files, desc=f"{dataset_type}集统计"):
            try:
                with open(json_path, 'r', encoding='utf-8') as f:
                    annotation = json.load(f)
                    if 'srcPath' in annotation:
                        src_path = os.path.normpath(str(Path(annotation['srcPath']).parent))
                        for norm_src in normalized_sources:
                            if os.path.basename(norm_src) == os.path.basename(src_path):
                                orig_src = normalized_sources[norm_src]
                                stats[orig_src][dataset_type] += 1
                                stats[orig_src]["total"] += 1
                                break
            except Exception as e:
                print(f"\n处理文件时出错 {json_path}: {str(e)}")
                continue
    
    print("\n开始统计数据分布...")
    count_distribution(train_files, "train")
    count_distribution(val_files, "val")
    count_distribution(test_files, "test")
    
    # 准备绘图数据
    sources = []
    train_counts = []
    val_counts = []
    test_counts = []
    total_counts = []
    
    for src_path, counts in stats.items():
        if counts["total"] == 0:
            continue
        sources.append(os.path.basename(src_path))
        train_counts.append(counts["train"])
        val_counts.append(counts["val"])
        test_counts.append(counts["test"])
        total_counts.append(counts["total"])
    
    # 创建图表
    plt.figure(figsize=(20, 15))
    
    # 1. 绘制分组柱状图
    ax1 = plt.subplot(211)
    width = 0.25
    x = np.arange(len(sources))
    
    train_bars = ax1.bar(x - width, train_counts, width, label='Train', color='#2ecc71')
    val_bars = ax1.bar(x, val_counts, width, label='Val', color='#3498db')
    test_bars = ax1.bar(x + width, test_counts, width, label='Test', color='#e74c3c')
    
    # 添加数值标签
    def add_value_labels(bars):
        for bar in bars:
            height = bar.get_height()
            if height > 0:  # 只显示非零值
                ax1.text(bar.get_x() + bar.get_width()/2, height,
                        f'{int(height)}', 
                        ha='center', va='bottom', 
                        fontsize=10)
    
    add_value_labels(train_bars)
    add_value_labels(val_bars)
    add_value_labels(test_bars)
    
    ax1.set_title('Dataset Distribution', fontsize=14, pad=20)
    ax1.set_xticks(x)
    ax1.set_xticklabels(sources, rotation=45, ha='right')
    ax1.legend(fontsize=12)
    ax1.set_ylabel('Number of Images', fontsize=12)
    
    # 添加网格线提高可读性
    ax1.grid(True, axis='y', linestyle='--', alpha=0.3)
    
    # 调整y轴范围，给标签留出空间
    ax1.set_ylim(0, max(max(train_counts), max(val_counts), max(test_counts)) * 1.15)
    
    # 2. 绘制饼图
    ax2 = plt.subplot(212)
    total = sum(total_counts)
    if total > 0:
        sizes = [sum(train_counts)/total*100, sum(val_counts)/total*100, sum(test_counts)/total*100]
        labels = [f'Train\n{sum(train_counts)} ({sizes[0]:.1f}%)', 
                 f'Val\n{sum(val_counts)} ({sizes[1]:.1f}%)', 
                 f'Test\n{sum(test_counts)} ({sizes[2]:.1f}%)']
        colors = ['#2ecc71', '#3498db', '#e74c3c']
        
        ax2.pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)
        ax2.axis('equal')
        ax2.set_title('Overall Distribution', fontsize=14, pad=20)
        
        plt.tight_layout(pad=3.0)
        plt.savefig(os.path.join(output_dir, 'dataset_distribution.png'), dpi=300, bbox_inches='tight')
        plt.close()

def get_image_annotation_pairs(directory: str) -> list:
    """获取目录下的所有图像和对应的标注文件"""
    pairs = []
    for root, _, files in os.walk(directory):
        for file in files:
            if file.endswith(('.jpg', '.png', '.bmp')):
                image_path = os.path.join(root, file)
                json_path = os.path.splitext(image_path)[0] + '.json'
                if os.path.exists(json_path):
                    pairs.append((image_path, json_path))
    return pairs

def process_augmentation(dataset_dir: Path, aug_dir: Path):
    """使用imgaug进行数据增强"""
    # resize变换 - 所有图像必须先进行这个处理
    resize_aug = iaa.Resize({"height": 320, "width": 320})
    
    # 基础变换序列（灰度）- 用于所有数据集
    base_aug = iaa.Sequential([
        iaa.Grayscale(alpha=1.0)
    ])
    
    # 训练集额外增强序列
    train_extra_augs = [
        # 反色处理
        iaa.Invert(1.0),
        
        # 添加反光效果
        iaa.Sequential([
            iaa.Add((-10, 10), per_channel=0.5),
            iaa.GammaContrast((0.8, 1.2)),
            iaa.Add((20, 40)),
            iaa.BlendAlphaRegularGrid(
                nb_rows=(4, 8), nb_cols=(4, 8),
                foreground=iaa.Add((30, 70)),
                background=iaa.Add((0, 20))
            )
        ]),
        
        # 添加噪点效果
        iaa.Sequential([
            # 高斯噪声
            iaa.AdditiveGaussianNoise(
                loc=0,                 # 均值
                scale=(0.01*255, 0.03*255),  # 标准差
                per_channel=0.5       # 50%的概率对每个通道独立添加噪声
            ),
            # 椒盐噪声
            iaa.SaltAndPepper(
                p=(0.01, 0.03)        # 1-3%的像素被替换为黑白点
            ),
            # 泊松噪声
            iaa.AdditivePoissonNoise(
                lam=(0, 5),           # 泊松分布参数
                per_channel=True
            )
        ])
    ]

    def process_single_image(img_path, json_path, output_dir, extra_augmenters=None):
        """处理单张图像及其标注"""
        # 读取图像
        img = cv2.imread(str(img_path))
        img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
        
        # 读取JSON标注
        with open(json_path, 'r', encoding='utf-8') as f:
            anno = json.load(f)
        
        # 获取原始图像尺寸
        orig_height, orig_width = img.shape[:2]
        
        # 转换标注为imgaug格式
        bbs = []
        for shape in anno['shapes']:
            if shape['shape_type'] == 'rectangle':
                points = shape['points']
                x1, y1 = points[0]
                x2, y2 = points[1]
                bbs.append(BoundingBox(
                    x1=min(x1, x2),
                    y1=min(y1, y2),
                    x2=max(x1, x2),
                    y2=max(y1, y2),
                    label=shape['label']
                ))
        
        bbs = BoundingBoxesOnImage(bbs, shape=img.shape)
        
        # 1. 首先进行resize
        img_resized, bbs_resized = resize_aug(image=img, bounding_boxes=bbs)
        
        # 2. 应用基础增强（灰度）
        img_base, bbs_base = base_aug(image=img_resized, bounding_boxes=bbs_resized)
        
        # 保存基础处理后的图像
        aug_img_path = output_dir / f"{img_path.stem}.jpg"
        aug_json_path = output_dir / f"{img_path.stem}.json"
        
        # 保存基础处理图像
        img_base_bgr = cv2.cvtColor(img_base, cv2.COLOR_RGB2BGR)
        cv2.imwrite(str(aug_img_path), img_base_bgr)
        
        # 更新并保存基础处理JSON
        new_anno = anno.copy()
        new_anno['imagePath'] = aug_img_path.name
        new_anno['imageData'] = None
        new_anno['imageHeight'] = 320  # 更新图像尺寸信息
        new_anno['imageWidth'] = 320
        new_anno['shapes'] = []
        
        # 更新边界框坐标
        for bb in bbs_base.bounding_boxes:
            # 确保坐标在图像范围内
            x1 = max(0, min(320, float(bb.x1)))
            y1 = max(0, min(320, float(bb.y1)))
            x2 = max(0, min(320, float(bb.x2)))
            y2 = max(0, min(320, float(bb.y2)))
            
            new_anno['shapes'].append({
                'label': bb.label,
                'points': [[x1, y1], [x2, y2]],
                'group_id': None,
                'shape_type': 'rectangle',
                'flags': {}
            })
        
        with open(aug_json_path, 'w', encoding='utf-8') as f:
            json.dump(new_anno, f, indent=2)
        
        # 3. 如果有额外的增强器（训练集），继续处理
        if extra_augmenters:
            for idx, augmenter in enumerate(extra_augmenters):
                img_aug, bbs_aug = augmenter(image=img_base, bounding_boxes=bbs_base)
                
                # 保存额外增强的图像
                aug_img_path = output_dir / f"{img_path.stem}_aug_{idx}.jpg"
                aug_json_path = output_dir / f"{img_path.stem}_aug_{idx}.json"
                
                img_aug_bgr = cv2.cvtColor(img_aug, cv2.COLOR_RGB2BGR)
                cv2.imwrite(str(aug_img_path), img_aug_bgr)
                
                # 更新JSON标注
                new_anno = anno.copy()
                new_anno['imagePath'] = aug_img_path.name
                new_anno['imageData'] = None
                new_anno['imageHeight'] = 320
                new_anno['imageWidth'] = 320
                new_anno['shapes'] = []
                
                # 更新边界框坐标
                for bb in bbs_aug.bounding_boxes:
                    # 确保坐标在图像范围内
                    x1 = max(0, min(320, float(bb.x1)))
                    y1 = max(0, min(320, float(bb.y1)))
                    x2 = max(0, min(320, float(bb.x2)))
                    y2 = max(0, min(320, float(bb.y2)))
                    
                    new_anno['shapes'].append({
                        'label': bb.label,
                        'points': [[x1, y1], [x2, y2]],
                        'group_id': None,
                        'shape_type': 'rectangle',
                        'flags': {}
                    })
                
                with open(aug_json_path, 'w', encoding='utf-8') as f:
                    json.dump(new_anno, f, indent=2)

    # 处理每个数据集分割
    for split in ['train', 'val', 'test']:
        print(f"\n处理{split}集...")
        split_dir = dataset_dir / split
        aug_split_dir = aug_dir / split
        aug_split_dir.mkdir(parents=True, exist_ok=True)
        
        # 获取图像和标注对
        pairs = get_image_annotation_pairs(str(split_dir))
        
        # 处理图像
        for img_path, json_path in tqdm(pairs, desc=f"处理{split}集"):
            # 对于训练集，使用额外的增强
            extra_augs = train_extra_augs if split == 'train' else None
            process_single_image(
                Path(img_path), 
                Path(json_path), 
                aug_split_dir, 
                extra_augs
            )

if __name__ == "__main__":
    # 数据集路径和权重配置
    data_sources = {
        "../mindeo/data/DS_1D2D_ROI/1D_Goods" : 1.0,
        "../mindeo/data/DS_1D2D_ROI/1D_Industry" : 1.0,
        "../mindeo/data/DS_1D2D_ROI/2D_DM_Industry" : 1.0,
        "../mindeo/data/DS_1D2D_ROI/2D_DM_Life" : 1.0,
        "../mindeo/data/DS_1D2D_ROI/2D_DM_Small" : 1.0,
        "../mindeo/data/DS_1D2D_ROI/2D_Others_Industry1" : 1.0,
        "../mindeo/data/DS_1D2D_ROI/2D_Others_Industry2" : 1.0,     
        "../mindeo/data/DS_1D2D_ROI/2D_Others_Industry3" : 1.0,     # PDF417
        "../mindeo/data/DS_1D2D_ROI/2D_Others_Life1" : 1.0,
        "../mindeo/data/DS_1D2D_ROI/2D_QR_Industry" : 1.0,
        "../mindeo/data/DS_1D2D_ROI/2D_QR_Large" : 1.0,
        "../mindeo/data/DS_1D2D_ROI/2D_QR_Life" : 1.0,
        "../mindeo/data/DS_1D2D_ROI/Merged_Barcode" : 1.0,          # 新增的拼接条码图像（1D单独拼接+1D、2D_DM合并拼接）
        "../mindeo/data/DS_1D2D_ROI/MutilCodes" : 1.0,
        "../mindeo/data/DS_1D2D_ROI/MutilCodes1" : 1.0,             # PDF417
    }
    
    # 处理数据集
    process_dataset(data_sources, "../mindeo/data_after_processed")