"""
文件处理服务

处理文件上传、验证、临时文件管理等功能。

完整的FileService架构：

class FileService(BaseService):
    # ==================== 文件验证模块 ====================
    def validate_image_file()              # 验证图像文件格式和大小
    def validate_model_file()              # 验证模型文件格式和大小

    # ==================== 文件保存模块 ====================
    def save_temp_file()                   # 保存文件到临时位置
    def save_permanent_file()              # 保存文件到永久位置

    # ==================== 文件名处理模块 ====================
    def generate_safe_filename()           # 生成安全的文件名
    def get_unique_filepath()              # 获取唯一的文件路径（避免重名）

    # ==================== 文件信息模块 ====================
    def get_file_info()                    # 获取文件基本信息和图像尺寸
    def get_mime_type()                    # 获取文件MIME类型

    # ==================== 临时文件管理模块 ====================
    def cleanup_temp_files()               # 清理临时文件
    def __del__()                          # 析构函数确保清理

"""

import os
import tempfile
import mimetypes
from typing import Optional, Dict, Any, List, Tuple
from pathlib import Path
from PIL import Image as PILImage
from django.core.files.uploadedfile import UploadedFile
from django.conf import settings

from .base import BaseService


class FileService(BaseService):
    """
    文件处理服务类

    提供文件上传、验证、临时文件管理等功能。
    统一处理所有文件验证逻辑，避免重复实现。
    """

    # 支持的图像格式
    SUPPORTED_IMAGE_FORMATS = {'.jpg', '.jpeg', '.png', '.bmp', '.webp', '.avif'}

    # 支持的模型文件格式
    SUPPORTED_MODEL_FORMATS = {
        'barcode': ['.pt'],
        'ocr': ['.tar', '.tar.gz', '.zip'],
        'ai_restored': ['.onnx'],
        'feature_matching': ['.onnx']
    }

    # 允许的示例图片分类
    ALLOWED_EXAMPLE_CATEGORIES = {'barcode', 'ocr', 'ai_restored'}

    # 路径遍历攻击模式
    PATH_TRAVERSAL_PATTERNS = ['../', '..\\', '../', '..\\\\', '%2e%2e%2f', '%2e%2e%5c']

    def __init__(self):
        """初始化文件服务"""
        super().__init__()
        self.temp_files = []  # 跟踪临时文件以便清理
    
    def validate_image_file(self, file: UploadedFile) -> Tuple[bool, Optional[str]]:
        """
        验证图像文件
        
        Args:
            file: 上传的文件对象
            
        Returns:
            (是否有效, 错误消息)
        """
        if not file:
            return False, "未提供文件"
        
        # 检查文件扩展名
        file_ext = os.path.splitext(file.name)[1].lower()
        if file_ext not in self.SUPPORTED_IMAGE_FORMATS:
            return False, f"不支持的图像格式。支持的格式: {', '.join(self.SUPPORTED_IMAGE_FORMATS)}"
        
        # 检查文件大小（默认最大50MB）
        max_size = self.get_setting('MAX_IMAGE_SIZE', 50 * 1024 * 1024)
        if file.size > max_size:
            return False, f"图像文件过大，最大允许 {max_size // (1024 * 1024)}MB"
        
        return True, None
    
    def validate_model_file(self, file: UploadedFile, model_type: str) -> Tuple[bool, Optional[str]]:
        """
        验证模型文件
        
        Args:
            file: 上传的文件对象
            model_type: 模型类型
            
        Returns:
            (是否有效, 错误消息)
        """
        if not file:
            return False, "未提供文件"
        
        if model_type not in self.SUPPORTED_MODEL_FORMATS:
            return False, f"不支持的模型类型: {model_type}"
        
        # 检查文件扩展名
        filename = file.name.lower()
        allowed_exts = self.SUPPORTED_MODEL_FORMATS[model_type]
        
        if not any(filename.endswith(ext) for ext in allowed_exts):
            return False, f"{model_type}模型只支持以下格式: {', '.join(allowed_exts)}"
        
        # 检查文件大小（默认最大500MB）
        max_size = self.get_setting('MAX_MODEL_SIZE', 500 * 1024 * 1024)
        if file.size > max_size:
            return False, f"模型文件过大，最大允许 {max_size // (1024 * 1024)}MB"
        
        return True, None

    def validate_file(self, file: UploadedFile, file_type: str, **kwargs) -> Tuple[bool, Optional[str]]:
        """
        统一文件验证入口

        Args:
            file: 上传的文件对象
            file_type: 文件类型 ('image', 'model')
            **kwargs: 额外参数，如model_type等

        Returns:
            (是否有效, 错误消息)
        """
        if file_type == 'image':
            return self.validate_image_file(file)
        elif file_type == 'model':
            model_type = kwargs.get('model_type')
            if not model_type:
                return False, "模型验证需要指定model_type参数"
            return self.validate_model_file(file, model_type)
        else:
            return False, f"不支持的文件类型: {file_type}"

    def validate_file_path(self, file_path: str, file_type: str = None) -> Tuple[bool, Optional[str]]:
        """
        验证文件路径格式

        Args:
            file_path: 文件路径
            file_type: 文件类型 ('image', 'model')，可选

        Returns:
            (是否有效, 错误消息)
        """
        if not os.path.exists(file_path):
            return False, "文件不存在"

        if file_type == 'image':
            file_ext = os.path.splitext(file_path)[1].lower()
            if file_ext not in self.SUPPORTED_IMAGE_FORMATS:
                return False, f"不支持的图像格式: {file_ext}"

        return True, None

    def sanitize_filename(self, filename: str) -> str:
        """
        清理文件名，移除不安全字符

        Args:
            filename: 原始文件名

        Returns:
            清理后的安全文件名
        """
        # 不安全的字符
        unsafe_chars = ['/', '\\', '..', '<', '>', ':', '"', '|', '?', '*']
        safe_name = filename

        # 替换不安全字符
        for char in unsafe_chars:
            safe_name = safe_name.replace(char, '_')

        # 去除首尾空格
        safe_name = safe_name.strip()

        # 限制文件名长度
        name_part, ext_part = os.path.splitext(safe_name)
        if len(name_part) > 100:
            name_part = name_part[:100]

        return name_part + ext_part

    def validate_path_security(self, file_path: str, base_dir: str) -> bool:
        """
        验证路径安全性，防止路径遍历攻击

        Args:
            file_path: 要验证的文件路径
            base_dir: 基础目录

        Returns:
            是否安全
        """
        try:
            # 规范化路径
            normalized_base = os.path.normpath(os.path.abspath(base_dir))
            normalized_path = os.path.normpath(os.path.abspath(file_path))

            # 检查路径是否在基础目录内
            if not normalized_path.startswith(normalized_base):
                self.log_warning(f"Path traversal attempt detected: {file_path}")
                return False

            return True

        except Exception as e:
            self.log_error(f"Error validating path security: {str(e)}")
            return False

    def check_path_traversal(self, path: str) -> bool:
        """
        检查路径是否包含路径遍历攻击模式

        Args:
            path: 要检查的路径

        Returns:
            是否包含攻击模式
        """
        path_lower = path.lower()
        for pattern in self.PATH_TRAVERSAL_PATTERNS:
            if pattern in path_lower:
                self.log_warning(f"Path traversal pattern detected: {pattern} in {path}")
                return True

        return False
    
    def save_temp_file(self, file: UploadedFile, suffix: Optional[str] = None) -> str:
        """
        保存文件到临时位置
        
        Args:
            file: 上传的文件对象
            suffix: 文件后缀名
            
        Returns:
            临时文件路径
            
        Raises:
            Exception: 保存失败时抛出异常
        """
        if suffix is None:
            suffix = os.path.splitext(file.name)[1] or '.tmp'
        
        try:
            with tempfile.NamedTemporaryFile(delete=False, suffix=suffix) as tmp_file:
                for chunk in file.chunks():
                    tmp_file.write(chunk)
                temp_path = tmp_file.name
            
            # 记录临时文件以便后续清理
            self.temp_files.append(temp_path)
            self.log_info(f"File saved to temporary path: {temp_path}")
            return temp_path
            
        except Exception as e:
            self.log_error(f"Error saving file to temporary location: {str(e)}")
            raise Exception(f"保存临时文件失败: {str(e)}")
    
    def save_permanent_file(self, file: UploadedFile, target_dir: str, 
                          filename: Optional[str] = None) -> str:
        """
        保存文件到永久位置
        
        Args:
            file: 上传的文件对象
            target_dir: 目标目录
            filename: 目标文件名（可选）
            
        Returns:
            保存的文件路径
            
        Raises:
            Exception: 保存失败时抛出异常
        """
        # 确保目标目录存在
        os.makedirs(target_dir, exist_ok=True)
        
        # 生成文件名
        if filename is None:
            filename = file.name
        
        # 生成安全的文件名
        safe_filename = self.generate_safe_filename(filename)
        file_path = os.path.join(target_dir, safe_filename)
        
        # 避免文件名冲突
        file_path = self.get_unique_filepath(file_path)
        
        try:
            with open(file_path, 'wb+') as f:
                for chunk in file.chunks():
                    f.write(chunk)
            
            self.log_info(f"File saved permanently to: {file_path}")
            return file_path
            
        except Exception as e:
            self.log_error(f"Error saving file permanently: {str(e)}")
            raise Exception(f"保存文件失败: {str(e)}")
    
    def generate_safe_filename(self, filename: str) -> str:
        """
        生成安全的文件名
        
        Args:
            filename: 原始文件名
            
        Returns:
            安全的文件名
        """
        # 移除不安全的字符
        safe_name = filename.replace(' ', '_').replace('/', '_').replace('\\', '_')
        
        # 限制文件名长度
        name_part, ext_part = os.path.splitext(safe_name)
        if len(name_part) > 100:
            name_part = name_part[:100]
        
        return name_part + ext_part
    
    def get_unique_filepath(self, file_path: str) -> str:
        """
        获取唯一的文件路径（避免重名）
        
        Args:
            file_path: 原始文件路径
            
        Returns:
            唯一的文件路径
        """
        if not os.path.exists(file_path):
            return file_path
        
        base_path, ext = os.path.splitext(file_path)
        counter = 1
        
        while os.path.exists(file_path):
            file_path = f"{base_path}_{counter}{ext}"
            counter += 1
        
        return file_path

    def get_file_mime_type(self, file_path: str) -> str:
        """
        获取文件MIME类型

        Args:
            file_path: 文件路径

        Returns:
            MIME类型字符串
        """
        mime_type, _ = mimetypes.guess_type(file_path)
        return mime_type or 'application/octet-stream'

    def is_image_file(self, filename: str) -> bool:
        """
        检查文件是否为支持的图像格式

        Args:
            filename: 文件名

        Returns:
            是否为图像文件
        """
        file_ext = os.path.splitext(filename)[1].lower()
        return file_ext in self.SUPPORTED_IMAGE_FORMATS

    def get_file_info(self, file_path: str) -> Dict[str, Any]:
        """
        获取文件信息
        
        Args:
            file_path: 文件路径
            
        Returns:
            文件信息字典
        """
        if not os.path.exists(file_path):
            return {}
        
        file_info = {
            'size': os.path.getsize(file_path),
            'name': os.path.basename(file_path)
        }
        
        # 如果是图像文件，获取尺寸信息
        try:
            with PILImage.open(file_path) as img:
                file_info['width'], file_info['height'] = img.size
                file_info['format'] = img.format
        except Exception:
            # 不是图像文件或无法读取
            pass
        
        return file_info
    
    def get_mime_type(self, file_path: str) -> str:
        """
        获取文件的MIME类型
        
        Args:
            file_path: 文件路径
            
        Returns:
            MIME类型
        """
        content_type, _ = mimetypes.guess_type(file_path)
        return content_type or 'application/octet-stream'
    
    def cleanup_temp_files(self) -> None:
        """清理临时文件"""
        for temp_path in self.temp_files:
            try:
                if os.path.exists(temp_path):
                    os.remove(temp_path)
                    self.log_debug(f"Cleaned up temporary file: {temp_path}")
            except Exception as e:
                self.log_warning(f"Failed to cleanup temporary file {temp_path}: {str(e)}")
        
        self.temp_files.clear()
    
    def __del__(self):
        """析构函数，确保清理临时文件"""
        self.cleanup_temp_files()
