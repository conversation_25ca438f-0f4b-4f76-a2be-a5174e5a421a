repos:
-   repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.6.0
    hooks:
    -   id: check-added-large-files
        args: ['--maxkb=512']
    -   id: check-case-conflict
    -   id: check-merge-conflict
    -   id: check-symlinks
    -   id: detect-private-key
    -   id: end-of-file-fixer
    -   id: trailing-whitespace
        files: \.(c|cc|cxx|cpp|cu|h|hpp|hxx|py)$
-   repo: https://github.com/Lucas-C/pre-commit-hooks
    rev: v1.5.1
    hooks:
    -   id: remove-crlf
    -   id: remove-tabs
        files: \.(c|cc|cxx|cpp|cu|h|hpp|hxx|py)$
-   repo: local
    hooks:
    -   id: clang-format
        name: clang-format
        description: Format files with ClangFormat
        entry: bash .clang_format.hook -i
        language: system
        files: \.(c|cc|cxx|cpp|cu|h|hpp|hxx|cuh|proto)$
# For Python files
-   repo: https://github.com/psf/black.git
    rev: 24.4.2
    hooks:
    -   id: black
        files: (.*\.(py|pyi|bzl)|BUILD|.*\.BUILD|WORKSPACE)$

# Flake8
-   repo: https://github.com/pycqa/flake8
    rev: 7.0.0
    hooks:
    -   id: flake8
        args:
            - --count
            - --select=E9,F63,F7,F82,E721
            - --show-source
            - --statistics
        exclude: ^benchmark/|^test_tipc/
