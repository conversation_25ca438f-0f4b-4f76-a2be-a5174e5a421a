/**
 * 文本旋转和方向计算工具函数
 */

// 定义通用的最小和最大字体大小
export const MIN_ADAPTIVE_FONT_SIZE = 10; // 最小字体大小
export const MAX_ADAPTIVE_FONT_SIZE = 36; // 最大字体大小（防止过大）
export const TEXT_PADDING_FACTOR = 0.75; // 字体占多边形高度的比例，用于留白

/**
 * 基于 OCR 检测的四边形坐标计算文本的旋转角度
 * @param polygon 四个点的坐标数组 [[x1,y1], [x2,y2], [x3,y3], [x4,y4]]
 * @returns 文本的旋转角度（度数）和最佳的锚点位置
 */
export function calculateTextRotationFromPolygon(polygon: [number, number][]): {
  rotationAngle: number;
  centerX: number;
  centerY: number;
  anchorX: number;
  anchorY: number;
} {
  if (!polygon || polygon.length < 4) {
    // 如果多边形无效，返回默认值
    return {
      rotationAngle: 0,
      centerX: 0,
      centerY: 0,
      anchorX: 0,
      anchorY: 0,
    };
  }

  // 计算多边形的中心点
  const centerX = polygon.reduce((sum, point) => sum + point[0], 0) / polygon.length;
  const centerY = polygon.reduce((sum, point) => sum + point[1], 0) / polygon.length;

  // 计算文本的主要方向
  // 通常 OCR 检测框是按照顺时针或逆时针顺序排列的
  // 我们取第一条边（point[0] 到 point[1]）作为文本的主要方向
  const dx = polygon[1][0] - polygon[0][0];
  const dy = polygon[1][1] - polygon[0][1];

  // 计算角度（弧度转度数）
  let rotationAngle = Math.atan2(dy, dx) * (180 / Math.PI);
  
  // 确保角度在合理范围内（-90 到 90 度）
  // 如果角度超过 90 度，说明文本可能是倒置的，需要调整
  if (rotationAngle > 90) {
    rotationAngle -= 180;
  } else if (rotationAngle < -90) {
    rotationAngle += 180;
  }

  // 计算文本锚点（通常选择多边形的左上角附近）
  // 寻找 y 坐标最小的点，如果有多个，选择 x 坐标最小的
  let anchorPoint = polygon[0];
  for (let i = 1; i < polygon.length; i++) {
    if (polygon[i][1] < anchorPoint[1] || 
        (polygon[i][1] === anchorPoint[1] && polygon[i][0] < anchorPoint[0])) {
      anchorPoint = polygon[i];
    }
  }

  return {
    rotationAngle,
    centerX,
    centerY,
    anchorX: anchorPoint[0],
    anchorY: anchorPoint[1],
  };
}

/**
 * 计算多边形的宽度和高度（沿着主要方向）
 * @param polygon 四边形坐标
 * @returns 主方向上的宽度和高度
 */
export function calculatePolygonDimensions(polygon: [number, number][]): {
  width: number;
  height: number;
} {
  if (!polygon || polygon.length < 4) {
    return { width: 0, height: 0 };
  }

  // 计算第一条边和第二条边的长度
  const edge1Length = Math.sqrt(
    Math.pow(polygon[1][0] - polygon[0][0], 2) + 
    Math.pow(polygon[1][1] - polygon[0][1], 2)
  );
  
  const edge2Length = Math.sqrt(
    Math.pow(polygon[2][0] - polygon[1][0], 2) + 
    Math.pow(polygon[2][1] - polygon[1][1], 2)
  );

  // 通常较长的边是文本的主方向（宽度），较短的边是高度
  return {
    width: Math.max(edge1Length, edge2Length),
    height: Math.min(edge1Length, edge2Length),
  };
}

/**
 * 计算适合检测框的字体大小
 * @param text 要显示的文本
 * @param polygon 检测框多边形
 * @param baseFontSize 基础字体大小（作为最大参考值）
 * @returns 调整后的字体大小
 */
export function calculateAdaptiveFontSize(
  text: string,
  polygon: [number, number][],
  baseFontSize: number // 现在作为最大参考值
): number {
  if (!text || !polygon || polygon.length < 4 || baseFontSize <= 0) {
    return MIN_ADAPTIVE_FONT_SIZE;
  }

  const { width: boxWidth, height: boxHeight } = calculatePolygonDimensions(polygon);

  // 可用宽度和高度，已应用填充因子
  const availableWidth = boxWidth * TEXT_PADDING_FACTOR;
  const availableHeight = boxHeight * TEXT_PADDING_FACTOR;

  // 如果可用空间太小，立即返回最小字体大小
  if (availableWidth <= 0 || availableHeight <= 0) {
    return MIN_ADAPTIVE_FONT_SIZE;
  }

  // 估算能够适应宽度和高度的最大字体大小
  // 假设英文字符宽度约为字体大小的 0.6 倍，中文字符宽度约为字体大小的 1 倍
  const chineseCharCount = (text.match(/[\u4e00-\u9fa5]/g) || []).length;
  const englishCharCount = text.length - chineseCharCount;

  // 确保字符宽度因子至少为 1，避免除以零或产生 Infinity
  const charWidthFactor = Math.max(1, (englishCharCount * 0.6) + (chineseCharCount * 1.0));
  let fs_from_width = availableWidth / charWidthFactor;
  
  // 假设单行文本高度近似为字体大小的 1.2 倍 (行高)
  const lineHeightFactor = 1.2; 
  const fs_from_height = availableHeight / lineHeightFactor;

  // 选择较小的计算结果作为调整后的字体大小的上限
  let adjustedFontSize = Math.min(fs_from_width, fs_from_height, baseFontSize);

  // 最终限制字体大小在预设的最小和最大范围内
  adjustedFontSize = Math.max(MIN_ADAPTIVE_FONT_SIZE, Math.min(adjustedFontSize, MAX_ADAPTIVE_FONT_SIZE));

  return adjustedFontSize;
}

/**
 * 计算文本在旋转后的最佳显示位置
 * @param polygon 四边形坐标
 * @param rotationAngle 旋转角度
 * @returns 文本显示的位置信息
 */
export function calculateOptimalTextPosition(
  polygon: [number, number][],
  rotationAngle: number
): {
  x: number;
  y: number;
  textAnchor: 'start' | 'middle' | 'end';
  dominantBaseline: 'auto' | 'text-before-edge' | 'text-after-edge' | 'central' | 'middle';
} {
  const { centerX, centerY } = calculateTextRotationFromPolygon(polygon);
  
  // 根据旋转角度调整文本对齐方式
  const absAngle = Math.abs(rotationAngle);
  
  let textAnchor: 'start' | 'middle' | 'end' = 'middle';
  let dominantBaseline: 'auto' | 'text-before-edge' | 'text-after-edge' | 'central' | 'middle' = 'central';
  
  // 对于接近水平的文本，使用中心对齐
  if (absAngle < 15) {
    textAnchor = 'middle';
    dominantBaseline = 'central';
  } else if (absAngle > 75) {
    // 对于接近垂直的文本，调整对齐方式
    textAnchor = 'middle';
    dominantBaseline = 'central';
  } else {
    // 对于倾斜的文本，使用中心对齐以获得最佳效果
    textAnchor = 'middle';
    dominantBaseline = 'central';
  }

  return {
    x: centerX,
    y: centerY,
    textAnchor,
    dominantBaseline,
  };
}

/**
 * 计算用于 Canvas 2D Context 的旋转变换参数
 * @param polygon 四边形坐标
 * @returns Canvas 旋转变换的参数
 */
export function calculateCanvasRotationTransform(polygon: [number, number][]): {
  centerX: number;
  centerY: number;
  rotationAngle: number; // 弧度
} {
  const { centerX, centerY, rotationAngle: angleDegrees } = calculateTextRotationFromPolygon(polygon);
  
  return {
    centerX,
    centerY,
    rotationAngle: angleDegrees * (Math.PI / 180), // 转换为弧度
  };
} 