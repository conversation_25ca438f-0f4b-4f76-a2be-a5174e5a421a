import axios from 'axios';
import { dataURLtoBlob } from '../utils/imageUtils'; // 导入 dataURLtoBlob

// --- 基本配置 ---
const API_BASE_URL = (import.meta.env.VITE_API_BASE_URL || 'http://127.0.0.1:8000').replace(/\/$/, '');

const apiClient = axios.create({
  baseURL: API_BASE_URL,
  withCredentials: true,
});

// --- 类型定义 ---

export interface BarcodeDetectionParams {
  image: File | string;
  model_name?: string;
  model_file?: File;
  confidence_threshold?: number;
  device?: string;
  preprocessing_method?: "full_scale" | "roi_area";
}

export interface Barcode {
  box: [number, number, number, number];
  class_name: string;
  confidence: number;
  class_id?: number;
}

export interface BarcodeDetectionResult {
  detections: Barcode[];
}

export interface ApiErrorData {
  detail?: string | Record<string, any> | Array<Record<string, any>>;
}

export class CustomApiError extends Error {
  status?: number;
  detail?: string | Record<string, any> | Array<Record<string, any>>;

  constructor(message: string, status?: number, detail?: any) {
    super(message);
    this.name = 'CustomApiError';
    this.status = status;
    this.detail = detail;
    Object.setPrototypeOf(this, CustomApiError.prototype);
  }
}

export interface VisionModel {
  id: number;
  name: string;
  model_type: string;
  version: string;
  description?: string | null;
  file_path: string;
  is_system_model: boolean;
  uploaded_at: string;
  ocr_role?: string | null;
  ocr_collection_name?: string | null;
}

export interface GroupedVisionModelsApiResponse {
  [modelType: string]: VisionModel[];
}

export interface UploadCustomModelParams {
  model_file: File;
  name: string;
  model_type: string;
  version?: string;
  description?: string;
}

export interface OcrDetectionParams {
  image: File | string;
  ocr_task_name: string;
  use_gpu?: boolean;
}

export interface OcrResultItem {
  text: string;
  confidence: number;
  box: [number, number][];
}

export interface OcrDetectionApiResponse {
  task_name: string;
  results: OcrResultItem[];
  time_info: Record<string, any>;
}

export interface OcrTask {
  task_name: string;
  display_name: string;
}

export type OcrTasksApiResponse = OcrTask[];

export interface AiImageRestoreParams {
  image: File | string;
  output_format?: 'png' | 'jpeg' | 'webp';
  model_name?: string;
}

export interface AiImageRestoreResult {
  image_base64: string;
  original_filename?: string;
  restored_filename?: string;
  format?: string;
  message?: string;
  original_size?: [number, number];
  restored_size?: [number, number];
}

export interface ExampleImage {
  id: number;
  name: string;
  description: string | null;
  url: string;
  category: 'barcode' | 'ocr' | 'ai_restored';
  file_size: number;
  dimensions: [number, number];
}

export interface ExampleImagesDashboardResponse {
  barcode: ExampleImage[];
  ocr: ExampleImage[];
  ai_restored: ExampleImage[];
}

export interface AdminExampleImagesResponse {
  images: ExampleImage[];
  folders: string[];
}

export interface CreateExampleFolderParams {
  category: string;
  folder_name: string;
}

export interface RenameExampleFolderParams {
  category: string;
  old_name: string;
  new_name: string;
}

export interface DeleteExampleFolderParams {
  category: string;
  folder_name: string;
}

export interface FolderApiResponse {
    success: boolean;
    message: string;
}

export interface UpdateImageOrderParams {
  category: string;
  folder?: string;
  source_folder?: string;
  ordered_image_ids: string[];
}

export interface UpdateImageOrderResponse {
    success: boolean;
    message: string;
}

export interface UploadExampleImageParams {
  file: File;
  category: 'barcode' | 'ocr' | 'ai_restored';
  name?: string;
  description?: string;
}

export interface DeleteExampleImagesParams {
  category: 'barcode' | 'ocr' | 'ai_restored';
  ids: number[];
}

export interface AdminLoginParams {
  password: string;
}

export interface AdminApiResponse {
  success: boolean;
  message: string;
}

export interface AdminStatusResponse {
  success: boolean;
  is_admin: boolean;
  login_time?: string;
  message?: string;
}

export interface ModelDeleteParams {
  model_ids: number[];
}

export interface ModelDeleteResponse {
  success: boolean;
  message: string;
  deleted: Array<{ id: number; name: string; model_type: string; }>;
  failed: Array<{ id: number; error: string; }>;
}

export interface ModelUploadParams {
  file: File;
  name: string;
  model_type: 'barcode' | 'ocr' | 'ai_restored';
  version: string;
  description?: string;
  ocr_role?: 'detection' | 'recognition';
  is_system_model?: boolean;
  ocr_collection_name?: string;
}

export interface ModelUploadResponse {
  success: boolean;
  message: string;
  model?: VisionModel;
}

export interface ModelUpdateParams {
  name: string;
  model_type: 'barcode' | 'ocr' | 'ai_restored';
  version: string;
  description?: string;
  ocr_role?: 'detection' | 'recognition';
  is_system_model?: boolean;
  ocr_collection_name?: string;
}

export interface ModelUpdateResponse {
  success: boolean;
  message: string;
  model?: VisionModel;
}

// 从 Context 导入并导出 FeatureMatchingResult 类型
export type { FeatureMatchingResult } from '../contexts/FeatureMatchingTraditionalContext';
import { FeatureMatchingResult } from '../contexts/FeatureMatchingTraditionalContext';


// --- API 调用函数 ---

const handleError = (error: unknown, defaultMessage: string): never => {
    let errorMessage = defaultMessage;
    let errorStatus: number | undefined;
    let errorDetails: ApiErrorData['detail'] | undefined;

    if (axios.isAxiosError(error)) {
        errorStatus = error.response?.status;
        const responseData = error.response?.data;

        if (responseData && typeof responseData === 'object') {
            errorDetails = responseData;
            if ('message' in responseData && typeof responseData.message === 'string') {
                errorMessage = responseData.message;
            } else if ('detail' in responseData && typeof responseData.detail === 'string') {
                errorMessage = responseData.detail;
            } else if ('error' in responseData && typeof responseData.error === 'string') {
                errorMessage = responseData.error;
            }
        } else if (typeof responseData === 'string' && responseData.trim() !== '') {
            errorMessage = responseData;
            errorDetails = responseData;
        } else if (error.message) {
            errorMessage = error.message;
        }
    } else if (error instanceof Error) {
        errorMessage = error.message;
    }
    throw new CustomApiError(errorMessage, errorStatus, errorDetails);
};


export const detectBarcode = async (params: BarcodeDetectionParams): Promise<BarcodeDetectionResult> => {
  const formData = new FormData();
  let imageToUpload: File;
  if (typeof params.image === 'string') {
    const blob = dataURLtoBlob(params.image);
    imageToUpload = new File([blob], 'cropped_image.png', { type: blob.type });
  } else {
    imageToUpload = params.image;
  }
  formData.append('image', imageToUpload);
  if (params.model_name) formData.append('model_name', params.model_name);
  else if (params.model_file) formData.append('model_file', params.model_file);
  if (params.confidence_threshold !== undefined) formData.append('confidence_threshold', params.confidence_threshold.toString());
  if (params.device) formData.append('device', params.device);
  if (params.preprocessing_method) formData.append('preprocessing_method', params.preprocessing_method);
  
  try {
    const response = await apiClient.post<BarcodeDetectionResult>('vision/detect/barcode/ultralytics/', formData);
    return response.data;
  } catch (error) {
    return handleError(error, '条码检测请求失败，请稍后重试。');
  }
};

export const getGroupedVisionModels = async (modelScope?: 'system' | 'custom' | 'all'): Promise<GroupedVisionModelsApiResponse> => {
  try {
    let apiUrl = 'vision/models/';
    if (modelScope) apiUrl += `?model_scope=${modelScope}`;
    const response = await apiClient.get<GroupedVisionModelsApiResponse>(apiUrl);
    return response.data;
  } catch (error) {
    return handleError(error, '获取模型列表失败，请稍后重试。');
  }
};

export const uploadCustomModel = async (params: UploadCustomModelParams): Promise<VisionModel> => {
  const formData = new FormData();
  formData.append('model_file', params.model_file);
  formData.append('name', params.name);
  formData.append('model_type', params.model_type);
  if (params.version) formData.append('version', params.version);
  if (params.description) formData.append('description', params.description);

  try {
    const response = await apiClient.post<VisionModel>('vision/models/upload/', formData);
    return response.data;
  } catch (error) {
    return handleError(error, '模型上传失败，请稍后重试。');
  }
};

export const detectOcrPaddle = async (params: OcrDetectionParams): Promise<OcrDetectionApiResponse> => {
  const formData = new FormData();
  let imageToUpload: File;
  if (typeof params.image === 'string') {
    const blob = dataURLtoBlob(params.image);
    imageToUpload = new File([blob], 'ocr_image.png', { type: blob.type });
  } else {
    imageToUpload = params.image;
  }
  formData.append('image', imageToUpload);
  formData.append('ocr_task_name', params.ocr_task_name);
  if (params.use_gpu !== undefined) formData.append('use_gpu', params.use_gpu.toString());

  try {
    const response = await apiClient.post<OcrDetectionApiResponse>('vision/detect/ocr/paddle/', formData);
    return response.data;
  } catch (error) {
    return handleError(error, 'OCR 检测请求失败，请稍后重试。');
  }
};

export const getOcrTasks = async (): Promise<OcrTasksApiResponse> => {
  try {
    const response = await apiClient.get<OcrTasksApiResponse>('vision/ocr-tasks/');
    return response.data;
  } catch (error) {
    return handleError(error, '获取OCR模型列表失败，请稍后重试。');
  }
};

export const detectImageRestore = async (params: AiImageRestoreParams): Promise<AiImageRestoreResult> => {
    const formData = new FormData();
    let imageToUpload: File;
    if (typeof params.image === 'string') {
        const blob = dataURLtoBlob(params.image);
        const fileName = `uploaded_image.${blob.type.split('/')[1] || 'bin'}`;
        imageToUpload = new File([blob], fileName, { type: blob.type });
    } else {
        imageToUpload = params.image;
    }
    formData.append('image', imageToUpload);
    if (params.output_format) formData.append('output_format', params.output_format);
    if (params.model_name) formData.append('model_name', params.model_name);

    try {
        const response = await apiClient.post<AiImageRestoreResult>('vision/restore/image/', formData);
        return response.data;
    } catch (error) {
        return handleError(error, 'AI 图像修复请求失败，请稍后重试。');
    }
};

export const getAIRestoredModels = async (): Promise<VisionModel[]> => {
    try {
        const response = await apiClient.get<VisionModel[] | GroupedVisionModelsApiResponse>('vision/models/?model_type_filter=ai_restored');
        if (Array.isArray(response.data)) {
            return response.data;
        } else if (response.data && typeof response.data === 'object' && 'ai_restored' in response.data) {
            return (response.data as GroupedVisionModelsApiResponse)['ai_restored'] || [];
        }
        return [];
    } catch (error) {
        return handleError(error, '获取 AI 修复模型列表失败，请稍后重试。');
    }
};

export const getBarcodeModels = async (): Promise<{ systemModels: VisionModel[], customModels: VisionModel[] }> => {
    try {
        const systemModelsResponse = await getGroupedVisionModels('system');
        const customModelsResponse = await getGroupedVisionModels('custom');
        return {
            systemModels: systemModelsResponse['barcode'] || [],
            customModels: customModelsResponse['barcode'] || []
        };
    } catch (error) {
        return handleError(error, '获取条码模型列表失败，请稍后重试。');
    }
};

export const getOcrTasksForReactQuery = async (): Promise<OcrTask[]> => {
    try {
        return await getOcrTasks();
    } catch (error) {
        return handleError(error, '获取OCR任务列表失败，请稍后重试。');
    }
};

export const getExampleImagesForDashboard = async (): Promise<ExampleImagesDashboardResponse> => {
    try {
        const response = await apiClient.get<ExampleImagesDashboardResponse>('vision/example-images/?source=dashboard');
        return response.data;
    } catch (error) {
        return handleError(error, '获取看板示例图片列表失败，请稍后重试。');
    }
};

export const getExampleImagesForAdmin = async (category: string, folder?: string): Promise<AdminExampleImagesResponse> => {
    try {
        let url = `vision/example-images/?category=${category}&_=${new Date().getTime()}`;
        if (folder) url += `&folder=${folder}`;
        const response = await apiClient.get<AdminExampleImagesResponse>(url);
        return response.data;
    } catch (error) {
        return handleError(error, '获取管理后台示例图片列表失败，请稍后重试。');
    }
};

export const updateImageOrder = async (params: UpdateImageOrderParams): Promise<UpdateImageOrderResponse> => {
    try {
        const response = await apiClient.post<UpdateImageOrderResponse>('vision/example-images/update-order/', params);
        return response.data;
    } catch (error) {
        return handleError(error, '更新图片顺序失败，请稍后重试。');
    }
};

export const uploadExampleImage = async (params: UploadExampleImageParams): Promise<ExampleImage> => {
    const formData = new FormData();
    formData.append('file', params.file);
    formData.append('category', params.category);
    if (params.name) formData.append('name', params.name);
    if (params.description) formData.append('description', params.description);
    try {
        const response = await apiClient.post<ExampleImage>('vision/example-images/upload/', formData);
        return response.data;
    } catch (error) {
        return handleError(error, '上传示例图片失败，请稍后重试。');
    }
};

export const deleteExampleImages = async (params: DeleteExampleImagesParams): Promise<{ success: boolean; deleted_ids: number[]; failed_ids: Array<{ id: number, reason: string }>; message?: string }> => {
    try {
        const response = await apiClient.post('vision/example-images/delete/', params);
        return response.data;
    } catch (error) {
        return handleError(error, '删除示例图片失败，请稍后重试。');
    }
};

export const updateImageDescription = async (imageId: number, description: string): Promise<ExampleImage> => {
    try {
        const response = await apiClient.put<ExampleImage>(`vision/example-images/update-description/${imageId}/`, { description });
        return response.data;
    } catch (error) {
        return handleError(error, '更新图片描述失败，请稍后重试。');
    }
};

export const createExampleFolder = async (params: CreateExampleFolderParams): Promise<FolderApiResponse> => {
    try {
        const response = await apiClient.post<FolderApiResponse>('vision/example-images/folders/create/', params);
        return response.data;
    } catch (error) {
        return handleError(error, '创建文件夹失败，请稍后重试。');
    }
};

export const renameExampleFolder = async (params: RenameExampleFolderParams): Promise<FolderApiResponse> => {
    try {
        const response = await apiClient.post<FolderApiResponse>('vision/example-images/folders/rename/', params);
        return response.data;
    } catch (error) {
        return handleError(error, '重命名文件夹失败，请稍后重试。');
    }
};

export const deleteExampleFolder = async (params: DeleteExampleFolderParams): Promise<FolderApiResponse> => {
    try {
        const response = await apiClient.post<FolderApiResponse>('vision/example-images/folders/delete/', params);
        return response.data;
    } catch (error) {
        return handleError(error, '删除文件夹失败，请稍后重试。');
    }
};

export const adminLogin = async (params: AdminLoginParams): Promise<AdminApiResponse> => {
    try {
        const response = await apiClient.post<AdminApiResponse>('vision/admin/login/', params);
        return response.data;
    } catch (error) {
        return handleError(error, '管理员登录失败，请稍后重试。');
    }
};

export const adminLogout = async (): Promise<AdminApiResponse> => {
    try {
        const response = await apiClient.post<AdminApiResponse>('vision/admin/logout/');
        return response.data;
    } catch (error) {
        return handleError(error, '管理员登出失败，请稍后重试。');
    }
};

export const adminCheckStatus = async (): Promise<AdminStatusResponse> => {
    try {
        const response = await apiClient.get<AdminStatusResponse>('vision/admin/status/');
        return response.data;
    } catch (error) {
        return handleError(error, '检查管理员状态失败，请稍后重试。');
    }
};

export const deleteModels = async (params: ModelDeleteParams): Promise<ModelDeleteResponse> => {
    try {
        const response = await apiClient.post<ModelDeleteResponse>('vision/models/delete/', params);
        return response.data;
    } catch (error) {
        return handleError(error, '删除模型失败，请稍后重试。');
    }
};

export const uploadModel = async (params: ModelUploadParams): Promise<ModelUploadResponse> => {
    const formData = new FormData();
    formData.append('model_file', params.file);
    formData.append('name', params.name);
    formData.append('model_type', params.model_type);
    formData.append('version', params.version);
    if (params.description) formData.append('description', params.description);
    if (params.ocr_role) formData.append('ocr_role', params.ocr_role);
    if (params.is_system_model !== undefined) formData.append('is_system_model', params.is_system_model.toString());
    if (params.ocr_collection_name) formData.append('ocr_collection_name', params.ocr_collection_name);
    
    try {
        const response = await apiClient.post<ModelUploadResponse>('vision/models/upload/', formData, {
            headers: { 'Content-Type': 'multipart/form-data' },
        });
        return response.data;
    } catch (error) {
        return handleError(error, '上传模型失败，请稍后重试。');
    }
};

export const updateModel = async (modelId: number, params: ModelUpdateParams): Promise<ModelUpdateResponse> => {
    try {
        const response = await apiClient.put<ModelUpdateResponse>(`vision/models/${modelId}/update/`, params);
        return response.data;
    } catch (error) {
        return handleError(error, '更新模型失败，请稍后重试。');
    }
};

export const featureMatchingTraditionalApi = async (formData: FormData): Promise<FeatureMatchingResult> => {
  try {
    const response = await apiClient.post<FeatureMatchingResult>('vision/detect/feature-matching/traditional/', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  } catch (error) {
    return handleError(error, '特征匹配请求失败，请检查参数或稍后重试。');
  }
};

// --- 深度学习模型特征匹配接口定义 ---

export interface FeatureMatchingModelParams {
  template_image: File;
  target_image: File;
  model_id: number;
  template_roi?: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  keypoints_count?: number;
  nms_grid_size?: number;
  nms_threshold?: number;
  match_ratio_threshold?: number;
  min_match_count?: number;
  ransac_threshold?: number;
}

export interface FeatureMatchingModelMatch {
  template_point: [number, number];
  target_point: [number, number];
  distance: number;
}

export interface FeatureMatchingModelResult {
  status: 'success' | 'failure' | 'error';
  message: string;
  data: {
    match_count: number;
    matches: FeatureMatchingModelMatch[];
    homography?: number[][];
    template_keypoints_count: number;
    target_keypoints_count: number;
    template_roi?: {
      x: number;
      y: number;
      width: number;
      height: number;
    };
    processing_time: number; // 处理耗时（单位：秒）
  };
}

export const featureMatchingModelApi = async (params: FeatureMatchingModelParams): Promise<FeatureMatchingModelResult> => {
  const formData = new FormData();
  formData.append('template_image', params.template_image);
  formData.append('target_image', params.target_image);
  formData.append('model_id', params.model_id.toString());
  
  if (params.template_roi) {
    formData.append('template_roi', JSON.stringify(params.template_roi));
  }
  
  if (params.keypoints_count !== undefined) {
    formData.append('keypoints_count', params.keypoints_count.toString());
  }
  
  if (params.nms_grid_size !== undefined) {
    formData.append('nms_grid_size', params.nms_grid_size.toString());
  }
  
  if (params.nms_threshold !== undefined) {
    formData.append('nms_threshold', params.nms_threshold.toString());
  }
  
  if (params.match_ratio_threshold !== undefined) {
    formData.append('match_ratio_threshold', params.match_ratio_threshold.toString());
  }
  
  if (params.min_match_count !== undefined) {
    formData.append('min_match_count', params.min_match_count.toString());
  }
  
  if (params.ransac_threshold !== undefined) {
    formData.append('ransac_threshold', params.ransac_threshold.toString());
  }

  try {
    const response = await apiClient.post<FeatureMatchingModelResult>('vision/detect/feature-matching/model/', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  } catch (error) {
    return handleError(error, '深度学习模型特征匹配请求失败，请检查参数或稍后重试。');
  }
};

export default apiClient;