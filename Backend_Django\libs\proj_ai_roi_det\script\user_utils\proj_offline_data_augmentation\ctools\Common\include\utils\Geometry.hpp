#ifndef _GEOMETRY_H
#define _GEOMETRY_H

//-----------------------------------------------------------------------------
//  Includes

#include <cmath>
#include <cstdint>
#include <vector>
#include <algorithm>
//-----------------------------------------------------------------------------
//  Definitions


//-----------------------------------------------------------------------------
//  Declarations

/**
 * @brief    
 *           几何运算模块，默认坐标原点在左上角
 *           
 * @date     2024-04-25 Created by Huang<PERSON>
 */
template<typename operate_t>
class Geometry2D {
public:
    struct Point {
        operate_t x; // x坐标
        operate_t y; // y坐标

        // 重载==符号
        bool operator==(const Point& other) const {
            return x == other.x && y == other.y;
        }
    }; // 表示点（或向量）的结构体

    typedef std::vector<Point> Polygon; // 表示多边形的数据类型

    /**
     * @brief    
     *           计算两点间的距离
     *           
     * @param    x0, y0:        第一个点的坐标
     * @param    x1, y1:        第二个点的坐标
     *           
     * @retval   两点间的距离
     *           
     * @date     2024-08-09 Created by HuangJP
     */
    static inline double Calculate_Distance(operate_t x0, operate_t y0, operate_t x1, operate_t y1)
    {
        return sqrt(pow(x0-x1, 2) + pow(y0-y1, 2));
    }

    /**
     * @brief    
     *           获取从点p1指向点p2的向量
     *           
     * @param    p1, p2:      形成向量的两个坐标点
     *           
     * @retval   从点p1指向点p2的向量
     *           
     * @date     2024-04-25 Created by HuangJP
     */
    static inline Point Get_Vector(Point p1, Point p2)
    {
        return (Point){p2.x - p1.x, p2.y - p1.y};
    }

    /**
     * @brief    
     *           计算两个向量的叉乘
     *           
     * @param    vec1, vec2:    要计算叉乘的向量
     *           
     * @retval   两个向量叉乘结果
     *           
     * @date     2024-04-25 Created by HuangJP
     */
    static inline operate_t Cross_Product(Point vec1, Point vec2)
    {
        return vec1.x * vec2.y - vec2.x * vec1.y;
    }

    /**
     * @brief    
     *           计算两个向量的点乘
     *           
     * @param    vec1, vec2:    要计算点乘的向量
     *           
     * @retval   两个向量点乘结果
     *           
     * @date     2024-08-09 Created by HuangJP
     */
    static inline operate_t Dot_Product(Point vec1, Point vec2)
    {
        return vec1.x * vec2.x + vec2.y * vec1.y;
    }

    /**
     * @brief    
     *           判断一个点是否在凸多边形内部
     *           
     * @param    point:     参与判断的点坐标（参考点）
     * @param    convex:    凸多边形的顶点坐标（要求点按顺序排列）
     *           
     * @retval   true:      点在凸多边形内（点在凸边形上也返回此值）
     * @retval   false:     点在凸多边形外
     *           
     * @date     2024-04-25 Created by HuangJP
     */
    static bool Is_Point_Inside_Convex_Polygon(Point point, Polygon convex)
    {
        // 先获取凸多边形中，第一个点与最后一个点与参考点形成的向量的叉乘符号
        Point b = convex.back();
        Point c = convex.front();
        Point ab = Get_Vector(point, b);
        Point ac = Get_Vector(point, c);
        operate_t cp = Cross_Product(ab, ac);

        // 记录数值符号
        operate_t max_val = cp;
        operate_t min_val = cp;

        // 依次计算参考点与凸多边形顶点形成的相邻两个向量的叉乘符号
        int loop = convex.size()-1;
        for (int i = 0; i < loop; i++)
        {
            b = convex[i];
            c = convex[i+1];
            ab = Get_Vector(point, b);
            ac = Get_Vector(point, c);
            cp = Cross_Product(ab, ac);
            max_val = std::max(max_val, cp);
            min_val = std::min(min_val, cp);
        }

        // 所有相邻向量的叉乘符号均一致时，可判断参考点不在凸多边形外，否则可以判断点在凸多边形外
        if ((max_val * min_val) < 0)
        {
            return false;
        }

        return true;
    }

    /**
     * @brief    
     *           找到边ab与凸多边形的交点坐标
     *           
     * @param    a, b:        边坐标点
     * @param    convex:      凸多边形的顶点坐标（要求点按顺序排列）
     * @param    points:      存储交点坐标的变量
     *           
     * @date     2024-04-25 Created by HuangJP
     */
    static void Find_Intersecting_Points(Point a, Point b, Polygon convex, Polygon &points)
    {
        // 添加第一个点到convex末尾，方便后续计算
        convex.emplace_back(convex.front());

        // 计算边ab与凸多边形每条边的交点，计算方法参考：https://zhuanlan.zhihu.com/p/322265453 (b)如何得到两个凸多边形相交的交点
        int loop = convex.size() - 1;
        for (int i = 0; i < loop; i++)
        {
            // 获取凸多边形的边cd
            Point c = convex[i];
            Point d = convex[i+1];

            Point ab = Get_Vector(a, b);
            Point ac = Get_Vector(a, c);
            Point ad = Get_Vector(a, d);

            Point cd = Get_Vector(c, d);
            Point ca = Get_Vector(c, a);
            Point cb = Get_Vector(c, b);

            // 判断边ab和边cd是否没有交点
            if ((Cross_Product(ac, ab) * Cross_Product(ad, ab) >= 0)
                || (Cross_Product(ca, cd) * Cross_Product(cb, cd) >= 0))
            {
                continue;
            }

            // 计算交点坐标
            float div = (float)Cross_Product(ac, ad) / (float)Cross_Product(ab, cd);
            points.emplace_back((Point){a.x + (operate_t)(div * (float)ab.x), a.y + (operate_t)(div * (float)ab.y)});
        }
    }

    /**
     * @brief    
     *           获取两个凸多边形所有相交的点
     *           
     * @param    convex1, convex2:  要获取交点的两个凸多边形（要求凸多边形顶点坐标按顺序排列）
     *           
     * @retval   两个凸多边形所有的交点，若没有交点，则返回空
     *           
     * @date     2024-04-25 Created by HuangJP
     */
    static Polygon Get_All_Intersecting_Points(Polygon convex1, Polygon convex2)
    {
        // 添加第一个点到convex末尾，方便后续计算
        convex1.emplace_back(convex1.front());
        Polygon points;

        // 找到convex1每条边与convex2相交的点
        int loop = convex1.size() - 1;
        for (int i = 0; i < loop; i++)
        {
            Point a = convex1[i];
            Point b = convex1[i+1];

            Find_Intersecting_Points(a, b, convex2, points);
        }

        return points;
    }

    /**
     * @brief    
     *           构建凸包
     *           
     * @param    需要构建凸包的点列表
     *           
     * @retval   构建好的凸包（按逆时针排序）
     *           
     * @date     2024-10-28 Created by HuangJP
     */
    static Polygon Build_Convex_Hull(Polygon points) {
        if (points.size() < 3) return points;

        // 首先按照x坐标排序，如果x相同则按y排序
        std::sort(points.begin(), points.end(), 
            [](const Point& a, const Point& b) {
                return a.x < b.x || (a.x == b.x && a.y < b.y);
            });

        // 构建上下凸壳
        std::vector<Point> hull;

        // 构建下凸壳
        for (const Point& p : points) {
            // 当新点p加入时，如果发现最后两个点与p的转向为顺时针或共线
            // 则需要将hull最后的点删除，直到满足条件
            while (hull.size() >= 2 && turn(hull[hull.size()-2], hull.back(), p) <= 0)
            {
                hull.pop_back();
            }
            hull.push_back(p);
        }

        // 记住下凸壳的大小（除了最后一个点）
        auto lower_hull_size = hull.size();

        // 构建上凸壳
        for (int i = points.size()-2; i >= 0; i--) {
            const Point& p = points[i];
            while ((hull.size() > lower_hull_size) && (turn(hull[hull.size()-2], hull.back(), p) <= 0))
            {
                hull.pop_back();
            }
            hull.push_back(p);
        }

        // 去掉最后一个重复的点（起点）
        if (hull.size() > 1) {
            hull.pop_back();
        }

        return hull;
    }

    /**
     * @brief    
     *           排序凸多边形，同时消除相同的点
     *           
     * @param    convex:    要排序的凸多边形的顶点坐标
     *           
     * @retval   排序后的凸多边形的顶点坐标
     *           
     * @date     2024-04-25 Created by HuangJP
     */
    static Polygon Sort_Convex_Polygon_With_Erase(Polygon convex)
    {
        // 判断传入的数据能否构成多边形
        if (convex.size() < 3)
        {
            return convex;
        }

        // 以最左上角的点为基准点
        int left_most_pos1 = 0, left_most_pos2 = 0;
        for (int i = 0; i < (int)convex.size(); i++)
        {
            // 获取最左边的两个点
            if (convex[left_most_pos1].x > convex[i].x)
            {
                left_most_pos2 = left_most_pos1;
                left_most_pos1 = i;
            }
        }
        int base_point_pos = convex[left_most_pos1].y < convex[left_most_pos2].y ? left_most_pos1 : left_most_pos2; // 选择最左边两个点中靠上的点作为基准点
        std::swap(convex[0], convex[base_point_pos]); // 交换基准点

        // 使用冒泡排序按顺时针排列坐标点
        Point a = convex[0];
        for (int count = 0; count < (int)convex.size(); count++)
        {
            bool swap_happened = false;
            std::vector<int> delete_list;
            for (int i = convex.size()-2; i > count; i--)
            {
                // 获取两个坐标点
                Point b = convex[i];
                Point c = convex[i+1];

                // 两个坐标点相同时，消除重复的坐标点
                if (b == c)
                {
                    delete_list.push_back(i+1);
                    continue;
                }

                // 获取两个坐标点与基准点形成的向量
                Point ab = Get_Vector(a, b);
                Point ac = Get_Vector(a, c);

                // 计算两个向量的叉乘，当叉乘为正数时，两个坐标点按照顺时针排序
                operate_t cp = Cross_Product(ab, ac);
                if (cp < 0)
                {
                    swap_happened = true;
                    std::swap(convex[i], convex[i+1]);
                }
                else if (cp == 0)
                {
                    if (b.x > c.x)
                    {
                        swap_happened = true;
                        std::swap(convex[i], convex[i+1]);
                    }
                    else if ((b.x == c.x) || (b.y < c.y))
                    {
                        swap_happened = true;
                        std::swap(convex[i], convex[i+1]);
                    }
                }
            }

            // 消除重复元素
            for (auto x: delete_list)
            {
                convex.erase(convex.begin()+x);
            }
            
            // 如果已经完成排序，则可提前退出循环
            if (swap_happened == false)
            {
                break;
            }
        }

        // 消除重复的基准点
        if (convex.size() > 1)
        {
            if (convex[0] == convex[1])
            {
                convex.erase(convex.begin());
            }
        }

        return convex;
    }

    /**
     * @brief    
     *           计算凸多边形面积
     *           
     * @param    convex:    要计算面积的凸多边形的坐标顶点（要求顶点坐标按顺序排列）
     *           
     * @retval   凸多边形的面积
     *           
     * @date     2024-04-26 Created by HuangJP
     */
    static operate_t Calculate_Area_Of_Convex_Polygon(Polygon convex)
    {
        operate_t area = 0; // 凸多边形面积
        Point a = convex[0]; // 获取基准点

        // 逐个计算基准点指向相邻两个坐标顶点的向量的叉乘，得到两个向量围成的平行四边形的面积
        // 计算方法参考：https://zhuanlan.zhihu.com/p/322265453
        int loop = convex.size() - 1;
        for (int i = 1; i < loop; i++)
        {
            // 获取相邻两个坐标点
            Point b = convex[i];
            Point c = convex[i+1];

            // 获取基准点指向上述坐标点的向量
            Point ab = Get_Vector(a, b);
            Point ac = Get_Vector(a, c);

            // 记录两个向量形成的平行四边形的面积，此面积除以2后，即可得到两个向量围成的三角形的面积，所有三角形面积相加即可得到凸多边形的面积
            area += Cross_Product(ab, ac);
        }

        // 计算并返回多边形面积
        area /= 2;
        return (area < 0 ? -area : area);
    }

    /**
     * @brief    
     *           获取两个凸多边形相交区域形成的凸多边形
     *           
     * @param    convex1, convex2:  要获取相交区域的两个凸多边形（要求凸多边形顶点坐标按顺序排列）
     *           
     * @retval   两个凸多边形相交区域形成的凸多边形的顶点坐标（顶点按顺时针排序）
     *           
     * @date     2024-04-26 Created by HuangJP
     */
    static Polygon Get_Intersecting_Area(Polygon convex1, Polygon convex2)
    {
        Polygon L; // 相交区域形成的凸多边形

        // 获取两个凸多边形相交区域形成的多边形L
        // 获取包含在convex2中的convex1坐标顶点
        for (auto p: convex1)
        {
            if (Is_Point_Inside_Convex_Polygon(p, convex2))
            {
                L.emplace_back(p);
            }
        }

        // 获取包含在convex1中的convex2坐标顶点
        for (auto p: convex2)
        {
            if (Is_Point_Inside_Convex_Polygon(p, convex1))
            {
                L.emplace_back(p);
            }
        }

        // 获取两个凸多边形的交点
        Polygon intersectings = Get_All_Intersecting_Points(convex1, convex2);
        L.insert(L.begin(), intersectings.begin(), intersectings.end());

        // 将交点按顺序排列
        L = Sort_Convex_Polygon_With_Erase(L);

        return L;
    }

    /**
     * @brief    
     *           计算两个凸多边形的交并比
     *           
     * @param    convex1, convex2:  要计算交并比的两个凸多边形（要求凸多边形顶点坐标按顺序排列）
     *           
     * @retval   两个凸多边形的交并比
     *           
     * @date     2024-04-26 Created by HuangJP
     */
    static float Convex_Polygon_IoU(Polygon convex1, Polygon convex2)
    {
        // 首先判断两个凸多边形的最小包围框是否发生碰撞
        if (Bounding_Box_Collision_Detection(convex1, convex2) == false)
        {
            return 0.0f;
        }

        // 步骤一：获取两个凸多边形相交区域形成的凸多边形
        Polygon L = Get_Intersecting_Area(convex1, convex2);

        // 判断相交区域能否围成图形，无法围成图形时，交并比等于0
        if (L.size() < 3)
        {
            return 0.0f;
        }

        // 步骤二：计算相交区域的面积
        operate_t inters = Calculate_Area_Of_Convex_Polygon(L);
        if (inters <= 0)
        {
            return 0.0f; // 相交区域面积等于（或小于）零时，交并比等于0
        }

        // 步骤三：计算两个凸多边形的面积
        operate_t s1 = Calculate_Area_Of_Convex_Polygon(convex1);
        operate_t s2 = Calculate_Area_Of_Convex_Polygon(convex2);

        // 步骤四：计算两个凸多边形的并集面积
        operate_t uni = s1 + s2 - inters;

        // 步骤五：计算和返回交并比
        return (float)inters / (float)uni;
    }

private:
    /**
     * @brief    
     *           判断三个点的转向
     *           
     * @param    p1, p2, p3:    三个点的坐标
     *           
     * @retval   正值表示向左转，负值表示向右转，0表示共线
     *           
     * @date     2024-10-28 Created by HuangJP
     */
    static inline operate_t turn(const Point& p1, const Point& p2, const Point& p3) {
        return Cross_Product(
            Get_Vector(p1, p2),
            Get_Vector(p1, p3)
        );
    }

    /**
     * @brief    
     *           包围框碰撞检测，用于检测两个凸多边形的最小包围框是否发生碰撞
     *           
     * @param    convex1, convex2:  要检测最小包围框是否发生碰撞的两个凸多边形（要求凸多边形顶点坐标按顺序排列）
     *           
     * @retval   true:      两个凸多边形的最小包围框存在碰撞
     * @retval   false:     两个凸多边形的最小包围框未碰撞
     *           
     * @date     2024-04-28 Created by HuangJP
     */
    static bool Bounding_Box_Collision_Detection(Polygon convex1, Polygon convex2)
    {
        // 获取两个凸多边形在x轴和y轴上的投影（获取最小包围框）
        operate_t c1_xmin = convex1[0].x;
        operate_t c1_xmax = convex1[0].x;
        operate_t c1_ymin = convex1[0].y;
        operate_t c1_ymax = convex1[0].y;
        for (int i = 1; i < convex1.size(); i++)
        {
            c1_xmin = std::min(c1_xmin, convex1[i].x);
            c1_xmax = std::max(c1_xmax, convex1[i].x);
            c1_ymin = std::min(c1_ymin, convex1[i].y);
            c1_ymax = std::max(c1_ymax, convex1[i].y);
        }

        operate_t c2_xmin = convex2[0].x;
        operate_t c2_xmax = convex2[0].x;
        operate_t c2_ymin = convex2[0].y;
        operate_t c2_ymax = convex2[0].y;
        for (int i = 1; i < convex1.size(); i++)
        {
            c2_xmin = std::min(c2_xmin, convex2[i].x);
            c2_xmax = std::max(c2_xmax, convex2[i].x);
            c2_ymin = std::min(c2_ymin, convex2[i].y);
            c2_ymax = std::max(c2_ymax, convex2[i].y);
        }

        // 判断x轴方向是否为分离轴
        operate_t xmin = std::max(c1_xmin, c2_xmin);
        operate_t xmax = std::min(c1_xmax, c2_xmax);
        if (xmin >= xmax)
        {
            return false;
        }

        // 判断y轴方向是否为分离轴
        operate_t ymin = std::max(c1_ymin, c2_ymin);
        operate_t ymax = std::min(c1_ymax, c2_ymax);
        if (ymin >= ymax)
        {
            return false;
        }

        // 未发现分离轴，两个凸多边形的最小包围框发生碰撞
        return true;
    }
};

#endif
//-----------------------------------------------------------------------------
//  End of file