//-----------------------------------------------------------------------------
//  Includes

#include <stdint.h>
#include <new>
#include <memory>
#include <iostream>

#include "AIEngineCore.hpp"
#include "Log.hpp"
#include "Version.hpp"
#include "NIUManager.hpp"
//-----------------------------------------------------------------------------
//  Definitions

using namespace std;
//-----------------------------------------------------------------------------
//  Function declarations


//-----------------------------------------------------------------------------
//  Global variables

std::unordered_map<std::string, std::shared_ptr<NIUManager>> NIUManager::managers;
//-----------------------------------------------------------------------------
//  Functions

/**
 * @brief    
 *           获取AIEngine版本号
 *           
 * @retval   版本号
 *           
 * @date     2024-03-06 Created by HuangJP
 */
const char *ai_engine_get_version(void)
{
    return _ai_engine_get_version();
}

/**
 * @brief    
 *           获取AIEngine编译选项
 *           
 * @retval   AIEngine编译选项
 *           
 * @date     2024-03-06 Created by HuangJP
 */
const char *ai_engine_get_build_flags(void)
{
    return _ai_engine_get_build_flags();
}

/**
 * @brief    
 *           初始化模型
 *           
 * @param    model_tag:         模型标签
 * @param    num_sessions:      创建会话个数
 *           
 * @retval   错误码
 *           
 * @date     2024-03-06 Created by HuangJP
 */
int ai_engine_model_init(const char *model_tag, int num_sessions)
{
    // 判断传入参数是否合法
    if (model_tag == nullptr)
    {
        LOGE("A nullptr found in user input");
        return AIENGINE_INPUT_DATA_ERROR;
    }

    // 判断模型是否不存在
    if (BaseModelLoader::Check_Model_Exist(model_tag) == false)
    {
        return AIENGINE_MODEL_NOT_FOUND_ERROR;
    }

    // 创建NIU管理器并返回创建结果
    return NIUManager::Create(model_tag, num_sessions);
}

/**
 * @brief    
 *           销毁会话
 *           
 * @param    model_tag:         模型标签
 *           
 * @retval   错误码
 *           
 * @date     2024-08-30 Created by HuangJP
 */
int ai_engine_destroy_sessions(const char *model_tag)
{
    // 判断传入参数是否合法
    if (model_tag == nullptr)
    {
        LOGE("A nullptr found in user input");
        return AIENGINE_INPUT_DATA_ERROR;
    }

    // 判断模型是否不存在
    if (BaseModelLoader::Check_Model_Exist(model_tag) == false)
    {
        return AIENGINE_MODEL_NOT_FOUND_ERROR;
    }

    // 销毁会话
    return NIUManager::Destroy(model_tag);
}

/**
 * @brief    
 *           执行目标检测任务
 *           
 * @param    model_tag:         模型标签
 * @param    image_info:        图片数据信息
 * @param    det_results:       返回结果保存地址
 *           
 * @retval   错误码
 *           
 * @date     2024-03-06 Created by HuangJP
 */
int ai_engine_task_detection(const char *model_tag, struct ImageInfo *image_info, std::vector<DetectionBBoxInfo> *det_results)
{
    // 判断传入参数是否合法
    if ((model_tag == nullptr)
        || (image_info == nullptr)
        || (det_results == nullptr))
    {
        LOGE("A nullptr found in user input");
        return AIENGINE_INPUT_DATA_ERROR; // 输入数据为空指针
    }

    // 判断模型是否不存在
    if (BaseModelLoader::Check_Model_Exist(model_tag) == false)
    {
        return AIENGINE_MODEL_NOT_FOUND_ERROR; // 找不到模型
    }

    // 判断模型是否不适用于目标任务类型
    if (BaseModelLoader::Check_Model_Task_Type(model_tag, TASK_TYPE_DETECTION) == false)
    {
        return AIENGINE_TASK_TYPE_ERROR; // 模型不适用于目标任务类型
    }

    // 获取NIU管理器
    auto manager = NIUManager::Get_Instance(model_tag);
    if (manager == nullptr)
    {
        LOGE("Failed to get NIU manager");
        return AIENGINE_INSTANCE_NOT_FOUND_ERROR; // 获取NIU管理器失败
    }

    // 获取NIU
    std::shared_ptr<BaseNIU> niu = manager->Get_Idle();
    if (niu == nullptr)
    {
        LOGE("Failed to get idle NIU");
        return AIENGINE_INSTANCE_NOT_FOUND_ERROR; // 获取不到空闲NIU
    }

    // 执行目标检测
    int rev = niu->Task_Detection(image_info, det_results);

    // 返还NIU
    int rs = manager->Give_Back(niu);
    if (rs != AIENGINE_NO_ERROR)
    {
        return rs;
    }

    return rev; // 返回检测结果
}

/**
 * @brief    
 *           执行语义分割任务
 *           
 * @param    model_tag:         模型标签
 * @param    image_info:        图片数据信息
 * @param    seg_mask:          指向掩码图像存储地址的指针
 * @param    contours:          轮廓坐标
 * @param    classes:           类别信息
 *           
 * @retval   错误码
 *           
 * @date     2024-10-22 Created by HuangJP
 */
int ai_engine_task_semantic_segmentation(const char *model_tag, struct ImageInfo *image_info, struct ImageInfo *seg_mask, std::vector<std::vector<Point>> *contours, std::vector<int> *classes)
{
    // 判断传入参数是否合法
    if ((model_tag == nullptr)
        || (image_info == nullptr))
    {
        LOGE("A nullptr found in user input");
        return AIENGINE_INPUT_DATA_ERROR; // 输入数据为空指针
    }

    // 判断模型是否不存在
    if (BaseModelLoader::Check_Model_Exist(model_tag) == false)
    {
        return AIENGINE_MODEL_NOT_FOUND_ERROR; // 找不到模型
    }

    // 判断模型是否不适用于目标任务类型
    if (BaseModelLoader::Check_Model_Task_Type(model_tag, TASK_TYPE_SEMANTIC_SEGMENTATION) == false)
    {
        return AIENGINE_TASK_TYPE_ERROR; // 模型不适用于目标任务类型
    }

    // 获取NIU管理器
    auto manager = NIUManager::Get_Instance(model_tag);
    if (manager == nullptr)
    {
        LOGE("Failed to get NIU manager");
        return AIENGINE_INSTANCE_NOT_FOUND_ERROR; // 获取NIU管理器失败
    }

    // 获取NIU
    std::shared_ptr<BaseNIU> niu = manager->Get_Idle();
    if (niu == nullptr)
    {
        LOGE("Failed to get idle NIU");
        return AIENGINE_INSTANCE_NOT_FOUND_ERROR; // 获取不到空闲NIU
    }

    // 执行语义分割
    int rev = niu->Task_Semantic_Segmentation(image_info, seg_mask, contours, classes);

    // 返还NIU
    int rs = manager->Give_Back(niu);
    if (rs != AIENGINE_NO_ERROR)
    {
        return rs;
    }

    return rev;
}

/**
 * @brief    
 *           执行字符检测任务
 *           
 * @param    model_tag:         模型标签
 * @param    image_info:        图片数据信息
 * @param    ocr_det_results:   字符检测返回结果保存地址
 *           
 * @retval   错误码
 *           
 * @date     2024-08-14 Created by HuangJP
 */
int ai_engine_task_ocr_detection(const char *model_tag, struct ImageInfo *image_info, std::vector<QuadPoints> *ocr_det_results)
{
    // 判断传入参数是否合法
    if ((model_tag == nullptr)
        || (image_info == nullptr)
        || (ocr_det_results == nullptr))
    {
        LOGE("A nullptr found in user input");
        return AIENGINE_INPUT_DATA_ERROR; // 输入数据为空指针
    }

    // 判断模型是否不存在
    if (BaseModelLoader::Check_Model_Exist(model_tag) == false)
    {
        return AIENGINE_MODEL_NOT_FOUND_ERROR; // 找不到模型
    }

    // 判断模型是否不适用于目标任务类型
    if (BaseModelLoader::Check_Model_Task_Type(model_tag, TASK_TYPE_OCR_DETECTION) == false)
    {
        return AIENGINE_TASK_TYPE_ERROR; // 模型不适用于目标任务类型
    }

    // 获取NIU管理器
    auto manager = NIUManager::Get_Instance(model_tag);
    if (manager == nullptr)
    {
        LOGE("Failed to get NIU manager");
        return AIENGINE_INSTANCE_NOT_FOUND_ERROR; // 获取NIU管理器失败
    }

    // 获取NIU
    std::shared_ptr<BaseNIU> niu = manager->Get_Idle();
    if (niu == nullptr)
    {
        LOGE("Failed to get idle NIU");
        return AIENGINE_INSTANCE_NOT_FOUND_ERROR; // 获取不到空闲NIU
    }

    // 执行字符检测
    int rev = niu->Task_OCR_Detection(image_info, ocr_det_results);

    // 返还NIU
    int rs = manager->Give_Back(niu);
    if (rs != AIENGINE_NO_ERROR)
    {
        return rs;
    }

    return rev; // 返回字符检测结果
}

/**
 * @brief    
 *           执行字符识别任务
 *           
 * @param    model_tag:         模型标签
 * @param    image_info:        图片数据信息
 * @param    ocr_rec_results:   字符识别返回结果保存地址
 *           
 * @retval   错误码
 *           
 * @date     2024-08-14 Created by HuangJP
 */
int ai_engine_task_ocr_recognize(const char *model_tag, struct ImageInfo *image_info, std::vector<OCRCharacter> *ocr_rec_results)
{
    // 判断传入参数是否合法
    if ((model_tag == nullptr)
        || (image_info == nullptr)
        || (ocr_rec_results == nullptr))
    {
        LOGE("A nullptr found in user input");
        return AIENGINE_INPUT_DATA_ERROR; // 输入数据为空指针
    }

    // 判断模型是否不存在
    if (BaseModelLoader::Check_Model_Exist(model_tag) == false)
    {
        return AIENGINE_MODEL_NOT_FOUND_ERROR; // 找不到模型
    }

    // 判断模型是否不适用于目标任务类型
    if (BaseModelLoader::Check_Model_Task_Type(model_tag, TASK_TYPE_OCR_RECOGNIZE) == false)
    {
        return AIENGINE_TASK_TYPE_ERROR; // 模型不适用于目标任务类型
    }

    // 获取NIU管理器
    auto manager = NIUManager::Get_Instance(model_tag);
    if (manager == nullptr)
    {
        LOGE("Failed to get NIU manager");
        return AIENGINE_INSTANCE_NOT_FOUND_ERROR; // 获取NIU管理器失败
    }

    // 获取NIU
    std::shared_ptr<BaseNIU> niu = manager->Get_Idle();
    if (niu == nullptr)
    {
        LOGE("Failed to get idle NIU");
        return AIENGINE_INSTANCE_NOT_FOUND_ERROR; // 获取不到空闲NIU
    }

    // 执行字符检测
    int rev = niu->Task_OCR_Recognize(image_info, ocr_rec_results);

    // 返还NIU
    int rs = manager->Give_Back(niu);
    if (rs != AIENGINE_NO_ERROR)
    {
        return rs;
    }

    return rev; // 返回字符识别结果
}
//-----------------------------------------------------------------------------
//  End of file