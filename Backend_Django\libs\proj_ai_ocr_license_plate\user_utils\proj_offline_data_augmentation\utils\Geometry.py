import math

class Geometry():
    # 计算两个向量叉乘
    def cross_product(self, v1: list, v2: list):
        return v1[0]*v2[1] - v2[0]*v1[1]

    # 判断点是否在凸边形内
    def is_point_inside(self, point: list, convex: list) -> bool:
        b = convex[-1]
        c = convex[0]
        ab = [b[i]-point[i] for i in range(0, 2)]
        ac = [c[i]-point[i] for i in range(0, 2)]
        cp = self.cross_product(ab, ac)
        max_val = cp
        min_val = cp

        for i in range(0, len(convex)-1):
            b = convex[i]
            c = convex[i+1]
            ab = [b[i]-point[i] for i in range(0, 2)]
            ac = [c[i]-point[i] for i in range(0, 2)]
            cp = self.cross_product(ab, ac)
            max_val = max(max_val, cp)
            min_val = min(min_val, cp)

        if max_val * min_val < 0:
            return False
        
        return True

    # 判断两条边是否相交
    def is_edges_intersecting(self, a: list, b: list, c: list, d: list) -> bool:
            ab = [b[i]-a[i] for i in range(0, 2)]
            ac = [c[i]-a[i] for i in range(0, 2)]
            ad = [d[i]-a[i] for i in range(0, 2)]

            cd = [d[i]-c[i] for i in range(0, 2)]
            ca = [a[i]-c[i] for i in range(0, 2)]
            cb = [b[i]-c[i] for i in range(0, 2)]

            if self.cross_product(ac, ab) * self.cross_product(ad, ab) >= 0:
                return False

            if self.cross_product(ca, cd) * self.cross_product(cb, cd) >= 0:
                return False
            
            return True

    # 找两条边的交点坐标
    def find_intersecting_points(self, a: list, b: list, convex: list) -> list:
        points = []
        
        convex.append(convex[0])
        for i in range(0, len(convex)-1):
            c = convex[i]
            d = convex[i+1]

            if self.is_edges_intersecting(a, b, c, d):
                # 计算交点坐标
                ac = [c[i]-a[i] for i in range(0, 2)]
                ad = [d[i]-a[i] for i in range(0, 2)]
                ab = [b[i]-a[i] for i in range(0, 2)]
                cd = [d[i]-c[i] for i in range(0, 2)]
                div = self.cross_product(ac, ad) / self.cross_product(ab, cd)
                p = [a[i]+div*ab[i] for i in range(0, 2)]
                points.append(p)
        
        return points

    # 找到两个凸边形所有相交的点
    def find_all_intersecting_points(self, convex1: list, convex2: list) -> list:
        points = []
        convex1.append(convex1[0])
        for i in range(0, len(convex1)-1):
            a = convex1[i]
            b = convex1[i+1]
            points.extend(self.find_intersecting_points(a, b, convex2))
        
        return points

    # 多边形排序
    def sort_convex(self, convex: list, anticlockwise=True) -> list:
        if len(convex) < 3:
            return []
        a = convex[0]
        for count in range(0, len(convex)):
            # print(convex, "\n")
            for i in range(len(convex)-2, count, -1):
                b = convex[i+1]
                c = convex[i]
                ab = [b[i]-a[i] for i in range(0, 2)]
                ac = [c[i]-a[i] for i in range(0, 2)]
                cp = self.cross_product(ab, ac)
                # print("==>", b, c, cp)
                if anticlockwise == True:
                    if cp < 0:
                        tmp = convex[i]
                        convex[i] = convex[i+1]
                        convex[i+1] = tmp
                    if cp == 0:
                        if c[0] < b[0] or c[1] < b[1]:
                            tmp = convex[i]
                            convex[i] = convex[i+1]
                            convex[i+1] = tmp
        
        return convex

    # 获取两个多边形的交集区域
    def get_intersecting(self, A: list, B: list):
        L = []
        for point in A:
            if self.is_point_inside(point, B):
                L.append(point)

        for point in B:
            if self.is_point_inside(point, A):
                L.append(point)

        L.extend(self.find_all_intersecting_points(A, B))

        L = self.sort_convex(L)

        return L

    # 计算多边形面积
    def calculate_convex_area(self, convex: list) -> float:
        area = 0.0 # 面积大小
        a = convex[0] # 基准点
        
        for i in range(1, len(convex)-1):
            # 获取相邻两个点
            b = convex[i]
            c = convex[i+1]

            # 获取基准点指向上述坐标的向量
            ab = [b[i]-a[i] for i in range(0, 2)]
            ac = [c[i]-a[i] for i in range(0, 2)]

            # 记录两个向量形成的平行四边形的面积，此面积除以2后，即可得到两个向量围成的三角形的面积，所有三角形面积相加即可得到凸多边形的面积
            area += self.cross_product(ab, ac)

        return math.fabs(area/2)