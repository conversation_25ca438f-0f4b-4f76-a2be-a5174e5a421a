import React, { useState, useRef } from 'react';
import { Modal, Form, Input, Button, Typography, Space } from 'antd';
import { LockOutlined, UserOutlined } from '@ant-design/icons';
import { useAdmin } from '../contexts/AdminContext';

const { Title, Text } = Typography;

interface AdminLoginModalProps {
  visible: boolean;
  onCancel: () => void;
  onSuccess?: () => void;
}

const AdminLoginModal: React.FC<AdminLoginModalProps> = ({
  visible,
  onCancel,
  onSuccess
}) => {
  const [form] = Form.useForm();
  const { login, isLoading } = useAdmin();
  const [loginAttempting, setLoginAttempting] = useState(false);

  // 使用useRef防止重复调用onSuccess回调
  const hasCalledSuccessRef = useRef<boolean>(false);

  const handleLogin = async (values: { password: string }) => {
    try {
      setLoginAttempting(true);
      const success = await login(values.password);

      if (success) {
        form.resetFields();
        onCancel(); // 关闭模态框

        // 防止重复调用onSuccess回调
        if (!hasCalledSuccessRef.current) {
          console.log('AdminLoginModal: 调用onSuccess回调');
          hasCalledSuccessRef.current = true;
          onSuccess?.(); // 调用成功回调
        } else {
          console.log('AdminLoginModal: onSuccess已被调用，跳过重复调用');
        }
      }
    } catch (error) {
      console.error('登录处理失败:', error);
    } finally {
      setLoginAttempting(false);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    // 重置成功回调标志，以便下次登录时能正常调用
    hasCalledSuccessRef.current = false;
    onCancel();
  };

  return (
    <Modal
      title={
        <div style={{
          textAlign: 'center',
          padding: '16px 0 8px 0',
          background: 'linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%)',
          margin: '-24px -24px 0 -24px',
          borderRadius: '8px 8px 0 0'
        }}>
          <div style={{
            width: '64px',
            height: '64px',
            borderRadius: '50%',
            background: 'linear-gradient(135deg, #1890ff 0%, #096dd9 100%)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            margin: '0 auto 16px auto',
            boxShadow: '0 4px 16px rgba(24, 144, 255, 0.3)'
          }}>
            <UserOutlined style={{ color: '#ffffff', fontSize: '28px' }} />
          </div>
          <Title level={3} style={{
            margin: 0,
            color: '#1890ff',
            fontWeight: 600,
            fontSize: '24px'
          }}>
            管理员登录
          </Title>
        </div>
      }
      open={visible}
      onCancel={handleCancel}
      footer={null}
      width={460}
      centered
      maskClosable={false}
      destroyOnHidden
      styles={{
        header: {
          padding: 0,
          borderBottom: 'none'
        },
        body: {
          padding: '32px 32px 24px 32px'
        },
        content: {
          borderRadius: '12px',
          overflow: 'hidden',
          boxShadow: '0 12px 48px rgba(0, 0, 0, 0.12)'
        }
      }}
    >
      <div style={{ textAlign: 'center', marginBottom: '24px' }}>
        <Text type="secondary" style={{ fontSize: '14px', lineHeight: '1.5' }}>
          请输入管理员密码以访问管理功能
        </Text>
      </div>

      <Form
        form={form}
        name="admin_login"
        onFinish={handleLogin}
        autoComplete="off"
        layout="vertical"
        size="large"
      >
        <Form.Item
          name="password"
          label={
            <span style={{
              fontWeight: 600,
              fontSize: '15px',
              color: '#333'
            }}>
              管理员密码
            </span>
          }
          rules={[
            { required: true, message: '请输入管理员密码' },
            { min: 1, message: '密码不能为空' }
          ]}
          style={{ marginBottom: '32px' }}
        >
          <Input.Password
            prefix={<LockOutlined style={{ color: '#1890ff', fontSize: '16px' }} />}
            placeholder="请输入管理员密码"
            autoFocus
            size="large"
            onPressEnter={() => form.submit()}
            style={{
              borderRadius: '8px',
              fontSize: '15px',
              height: '48px',
              border: '2px solid #e8f4fd',
              boxShadow: '0 2px 8px rgba(24, 144, 255, 0.1)'
            }}
            styles={{
              input: {
                fontSize: '15px'
              }
            }}
          />
        </Form.Item>

        <Form.Item style={{ marginBottom: '16px' }}>
          <Space
            direction="vertical"
            style={{ width: '100%' }}
            size="middle"
          >
            <Button
              type="primary"
              htmlType="submit"
              loading={loginAttempting || isLoading}
              block
              size="large"
              style={{
                height: '48px',
                borderRadius: '8px',
                fontWeight: 600,
                fontSize: '16px',
                boxShadow: '0 4px 12px rgba(24, 144, 255, 0.3)',
                background: 'linear-gradient(135deg, #1890ff 0%, #096dd9 100%)',
                border: 'none'
              }}
            >
              {loginAttempting || isLoading ? '登录中...' : '立即登录'}
            </Button>
            <Button
              onClick={handleCancel}
              block
              size="large"
              style={{
                height: '44px',
                borderRadius: '8px',
                fontWeight: 500,
                fontSize: '15px',
                color: '#666',
                borderColor: '#d9d9d9',
                background: '#fafafa'
              }}
            >
              取消
            </Button>
          </Space>
        </Form.Item>
      </Form>

      <div style={{
        marginTop: '24px',
        textAlign: 'center',
        padding: '16px 20px',
        background: 'linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%)',
        borderRadius: '8px',
        border: '1px solid #bae7ff'
      }}>
        <Text style={{
          fontSize: '13px',
          lineHeight: '1.5',
          color: '#1890ff',
          fontWeight: 500
        }}>
          🔧 管理员功能包括模型管理和示例图片管理
        </Text>
      </div>
    </Modal>
  );
};

export default AdminLoginModal;
