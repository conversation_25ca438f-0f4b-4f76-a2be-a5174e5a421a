// Frontend/src/components/ImageDisplay/hooks/useImagePanZoom.ts
import { useRef, useCallback, useEffect, RefObject } from 'react';
import { ReactZoomPanPinchRef } from 'react-zoom-pan-pinch';
import { ImageInfo, useImageWorkspace } from '../../../contexts/ImageWorkspaceContext';

interface UseImagePanZoomProps {
  currentImageInfo: ImageInfo | null;
  // isSelectingRoi: boolean; // To disable pan/zoom when selecting ROI
  imageRef: RefObject<HTMLImageElement | null>; // Ref to the actual <img> element
  svgOverlayRef: RefObject<SVGSVGElement | null>; // Ref to the SVG overlay
}

export const useImagePanZoom = ({
  currentImageInfo,
  // isSelectingRoi,
  imageRef,
  svgOverlayRef,
}: UseImagePanZoomProps) => {
  const transformWrapperRef = useRef<ReactZoomPanPinchRef | null>(null);
  const { registerDisplayedViewGetter } = useImageWorkspace(); // Get the registration function

  // Effect for initializing/resetting transform when image changes
  useEffect(() => {
    const wrapperInstance = transformWrapperRef.current;
    if (currentImageInfo?.url && wrapperInstance?.instance?.wrapperComponent) {
      const performInitialTransform = () => {
        const container = wrapperInstance.instance.wrapperComponent;
        if (!container || !currentImageInfo.width || !currentImageInfo.height) return;

        const { width: containerWidth, height: containerHeight } = container.getBoundingClientRect();
        const { width: imageWidth, height: imageHeight } = currentImageInfo;

        if (imageWidth === 0 || imageHeight === 0 || containerWidth === 0 || containerHeight === 0) return;

        const scaleX = containerWidth / imageWidth;
        const scaleY = containerHeight / imageHeight;
        let scale = Math.min(scaleX, scaleY);

        const minScaleProp = 0.1;
        const maxScaleProp = 15; // Match what's in ImageDisplay.tsx
        scale = Math.max(minScaleProp, Math.min(scale, maxScaleProp));
        
        const scaledImageWidth = imageWidth * scale;
        const scaledImageHeight = imageHeight * scale;

        const positionX = (containerWidth - scaledImageWidth) / 2;
        const positionY = (containerHeight - scaledImageHeight) / 2;
        
        // Use a slight delay to ensure the image is rendered and dimensions are stable
        const timer = setTimeout(() => {
            if (transformWrapperRef.current) { // Check ref again inside timeout
                 transformWrapperRef.current.setTransform(positionX, positionY, scale, 0, 'easeOut');
            }
        }, 50); // A small delay can help
        return () => clearTimeout(timer);
      };
      performInitialTransform();
    } else if (!currentImageInfo?.url && wrapperInstance) {
        // If no image, reset transform
        const timer = setTimeout(() => {
            if (transformWrapperRef.current) {
                transformWrapperRef.current.resetTransform(0, 'easeOut');
            }
        }, 50);
        return () => clearTimeout(timer);
    }
  }, [currentImageInfo]); // Rerun when currentImageInfo changes

  const captureDisplayedViewAsDataURL = useCallback(async (): Promise<string | null> => {
    if (!currentImageInfo || !transformWrapperRef.current?.instance || !imageRef.current) {
      console.warn('Cannot capture view: missing image info, transform wrapper, or image element.');
      return null;
    }

    const offscreenCanvas = document.createElement('canvas');
    const ctx = offscreenCanvas.getContext('2d');
    if (!ctx) {
      console.error('Failed to get 2D context from offscreen canvas.');
      return null;
    }

    const { instance } = transformWrapperRef.current;
    const { scale, positionX, positionY } = instance.transformState;
    const imgElement = imageRef.current;

    const wrapperEl = instance.wrapperComponent;
    if (!wrapperEl) {
        console.error('Wrapper component not found in transform instance.');
        return null;
    }
    const viewportWidth = wrapperEl.clientWidth;
    const viewportHeight = wrapperEl.clientHeight;

    offscreenCanvas.width = viewportWidth;
    offscreenCanvas.height = viewportHeight;
    
    ctx.clearRect(0, 0, offscreenCanvas.width, offscreenCanvas.height); 

    // Draw the image
    ctx.drawImage(
      imgElement,
      positionX, 
      positionY, 
      imgElement.naturalWidth * scale, 
      imgElement.naturalHeight * scale 
    );

    // Draw the SVG overlay
    if (svgOverlayRef.current) {
      try {
        const svgElement = svgOverlayRef.current;
        // Ensure SVG has dimensions, otherwise it might not draw correctly
        if (svgElement.width.baseVal.value === 0 || svgElement.height.baseVal.value === 0) {
            console.warn("SVG overlay has zero dimensions, skipping draw to canvas.");
        } else {
            const svgString = new XMLSerializer().serializeToString(svgElement);
            // More robust btoa for unicode:
            const svgDataUrl = `data:image/svg+xml;base64,${btoa(unescape(encodeURIComponent(svgString)))}`;
            
            const svgImage = new Image();
            
            await new Promise<void>((resolve, reject) => {
              svgImage.onload = () => {
                // Draw SVG at the same transformed position and scale as the main image content
                // The SVG's own viewBox handles its internal scaling.
                // We draw the entire SVG viewport onto the canvas, matching the image's pan/zoom.
                ctx.drawImage(
                  svgImage,
                  positionX, // SVG is positioned relative to the panned/zoomed image
                  positionY,
                  svgElement.width.baseVal.value * scale, // Scale the SVG's rendered size
                  svgElement.height.baseVal.value * scale
                );
                resolve();
              };
              svgImage.onerror = (err) => {
                console.error('Error loading SVG as image for canvas drawing:', err);
                reject(err); 
              };
              svgImage.src = svgDataUrl;
            });
        }
      } catch (error) {
        console.error('Error drawing SVG overlay to canvas:', error);
        // Don't return null here, let the image part be returned if SVG fails
      }
    }
    return offscreenCanvas.toDataURL('image/png');
  }, [currentImageInfo, imageRef, svgOverlayRef]); // transformWrapperRef is stable

  // Register the capture function with the context
  useEffect(() => {
    if (registerDisplayedViewGetter) {
      registerDisplayedViewGetter(captureDisplayedViewAsDataURL);
      // console.log('useImagePanZoom: Registered captureDisplayedViewAsDataURL');
    }
    return () => {
      if (registerDisplayedViewGetter) {
        registerDisplayedViewGetter(null); 
        // console.log('useImagePanZoom: Unregistered captureDisplayedViewAsDataURL');
      }
    };
  }, [registerDisplayedViewGetter, captureDisplayedViewAsDataURL]);

  // Expose control functions if needed
  const zoomIn = useCallback(() => transformWrapperRef.current?.zoomIn(), []);
  const zoomOut = useCallback(() => transformWrapperRef.current?.zoomOut(), []);
  const resetTransform = useCallback(() => {
    // Re-apply initial transform logic for reset
     const wrapperInstance = transformWrapperRef.current;
     if (currentImageInfo?.url && wrapperInstance?.instance?.wrapperComponent) {
        const container = wrapperInstance.instance.wrapperComponent;
        if (!container || !currentImageInfo.width || !currentImageInfo.height) return;
        const { width: containerWidth, height: containerHeight } = container.getBoundingClientRect();
        const { width: imageWidth, height: imageHeight } = currentImageInfo;
        if (imageWidth === 0 || imageHeight === 0 || containerWidth === 0 || containerHeight === 0) return;
        const scaleX = containerWidth / imageWidth;
        const scaleY = containerHeight / imageHeight;
        let scale = Math.min(scaleX, scaleY);
        const minScaleProp = 0.1;
        const maxScaleProp = 15;
        scale = Math.max(minScaleProp, Math.min(scale, maxScaleProp));
        const scaledImageWidth = imageWidth * scale;
        const scaledImageHeight = imageHeight * scale;
        const positionX = (containerWidth - scaledImageWidth) / 2;
        const positionY = (containerHeight - scaledImageHeight) / 2;
        transformWrapperRef.current?.setTransform(positionX, positionY, scale, 0, 'easeOut');
     } else if (wrapperInstance) {
        transformWrapperRef.current?.resetTransform(0, 'easeOut');
     }
  }, [currentImageInfo]);


  return {
    transformWrapperRef, // The ref to be passed to TransformWrapper
    captureDisplayedViewAsDataURL, // Explicitly return if needed elsewhere, though context handles it
    zoomIn,
    zoomOut,
    resetTransform,
    // currentZoomScale can be derived in ImageDisplay from transformWrapperRef.current.instance.transformState.scale
  };
};