import cv2
import numpy as np
import base64
import time

class TraditionalFeatureMatcher:
    def __init__(self, algorithm='SIFT', match_ratio_threshold=0.7, min_match_count=10):
        self.algorithm = algorithm.upper()
        self.match_ratio_threshold = match_ratio_threshold
        self.min_match_count = min_match_count
        self.detector = self._create_detector()

    def _create_detector(self):
        if self.algorithm == 'SIFT':
            try:
                return cv2.SIFT_create()
            except Exception as e:
                print(f"SIFT创建失败: {e}, 尝试使用ORB替代...")
                self.algorithm = 'ORB'
                return cv2.ORB_create(nfeatures=2000)
        elif self.algorithm == 'ORB':
            return cv2.ORB_create(nfeatures=2000)
        else:
            raise ValueError(f"不支持的算法: {self.algorithm}. 请选择 'SIFT' 或 'ORB'.")

    def match(self, template_image, target_image, template_roi):
        try:
            # 开始计时 - 特征匹配处理时间
            start_time = time.time()
            
            # 1. 提取模板区域
            x, y, w, h = int(template_roi['x']), int(template_roi['y']), int(template_roi['width']), int(template_roi['height'])
            
            # 如果ROI有效，则裁剪；否则使用整个图像作为模板
            if w > 0 and h > 0:
                template = template_image[y:y+h, x:x+w]
            else:
                template = template_image

            # 确保裁剪后的模板有效
            if template is None or template.size == 0:
                return self._build_error_response("无法从模板图像中提取有效的区域（ROI可能无效或超出边界）。")

            # 2. 转换为灰度图
            template_gray = cv2.cvtColor(template, cv2.COLOR_BGR2GRAY)
            target_gray = cv2.cvtColor(target_image, cv2.COLOR_BGR2GRAY)

            # 3. 检测关键点和描述符
            kp1, des1 = self.detector.detectAndCompute(template_gray, None)
            kp2, des2 = self.detector.detectAndCompute(target_gray, None)

            if des1 is None or des2 is None or len(kp1) == 0 or len(kp2) == 0:
                processing_time = time.time() - start_time
                return self._build_error_response("无法在模板或目标图像中提取特征描述符。", len(kp1), len(kp2), 0, processing_time)

            # 4. 特征匹配
            if self.algorithm == 'SIFT':
                matcher = cv2.FlannBasedMatcher(dict(algorithm=1, trees=5), dict(checks=50))
            else: # ORB
                matcher = cv2.BFMatcher(cv2.NORM_HAMMING, crossCheck=False)
            
            matches = matcher.knnMatch(des1, des2, k=2)

            # 5. 筛选优质匹配点
            good_matches = []
            for match_pair in matches:
                if len(match_pair) == 2:
                    m, n = match_pair
                    if m.distance < self.match_ratio_threshold * n.distance:
                        good_matches.append(m)

            keypoints_info = {
                "template_keypoints": len(kp1),
                "target_keypoints": len(kp2),
                "good_matches": len(good_matches)
            }

            if len(good_matches) < self.min_match_count:
                processing_time = time.time() - start_time
                return self._build_error_response(
                    f"优质匹配点数量不足 ({len(good_matches)} < {self.min_match_count})，无法计算变换矩阵。",
                    template_kp=len(kp1),
                    target_kp=len(kp2),
                    good_matches_count=len(good_matches),
                    processing_time=processing_time
                )

            # 6. 计算单应性矩阵
            src_pts = np.float32([kp1[m.queryIdx].pt for m in good_matches]).reshape(-1, 1, 2)
            dst_pts = np.float32([kp2[m.trainIdx].pt for m in good_matches]).reshape(-1, 1, 2)
            
            M, mask = cv2.findHomography(src_pts, dst_pts, cv2.RANSAC, 5.0)

            if M is None:
                processing_time = time.time() - start_time
                return self._build_error_response(
                    "无法计算单应性矩阵，可能是匹配点共线或存在其他问题。",
                    template_kp=len(kp1),
                    target_kp=len(kp2),
                    good_matches_count=len(good_matches),
                    processing_time=processing_time
                )

            # 7. 定位模板
            h_template, w_template = template_gray.shape
            template_corners = np.float32([[0, 0], [w_template, 0], [w_template, h_template], [0, h_template]]).reshape(-1, 1, 2)
            transformed_corners = cv2.perspectiveTransform(template_corners, M)
            
            # 8. 计算结果
            corners = transformed_corners.reshape(-1, 2).tolist()
            center_x = np.mean([c[0] for c in corners])
            center_y = np.mean([c[1] for c in corners])
            
            dx = corners[1][0] - corners[0][0]
            dy = corners[1][1] - corners[0][1]
            angle = np.arctan2(dy, dx) * 180 / np.pi

            # 计算特征匹配处理耗时（不包括可视化）
            processing_time = time.time() - start_time

            # 9. 可视化（这部分不计入处理时间）
            visualized_image = self._visualize_results(target_image, transformed_corners)
            _, buffer = cv2.imencode('.jpg', visualized_image)
            image_base64 = base64.b64encode(buffer).decode('utf-8')

            return {
                "status": "success",
                "message": "特征点匹配成功",
                "algorithm_used": self.algorithm,
                "keypoints_info": keypoints_info,
                "match_result": {
                    "box_corners": corners,
                    "center_point": [center_x, center_y],
                    "angle": angle
                },
                "image_base64": image_base64,
                "processing_time": processing_time
            }

        except Exception as e:
            # 如果在处理过程中发生异常，也要记录已经过去的时间
            processing_time = time.time() - start_time if 'start_time' in locals() else 0.0
            return self._build_error_response(f"处理过程中发生未知错误: {str(e)}", processing_time=processing_time)

    def _build_error_response(self, message, template_kp=0, target_kp=0, good_matches_count=0, processing_time=0.0):
        return {
            "status": "error",
            "message": message,
            "algorithm_used": self.algorithm,
            "keypoints_info": {
                "template_keypoints": template_kp,
                "target_keypoints": target_kp,
                "good_matches": good_matches_count
            },
            "processing_time": processing_time
        }

    def _visualize_results(self, target_image, transformed_corners):
        target_with_box = target_image.copy()
        cv2.polylines(target_with_box, [np.int32(transformed_corners)], True, (0, 255, 0), 3, cv2.LINE_AA)
        return target_with_box