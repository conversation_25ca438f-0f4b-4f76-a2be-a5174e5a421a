# 后端环境变量配置示例
# 复制此文件为 .env 并根据实际情况修改

# =============================================================================
# 网络配置
# =============================================================================

# 局域网IP地址 (逗号分隔)
LAN_IPS=*************

# 端口配置
FRONTEND_DEV_PORT=5173
FRONTEND_PROD_PORT=8080
BACKEND_PORT=9000

# 允许的主机 (逗号分隔)
ALLOWED_HOSTS=localhost,127.0.0.1,*************

# Django绑定配置
DJANGO_BIND_LOCALHOST=false
DJANGO_BIND_HOST=0.0.0.0

# =============================================================================
# 常用配置场景
# =============================================================================

# 1. 本地开发
#    LAN_IPS=localhost
#    BACKEND_PORT=9000
#    DJANGO_BIND_LOCALHOST=true

# 2. 局域网访问
#    LAN_IPS=*************
#    BACKEND_PORT=9000
#    DJANGO_BIND_LOCALHOST=false
#    DJANGO_BIND_HOST=0.0.0.0

# 3. Docker环境
#    BACKEND_PORT=8000
#    DJANGO_BIND_HOST=0.0.0.0
#    ALLOWED_HOSTS=*

# =============================================================================
# 访问地址示例
# =============================================================================

# 本地开发:
# 前端: http://localhost:5173
# 后端: http://localhost:9000

# 局域网访问:
# 前端: http://*************:8080
# 后端: http://*************:9000
