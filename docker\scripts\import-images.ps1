# Simple Docker Images Import Script
# Encoding: UTF-8

param(
    [string]$ImagePath = "./docker-images",
    [switch]$Restart
)

Write-Host "=== Import Docker Images (Simple) ===" -ForegroundColor Green
Write-Host ""

# Check if image path exists
if (-not (Test-Path $ImagePath)) {
    Write-Host "ERROR: Image path not found: $ImagePath" -ForegroundColor Red
    exit 1
}

Write-Host "Looking for images in: $ImagePath" -ForegroundColor Gray
Write-Host ""

# Find image files
$imageFiles = Get-ChildItem $ImagePath -File | Where-Object { 
    $_.Name -like "*.tar" -or $_.Name -like "*.tar.gz" 
}

if ($imageFiles.Count -eq 0) {
    Write-Host "ERROR: No Docker image files found in $ImagePath" -ForegroundColor Red
    Write-Host "Expected files: frontend.tar, backend.tar (or .tar.gz)" -ForegroundColor Yellow
    exit 1
}

Write-Host "Found image files:" -ForegroundColor Cyan
foreach ($file in $imageFiles) {
    $sizeMB = [math]::Round($file.Length / 1MB, 1)
    Write-Host "  $($file.Name) ($sizeMB MB)" -ForegroundColor White
}
Write-Host ""

# Process each image file
foreach ($file in $imageFiles) {
    $filePath = $file.FullName
    $fileName = $file.Name
    
    Write-Host "Processing: $fileName" -ForegroundColor Yellow
    
    if ($fileName -like "*.tar.gz") {
        # Extract compressed file first
        Write-Host "  Extracting compressed file..." -ForegroundColor Gray
        
        # Try using 7-Zip
        $7zipPath = "C:\Program Files\7-Zip\7z.exe"
        if (Test-Path $7zipPath) {
            $tarFileName = $fileName -replace '\.gz$', ''
            $tarFilePath = Join-Path $ImagePath $tarFileName
            
            & $7zipPath x "$filePath" -o"$ImagePath" -y | Out-Null
            
            if (Test-Path $tarFilePath) {
                Write-Host "  Loading Docker image..." -ForegroundColor Gray
                docker load -i "$tarFilePath"
                
                if ($LASTEXITCODE -eq 0) {
                    Write-Host "  OK: $fileName imported successfully" -ForegroundColor Green
                    # Clean up extracted tar file
                    Remove-Item $tarFilePath -ErrorAction SilentlyContinue
                } else {
                    Write-Host "  ERROR: Failed to load $fileName" -ForegroundColor Red
                }
            } else {
                Write-Host "  ERROR: Failed to extract $fileName" -ForegroundColor Red
            }
        } else {
            Write-Host "  ERROR: 7-Zip not found. Please install 7-Zip or extract manually." -ForegroundColor Red
        }
    } elseif ($fileName -like "*.tar") {
        # Load tar file directly
        Write-Host "  Loading Docker image..." -ForegroundColor Gray
        docker load -i "$filePath"
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "  OK: $fileName imported successfully" -ForegroundColor Green
        } else {
            Write-Host "  ERROR: Failed to load $fileName" -ForegroundColor Red
        }
    }
    
    Write-Host ""
}

# Check imported images
Write-Host "Checking imported images..." -ForegroundColor Yellow
$images = docker images | findstr web_ai_vision_app

if ($images) {
    Write-Host "Imported images:" -ForegroundColor Green
    $images | ForEach-Object { Write-Host "  $_" -ForegroundColor White }
} else {
    Write-Host "WARNING: No web_ai_vision_app images found" -ForegroundColor Yellow
}

if ($Restart) {
    Write-Host ""
    Write-Host "Restarting services..." -ForegroundColor Yellow
    docker-compose down
    Start-Sleep -Seconds 2
    docker-compose up -d
    
    Write-Host "OK: Services restarted" -ForegroundColor Green
}

Write-Host ""
Write-Host "=== Import Complete ===" -ForegroundColor Cyan
Write-Host ""
Write-Host "Next steps:" -ForegroundColor Yellow
Write-Host "1. Start services: .\scripts\deploy-hotreload.ps1 -Mode start" -ForegroundColor Gray
Write-Host "2. Check status: .\scripts\deploy-hotreload.ps1 -Mode status" -ForegroundColor Gray
