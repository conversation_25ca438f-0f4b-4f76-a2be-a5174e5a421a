<mxfile host="65bd71144e">
    <diagram name="第 1 页" id="n5ykjBkt9ZKuOkNlslMe">
        <mxGraphModel dx="4666" dy="669" grid="0" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1654" pageHeight="1169" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-200" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#eeeeee;strokeColor=#36393d;" parent="1" vertex="1">
                    <mxGeometry x="-2005" y="756" width="2832" height="1188" as="geometry"/>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-46" value="前端技术栈（Frontend）" style="whiteSpace=wrap;strokeWidth=4;verticalAlign=top;fontStyle=1;fontSize=20;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
                    <mxGeometry x="-1882" y="935" width="350" height="334" as="geometry"/>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-47" value="React 18.x" style="whiteSpace=wrap;strokeWidth=2;fontSize=15;" parent="J3cU0cQ84F11lH7ggRXQ-46" vertex="1">
                    <mxGeometry x="135.1679104477612" y="196.7751937984496" width="88.80597014925371" height="34.95348837209302" as="geometry"/>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-48" value="TypeScript" style="whiteSpace=wrap;strokeWidth=2;fontSize=15;" parent="J3cU0cQ84F11lH7ggRXQ-46" vertex="1">
                    <mxGeometry x="14" y="266" width="88.15" height="49.23" as="geometry"/>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-49" value="Vite&#xa;（项目构建）" style="whiteSpace=wrap;strokeWidth=2;fontSize=15;" parent="J3cU0cQ84F11lH7ggRXQ-46" vertex="1">
                    <mxGeometry x="131" y="266" width="100" height="49.23" as="geometry"/>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-50" value="Ant Design&#xa;（组件库）" style="whiteSpace=wrap;strokeWidth=2;fontSize=15;" parent="J3cU0cQ84F11lH7ggRXQ-46" vertex="1">
                    <mxGeometry x="244.22" y="267" width="88.81" height="48.23" as="geometry"/>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-51" value="React Router&#xa;（路由管理）" style="whiteSpace=wrap;strokeWidth=2;fontSize=15;" parent="J3cU0cQ84F11lH7ggRXQ-46" vertex="1">
                    <mxGeometry x="28.08" y="113.28" width="102.92" height="42.23" as="geometry"/>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-52" value="React Query&#xa;（数据交互、状态管理）" style="whiteSpace=wrap;strokeWidth=2;fontSize=15;" parent="J3cU0cQ84F11lH7ggRXQ-46" vertex="1">
                    <mxGeometry x="171" y="113.28" width="171.37" height="44.72" as="geometry"/>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-53" value="Axios" style="whiteSpace=wrap;strokeWidth=2;fontSize=15;" parent="J3cU0cQ84F11lH7ggRXQ-46" vertex="1">
                    <mxGeometry x="225.015223880597" y="43.99716658756526" width="63.33955223880597" height="34.95348837209302" as="geometry"/>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-54" value="" style="curved=1;startArrow=none;endArrow=block;exitX=0;exitY=0.98;entryX=0.5;entryY=-0.01;rounded=0;endFill=0;fontSize=15;" parent="J3cU0cQ84F11lH7ggRXQ-46" source="J3cU0cQ84F11lH7ggRXQ-47" target="J3cU0cQ84F11lH7ggRXQ-48" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <Array as="points">
                            <mxPoint x="93" y="238"/>
                            <mxPoint x="58" y="252"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-55" value="" style="curved=1;startArrow=none;endArrow=block;exitX=0.5;exitY=0.99;entryX=0.49;entryY=-0.01;rounded=0;endFill=0;fontSize=15;" parent="J3cU0cQ84F11lH7ggRXQ-46" source="J3cU0cQ84F11lH7ggRXQ-47" target="J3cU0cQ84F11lH7ggRXQ-49" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <Array as="points"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-56" value="" style="curved=1;startArrow=none;endArrow=block;exitX=1;exitY=0.98;entryX=0.5;entryY=-0.01;rounded=0;endFill=0;fontSize=15;" parent="J3cU0cQ84F11lH7ggRXQ-46" source="J3cU0cQ84F11lH7ggRXQ-47" target="J3cU0cQ84F11lH7ggRXQ-50" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <Array as="points">
                            <mxPoint x="279" y="241"/>
                            <mxPoint x="288.6194029850746" y="255.6782945736433"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-57" value="" style="curved=1;startArrow=none;endArrow=block;exitX=0.5;exitY=0.99;entryX=0.02;entryY=-0.01;rounded=0;endFill=0;fontSize=15;" parent="J3cU0cQ84F11lH7ggRXQ-46" source="J3cU0cQ84F11lH7ggRXQ-51" target="J3cU0cQ84F11lH7ggRXQ-47" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <Array as="points">
                            <mxPoint x="77.705223880597" y="172.17829457364346"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-58" value="" style="curved=1;startArrow=none;endArrow=block;exitX=0.5;exitY=0.99;entryX=0.99;entryY=-0.01;rounded=0;endFill=0;fontSize=15;" parent="J3cU0cQ84F11lH7ggRXQ-46" source="J3cU0cQ84F11lH7ggRXQ-52" target="J3cU0cQ84F11lH7ggRXQ-47" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <Array as="points">
                            <mxPoint x="260" y="173"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-59" value="" style="curved=1;startArrow=none;endArrow=block;exitX=0.5;exitY=0.99;entryX=0.5;entryY=-0.01;rounded=0;endFill=0;fontSize=15;" parent="J3cU0cQ84F11lH7ggRXQ-46" source="J3cU0cQ84F11lH7ggRXQ-53" target="J3cU0cQ84F11lH7ggRXQ-52" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <Array as="points"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-60" value="后端 (Python + Django)" style="whiteSpace=wrap;strokeWidth=4;verticalAlign=top;fontSize=20;fontStyle=1;fillColor=#f8cecc;strokeColor=#b85450;" parent="1" vertex="1">
                    <mxGeometry x="-1496" y="1496" width="1270" height="362" as="geometry"/>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-61" value="数据库 (SQLite)" style="whiteSpace=wrap;strokeWidth=2;fontStyle=1;fontSize=20;verticalAlign=top;fillColor=#fff2cc;gradientColor=#ffd966;strokeColor=#d6b656;" parent="1" vertex="1">
                    <mxGeometry x="-768" y="1591" width="499" height="230" as="geometry"/>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-145" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;startArrow=classic;startFill=1;shape=flexArrow;" parent="1" source="J3cU0cQ84F11lH7ggRXQ-62" target="J3cU0cQ84F11lH7ggRXQ-72" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-62" value="前端 (React + TypeScript)" style="whiteSpace=wrap;strokeWidth=4;verticalAlign=top;fontStyle=1;fontSize=20;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
                    <mxGeometry x="-1496" y="852" width="1270" height="413" as="geometry"/>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-155" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;exitX=0;exitY=0.5;exitDx=0;exitDy=0;curved=1;" parent="1" source="J3cU0cQ84F11lH7ggRXQ-64" target="J3cU0cQ84F11lH7ggRXQ-63" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-156" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;curved=1;" parent="1" source="J3cU0cQ84F11lH7ggRXQ-64" target="J3cU0cQ84F11lH7ggRXQ-65" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-64" value="用户界面" style="whiteSpace=wrap;strokeWidth=2;fontSize=20;fontStyle=1" parent="1" vertex="1">
                    <mxGeometry x="-887" y="903" width="160" height="54" as="geometry"/>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-137" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;shape=flexArrow;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="J3cU0cQ84F11lH7ggRXQ-72" target="J3cU0cQ84F11lH7ggRXQ-139" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="-213.68421052631584" y="1355.0000000000002" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-138" value="详细说明" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontStyle=1;fontSize=20;" parent="J3cU0cQ84F11lH7ggRXQ-137" vertex="1" connectable="0">
                    <mxGeometry x="-0.0016" y="5" relative="1" as="geometry">
                        <mxPoint y="-1" as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-146" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;startArrow=classic;startFill=1;shape=flexArrow;" parent="1" source="J3cU0cQ84F11lH7ggRXQ-72" target="J3cU0cQ84F11lH7ggRXQ-60" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-72" value="API服务层" style="whiteSpace=wrap;strokeWidth=4;fontStyle=1;fontSize=20;fillColor=#d5e8d4;strokeColor=#82b366;" parent="1" vertex="1">
                    <mxGeometry x="-1069.5" y="1348" width="417" height="64" as="geometry"/>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-73" value="认证系统" style="whiteSpace=wrap;strokeWidth=2;fontSize=15;" parent="1" vertex="1">
                    <mxGeometry x="-741.75" y="1646" width="124" height="54" as="geometry"/>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-74" value="权限管理" style="whiteSpace=wrap;strokeWidth=2;fontSize=15;" parent="1" vertex="1">
                    <mxGeometry x="-580.25" y="1646" width="124" height="54" as="geometry"/>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-76" value="用户数据" style="whiteSpace=wrap;strokeWidth=2;fontSize=15;" parent="1" vertex="1">
                    <mxGeometry x="-741.75" y="1750" width="124" height="54" as="geometry"/>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-141" value="图像处理" style="whiteSpace=wrap;strokeWidth=2;fontStyle=1;fontSize=20;verticalAlign=top;fillColor=#ffcd28;gradientColor=#ffa500;strokeColor=#d79b00;" parent="1" vertex="1">
                    <mxGeometry x="-1461" y="1586" width="379" height="230" as="geometry"/>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-77" value="权限组" style="whiteSpace=wrap;strokeWidth=2;fontSize=15;" parent="1" vertex="1">
                    <mxGeometry x="-572.25" y="1750" width="108" height="54" as="geometry"/>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-79" value="ultralytics库" style="whiteSpace=wrap;strokeWidth=2;fontSize=15;" parent="1" vertex="1">
                    <mxGeometry x="-1444" y="1675" width="164" height="99" as="geometry"/>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-93" value="" style="curved=1;startArrow=none;endArrow=block;exitX=0.5;exitY=1;entryX=0.5;entryY=0;rounded=0;endFill=0;fontSize=15;" parent="1" source="J3cU0cQ84F11lH7ggRXQ-73" target="J3cU0cQ84F11lH7ggRXQ-76" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <Array as="points"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-94" value="" style="curved=1;startArrow=none;endArrow=block;exitX=0.5;exitY=1;entryX=0.5;entryY=0;rounded=0;endFill=0;fontSize=15;" parent="1" source="J3cU0cQ84F11lH7ggRXQ-74" target="J3cU0cQ84F11lH7ggRXQ-77" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <Array as="points"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-136" value="" style="group;strokeWidth=4;strokeColor=#b85450;fillColor=#f8cecc;container=0;" parent="1" vertex="1" connectable="0">
                    <mxGeometry x="-1883" y="1564" width="350" height="294" as="geometry"/>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-140" value="" style="group" parent="1" vertex="1" connectable="0">
                    <mxGeometry x="-82" y="1079" width="871" height="602" as="geometry"/>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-139" value="" style="rounded=0;whiteSpace=wrap;html=1;strokeWidth=4;fillColor=#d5e8d4;strokeColor=#82b366;" parent="J3cU0cQ84F11lH7ggRXQ-140" vertex="1">
                    <mxGeometry x="-15" y="3" width="871" height="602" as="geometry"/>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-1" value="认证API" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=32.92307692307692;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=0;marginBottom=0;fillColor=#6d8764;fontColor=#ffffff;strokeColor=#3A5431;" parent="J3cU0cQ84F11lH7ggRXQ-140" vertex="1">
                    <mxGeometry x="315" y="60" width="203" height="205.9230769230769" as="geometry"/>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-2" style="line;strokeWidth=1;fillColor=none;align=left;verticalAlign=middle;spacingTop=-1;spacingLeft=3;spacingRight=3;rotatable=0;labelPosition=right;points=[];portConstraint=eastwest;strokeColor=inherit;fontSize=15;" parent="J3cU0cQ84F11lH7ggRXQ-1" vertex="1">
                    <mxGeometry y="32.92307692307692" width="203" height="8" as="geometry"/>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-3" value="+login()" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontSize=15;" parent="J3cU0cQ84F11lH7ggRXQ-1" vertex="1">
                    <mxGeometry y="40.92307692307692" width="203" height="33" as="geometry"/>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-4" value="+refresh()" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontSize=15;" parent="J3cU0cQ84F11lH7ggRXQ-1" vertex="1">
                    <mxGeometry y="73.92307692307692" width="203" height="33" as="geometry"/>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-5" value="+register()" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontSize=15;" parent="J3cU0cQ84F11lH7ggRXQ-1" vertex="1">
                    <mxGeometry y="106.92307692307692" width="203" height="33" as="geometry"/>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-6" value="+passwordRecovery()" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontSize=15;" parent="J3cU0cQ84F11lH7ggRXQ-1" vertex="1">
                    <mxGeometry y="139.9230769230769" width="203" height="33" as="geometry"/>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-7" value="+resetPassword()" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontSize=15;" parent="J3cU0cQ84F11lH7ggRXQ-1" vertex="1">
                    <mxGeometry y="172.9230769230769" width="203" height="33" as="geometry"/>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-8" value="用户管理API" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=34.54545454545455;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=0;marginBottom=0;fillColor=#6d8764;fontColor=#ffffff;strokeColor=#3A5431;" parent="J3cU0cQ84F11lH7ggRXQ-140" vertex="1">
                    <mxGeometry x="25" y="356" width="222" height="182.54545454545456" as="geometry"/>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-9" style="line;strokeWidth=1;fillColor=none;align=left;verticalAlign=middle;spacingTop=-1;spacingLeft=3;spacingRight=3;rotatable=0;labelPosition=right;points=[];portConstraint=eastwest;strokeColor=inherit;fontSize=15;" parent="J3cU0cQ84F11lH7ggRXQ-8" vertex="1">
                    <mxGeometry y="34.54545454545455" width="222" height="8" as="geometry"/>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-10" value="+getCurrentUser()" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontSize=15;" parent="J3cU0cQ84F11lH7ggRXQ-8" vertex="1">
                    <mxGeometry y="42.54545454545455" width="222" height="35" as="geometry"/>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-11" value="+getAllUsers()" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontSize=15;" parent="J3cU0cQ84F11lH7ggRXQ-8" vertex="1">
                    <mxGeometry y="77.54545454545455" width="222" height="35" as="geometry"/>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-12" value="+updateUser(user_id)" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontSize=15;" parent="J3cU0cQ84F11lH7ggRXQ-8" vertex="1">
                    <mxGeometry y="112.54545454545455" width="222" height="35" as="geometry"/>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-13" value="+deleteUser(user_id)" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontSize=15;" parent="J3cU0cQ84F11lH7ggRXQ-8" vertex="1">
                    <mxGeometry y="147.54545454545456" width="222" height="35" as="geometry"/>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-14" value="权限API" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=31.733333333333334;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=0;marginBottom=0;fillColor=#6d8764;fontColor=#ffffff;strokeColor=#3A5431;" parent="J3cU0cQ84F11lH7ggRXQ-140" vertex="1">
                    <mxGeometry x="297" y="332" width="239" height="231.73333333333335" as="geometry"/>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-15" style="line;strokeWidth=1;fillColor=none;align=left;verticalAlign=middle;spacingTop=-1;spacingLeft=3;spacingRight=3;rotatable=0;labelPosition=right;points=[];portConstraint=eastwest;strokeColor=inherit;" parent="J3cU0cQ84F11lH7ggRXQ-14" vertex="1">
                    <mxGeometry y="31.733333333333334" width="239" height="8" as="geometry"/>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-16" value="+getUserPermissions()" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontSize=15;" parent="J3cU0cQ84F11lH7ggRXQ-14" vertex="1">
                    <mxGeometry y="39.733333333333334" width="239" height="32" as="geometry"/>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-17" value="+getAllGroups()" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontSize=15;" parent="J3cU0cQ84F11lH7ggRXQ-14" vertex="1">
                    <mxGeometry y="71.73333333333333" width="239" height="32" as="geometry"/>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-18" value="+createGroup()" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontSize=15;" parent="J3cU0cQ84F11lH7ggRXQ-14" vertex="1">
                    <mxGeometry y="103.73333333333333" width="239" height="32" as="geometry"/>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-19" value="+updateGroup(group_id)" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontSize=15;" parent="J3cU0cQ84F11lH7ggRXQ-14" vertex="1">
                    <mxGeometry y="135.73333333333335" width="239" height="32" as="geometry"/>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-20" value="+deleteGroup(group_id)" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontSize=15;" parent="J3cU0cQ84F11lH7ggRXQ-14" vertex="1">
                    <mxGeometry y="167.73333333333335" width="239" height="32" as="geometry"/>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-21" value="+getGroupUsers(group_id)" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontSize=15;" parent="J3cU0cQ84F11lH7ggRXQ-14" vertex="1">
                    <mxGeometry y="199.73333333333335" width="239" height="32" as="geometry"/>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-22" value="图像处理API" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=32.92307692307692;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=0;marginBottom=0;fillColor=#6d8764;fontColor=#ffffff;strokeColor=#3A5431;" parent="J3cU0cQ84F11lH7ggRXQ-140" vertex="1">
                    <mxGeometry x="586" y="344" width="253" height="205.9230769230769" as="geometry"/>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-23" style="line;strokeWidth=1;fillColor=none;align=left;verticalAlign=middle;spacingTop=-1;spacingLeft=3;spacingRight=3;rotatable=0;labelPosition=right;points=[];portConstraint=eastwest;strokeColor=inherit;" parent="J3cU0cQ84F11lH7ggRXQ-22" vertex="1">
                    <mxGeometry y="32.92307692307692" width="253" height="8" as="geometry"/>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-24" value="+detect() : 待实现" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontSize=15;" parent="J3cU0cQ84F11lH7ggRXQ-22" vertex="1">
                    <mxGeometry y="40.92307692307692" width="253" height="33" as="geometry"/>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-25" value="+segment() : 待实现" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontSize=15;" parent="J3cU0cQ84F11lH7ggRXQ-22" vertex="1">
                    <mxGeometry y="73.92307692307692" width="253" height="33" as="geometry"/>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-26" value="+pose() : 待实现" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontSize=15;" parent="J3cU0cQ84F11lH7ggRXQ-22" vertex="1">
                    <mxGeometry y="106.92307692307692" width="253" height="33" as="geometry"/>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-27" value="+createDataset() : 待实现" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontSize=15;" parent="J3cU0cQ84F11lH7ggRXQ-22" vertex="1">
                    <mxGeometry y="139.9230769230769" width="253" height="33" as="geometry"/>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-28" value="+getModels() : 待实现" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontSize=15;" parent="J3cU0cQ84F11lH7ggRXQ-22" vertex="1">
                    <mxGeometry y="172.9230769230769" width="253" height="33" as="geometry"/>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-29" value="" style="curved=1;startArrow=block;startSize=16;startFill=0;endArrow=none;exitX=0;exitY=0.72;entryX=0.5;entryY=0;rounded=0;" parent="J3cU0cQ84F11lH7ggRXQ-140" source="J3cU0cQ84F11lH7ggRXQ-1" target="J3cU0cQ84F11lH7ggRXQ-8" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <Array as="points">
                            <mxPoint x="136" y="307"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-30" value="" style="curved=1;startArrow=block;startSize=16;startFill=0;endArrow=none;exitX=0.5;exitY=1;entryX=0.5;entryY=0;rounded=0;" parent="J3cU0cQ84F11lH7ggRXQ-140" source="J3cU0cQ84F11lH7ggRXQ-1" target="J3cU0cQ84F11lH7ggRXQ-14" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <Array as="points"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-31" value="" style="curved=1;startArrow=block;startSize=16;startFill=0;endArrow=none;exitX=1;exitY=0.71;entryX=0.5;entryY=0;rounded=0;" parent="J3cU0cQ84F11lH7ggRXQ-140" source="J3cU0cQ84F11lH7ggRXQ-1" target="J3cU0cQ84F11lH7ggRXQ-22" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <Array as="points">
                            <mxPoint x="712" y="307"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-143" value="opencv库" style="whiteSpace=wrap;strokeWidth=2;fontSize=15;" parent="1" vertex="1">
                    <mxGeometry x="-1257" y="1675" width="164" height="99" as="geometry"/>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-144" value="&lt;b&gt;&lt;font style=&quot;font-size: 20px;&quot;&gt;技术栈&lt;/font&gt;&lt;/b&gt;" style="rounded=0;whiteSpace=wrap;html=1;strokeWidth=4;fillColor=#a0522d;fontColor=#ffffff;strokeColor=#6D1F00;" parent="1" vertex="1">
                    <mxGeometry x="-1883" y="852" width="350" height="59" as="geometry"/>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-151" value="&lt;b&gt;&lt;font style=&quot;font-size: 20px;&quot;&gt;技术栈&lt;/font&gt;&lt;/b&gt;" style="rounded=0;whiteSpace=wrap;html=1;strokeWidth=3;fillColor=#a0522d;fontColor=#ffffff;strokeColor=#6D1F00;" parent="1" vertex="1">
                    <mxGeometry x="-1883" y="1493" width="350" height="59" as="geometry"/>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-32" value="后端技术栈（Backend）" style="whiteSpace=wrap;strokeWidth=2;verticalAlign=top;fontStyle=1;fontSize=20;container=0;fillColor=#f8cecc;strokeColor=#b85450;" parent="1" vertex="1">
                    <mxGeometry x="-1883" y="1564" width="350" height="294" as="geometry"/>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-125" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;curved=1;endArrow=block;endFill=0;fontSize=15;" parent="1" source="J3cU0cQ84F11lH7ggRXQ-99" target="J3cU0cQ84F11lH7ggRXQ-100" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-130" value="依赖" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];container=0;fontSize=15;" parent="J3cU0cQ84F11lH7ggRXQ-125" vertex="1" connectable="0">
                    <mxGeometry x="0.1823" y="1" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-126" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.25;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;curved=1;endArrow=block;endFill=0;fontSize=15;" parent="1" source="J3cU0cQ84F11lH7ggRXQ-99" target="J3cU0cQ84F11lH7ggRXQ-102" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-131" value="集成" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];container=0;fontSize=15;" parent="J3cU0cQ84F11lH7ggRXQ-126" vertex="1" connectable="0">
                    <mxGeometry x="0.0377" y="-1" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-127" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.75;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;curved=1;endArrow=block;endFill=0;fontSize=15;" parent="1" source="J3cU0cQ84F11lH7ggRXQ-99" target="J3cU0cQ84F11lH7ggRXQ-104" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-132" value="调用" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];container=0;fontSize=15;" parent="J3cU0cQ84F11lH7ggRXQ-127" vertex="1" connectable="0">
                    <mxGeometry x="-0.0427" y="1" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-128" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;curved=1;endArrow=block;endFill=0;fontSize=15;" parent="1" source="J3cU0cQ84F11lH7ggRXQ-99" target="J3cU0cQ84F11lH7ggRXQ-101" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-133" value="依赖" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];container=0;fontSize=15;" parent="J3cU0cQ84F11lH7ggRXQ-128" vertex="1" connectable="0">
                    <mxGeometry x="-0.0258" y="3" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-99" value="Django" style="whiteSpace=wrap;strokeWidth=2;container=0;fontSize=15;" parent="1" vertex="1">
                    <mxGeometry x="-1735.204783258595" y="1652.8010233060659" width="58.594917787742894" height="34.81578947368421" as="geometry"/>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-100" value="Pydantic" style="whiteSpace=wrap;strokeWidth=2;container=0;fontSize=15;" parent="1" vertex="1">
                    <mxGeometry x="-1873.3213751868461" y="1742.99681277975" width="63.826606875934225" height="34.81578947368421" as="geometry"/>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-129" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;endArrow=block;endFill=0;fontSize=15;" parent="1" source="J3cU0cQ84F11lH7ggRXQ-101" target="J3cU0cQ84F11lH7ggRXQ-103" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-134" value="驱动" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];container=0;fontSize=15;" parent="J3cU0cQ84F11lH7ggRXQ-129" vertex="1" connectable="0">
                    <mxGeometry x="-0.0576" y="1" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-101" value="SQLAlchemy" style="whiteSpace=wrap;strokeWidth=2;container=0;fontSize=15;" parent="1" vertex="1">
                    <mxGeometry x="-1626.79" y="1742.99" width="91.32" height="34.82" as="geometry"/>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-102" value="JWT认证" style="whiteSpace=wrap;strokeWidth=2;container=0;fontSize=15;" parent="1" vertex="1">
                    <mxGeometry x="-1790.137518684604" y="1742.99681277975" width="64.34977578475336" height="34.81578947368421" as="geometry"/>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-103" value="MySQL" style="whiteSpace=wrap;strokeWidth=2;container=0;fontSize=15;" parent="1" vertex="1">
                    <mxGeometry x="-1608.8594917787743" y="1818.4983917271184" width="55.45590433482809" height="34.81578947368421" as="geometry"/>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-104" value="ultralytics" style="whiteSpace=wrap;strokeWidth=2;container=0;fontSize=15;" parent="1" vertex="1">
                    <mxGeometry x="-1710.0926756352765" y="1742.99681277975" width="69.05829596412555" height="34.81578947368421" as="geometry"/>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-105" value="运行环境：Python 3.9+" style="whiteSpace=wrap;strokeWidth=2;container=0;fontSize=15;" parent="1" vertex="1">
                    <mxGeometry x="-1787.87" y="1605.89" width="163.93" height="34.82" as="geometry"/>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-164" value="" style="group;fillColor=#d5e8d4;gradientColor=#97d077;strokeColor=#82b366;container=0;" parent="1" vertex="1" connectable="0">
                    <mxGeometry x="-1476" y="1009" width="442" height="230" as="geometry"/>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-165" value="" style="group;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1" connectable="0">
                    <mxGeometry x="-598" y="1007" width="355" height="230" as="geometry"/>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-194" value="" style="group;fillColor=#dae8fc;strokeColor=#6c8ebf;container=0;" parent="J3cU0cQ84F11lH7ggRXQ-165" vertex="1" connectable="0">
                    <mxGeometry x="118" width="237" height="230" as="geometry"/>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-65" value="认证模块组件" style="whiteSpace=wrap;strokeWidth=2;fontStyle=1;fontSize=20;verticalAlign=top;fillColor=#cdeb8b;strokeColor=#36393d;gradientColor=#99FF33;" parent="J3cU0cQ84F11lH7ggRXQ-165" vertex="1">
                    <mxGeometry x="118" width="237" height="230" as="geometry"/>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-157" value="用户登录页面" style="whiteSpace=wrap;strokeWidth=2;fontSize=15;" parent="J3cU0cQ84F11lH7ggRXQ-165" vertex="1">
                    <mxGeometry x="129.35365853658536" y="70.99719298245614" width="101.3048780487805" height="54.4736842105263" as="geometry"/>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-158" value="用户注册页面" style="whiteSpace=wrap;strokeWidth=2;fontSize=15;" parent="J3cU0cQ84F11lH7ggRXQ-165" vertex="1">
                    <mxGeometry x="242.3448780487805" y="146.65719298245614" width="101.3048780487805" height="54.4736842105263" as="geometry"/>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-159" value="账户管理页面" style="whiteSpace=wrap;strokeWidth=2;fontSize=15;" parent="J3cU0cQ84F11lH7ggRXQ-165" vertex="1">
                    <mxGeometry x="242.34756097560975" y="70.99719298245614" width="101.3048780487805" height="54.4736842105263" as="geometry"/>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-162" value="忘记密码页面" style="whiteSpace=wrap;strokeWidth=2;fontSize=15;" parent="J3cU0cQ84F11lH7ggRXQ-165" vertex="1">
                    <mxGeometry x="129.35365853658536" y="146.65508771929822" width="101.3048780487805" height="54.4736842105263" as="geometry"/>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-173" value="&lt;b&gt;&lt;font style=&quot;font-size: 20px;&quot;&gt;路由保护组件&lt;/font&gt;&lt;/b&gt;" style="rounded=0;whiteSpace=wrap;html=1;strokeWidth=2;verticalAlign=top;fillColor=#ffcc99;strokeColor=#36393d;gradientColor=#FF9933;" parent="1" vertex="1">
                    <mxGeometry x="-1018" y="1009" width="212" height="230" as="geometry"/>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-175" value="&lt;span style=&quot;font-size: 20px;&quot;&gt;&lt;b&gt;全局状态管理组件&lt;/b&gt;&lt;/span&gt;&lt;div&gt;&lt;span style=&quot;font-size: 20px;&quot;&gt;&lt;b&gt;（React Contexts）&lt;/b&gt;&lt;/span&gt;&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;strokeWidth=2;verticalAlign=top;fillColor=#f8cecc;strokeColor=#b85450;gradientColor=#FFA29F;" parent="1" vertex="1">
                    <mxGeometry x="-787" y="1007" width="292" height="230" as="geometry"/>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-180" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;endArrow=block;endFill=0;fontSize=15;" parent="1" source="J3cU0cQ84F11lH7ggRXQ-176" target="J3cU0cQ84F11lH7ggRXQ-179" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-176" value="Alembic " style="whiteSpace=wrap;strokeWidth=2;fontSize=15;" parent="1" vertex="1">
                    <mxGeometry x="-419.25" y="1645.71" width="124" height="54" as="geometry"/>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-179" value="管理数据库结构演进" style="whiteSpace=wrap;strokeWidth=2;fontSize=15;" parent="1" vertex="1">
                    <mxGeometry x="-414.37" y="1750" width="114.25" height="54" as="geometry"/>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-195" value="AuthContext：&lt;br&gt;管理认证、用户权限、令牌" style="rounded=0;whiteSpace=wrap;html=1;fontSize=15;strokeWidth=2;" parent="1" vertex="1">
                    <mxGeometry x="-730.75" y="1063" width="185" height="48" as="geometry"/>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-196" value="ImageWorkspaceContxt：&lt;br&gt;管理图片加载、导航、信息" style="rounded=0;whiteSpace=wrap;html=1;fontSize=15;strokeWidth=2;" parent="1" vertex="1">
                    <mxGeometry x="-730.75" y="1119" width="186" height="51" as="geometry"/>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-197" value="FunctionPanelContext：&lt;br&gt;同步功能树与参数面板状态" style="rounded=0;whiteSpace=wrap;html=1;fontSize=15;strokeWidth=2;" parent="1" vertex="1">
                    <mxGeometry x="-730.75" y="1178" width="187" height="51" as="geometry"/>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-198" value="ProtectedRoute：&lt;br&gt;保护需要登录的路由" style="rounded=0;whiteSpace=wrap;html=1;fontSize=15;strokeWidth=2;" parent="1" vertex="1">
                    <mxGeometry x="-994.5" y="1085" width="165" height="48" as="geometry"/>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-199" value="PublicRouteWrapper：&lt;br&gt;防止已登录用户访问公共页面" style="rounded=0;whiteSpace=wrap;html=1;fontSize=15;strokeWidth=2;" parent="1" vertex="1">
                    <mxGeometry x="-1011" y="1159" width="198" height="48" as="geometry"/>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-63" value="核心组件" style="whiteSpace=wrap;strokeWidth=2;fontStyle=1;verticalAlign=top;fontSize=20;fillColor=#d5e8d4;gradientColor=#97d077;strokeColor=#82b366;" parent="1" vertex="1">
                    <mxGeometry x="-1476" y="1009" width="442" height="230" as="geometry"/>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-147" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;curved=1;endArrow=block;endFill=0;fontSize=15;" parent="1" source="J3cU0cQ84F11lH7ggRXQ-66" target="J3cU0cQ84F11lH7ggRXQ-68" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-148" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;curved=1;endArrow=block;endFill=0;fontSize=15;" parent="1" source="J3cU0cQ84F11lH7ggRXQ-66" target="J3cU0cQ84F11lH7ggRXQ-69" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-149" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;curved=1;endArrow=block;endFill=0;fontSize=15;" parent="1" source="J3cU0cQ84F11lH7ggRXQ-66" target="J3cU0cQ84F11lH7ggRXQ-70" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-150" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;curved=1;endArrow=block;endFill=0;fontSize=15;" parent="1" source="J3cU0cQ84F11lH7ggRXQ-66" target="J3cU0cQ84F11lH7ggRXQ-71" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-161" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;endArrow=block;endFill=0;fontSize=15;" parent="1" source="J3cU0cQ84F11lH7ggRXQ-66" target="J3cU0cQ84F11lH7ggRXQ-160" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-66" value="图像处理界面" style="whiteSpace=wrap;strokeWidth=2;fontSize=15;" parent="1" vertex="1">
                    <mxGeometry x="-1296.401055408971" y="1060.7748917748918" width="90.96569920844327" height="53.76623376623377" as="geometry"/>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-68" value="图像加载/显示" style="whiteSpace=wrap;strokeWidth=2;fontSize=15;" parent="1" vertex="1">
                    <mxGeometry x="-1467.2532981530344" y="1160.3419913419914" width="95.6306068601583" height="53.76623376623377" as="geometry"/>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-69" value="功能树" style="whiteSpace=wrap;strokeWidth=2;fontSize=15;" parent="1" vertex="1">
                    <mxGeometry x="-1359.377308707124" y="1160.3419913419914" width="62.97625329815303" height="53.76623376623377" as="geometry"/>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-70" value="参数面板" style="whiteSpace=wrap;strokeWidth=2;fontSize=15;" parent="1" vertex="1">
                    <mxGeometry x="-1205.4353562005276" y="1160.3419913419914" width="72.30606860158312" height="53.76623376623377" as="geometry"/>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-71" value="结果展示" style="whiteSpace=wrap;strokeWidth=2;fontSize=15;" parent="1" vertex="1">
                    <mxGeometry x="-1122.6332453825858" y="1160.3419913419914" width="72.30606860158312" height="53.76623376623377" as="geometry"/>
                </mxCell>
                <mxCell id="J3cU0cQ84F11lH7ggRXQ-160" value="图像信息面板" style="whiteSpace=wrap;strokeWidth=2;fontSize=15;" parent="1" vertex="1">
                    <mxGeometry x="-1287.071240105541" y="1160.3419913419914" width="72.30606860158312" height="53.76623376623377" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>