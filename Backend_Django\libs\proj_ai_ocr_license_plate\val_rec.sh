# 检测模型
model="output/ccpd_rec_ppocr_v4"
eval_log="${model}/evaluate.log"
metrics_dir="mindeo/metrics"
metric="${metrics_dir}/rec.json"

# 评估模型
python3.10 tools/eval.py \
-c ${model}/config.yml \
-o Global.checkpoints="${model}/best_model/model.pdparams"\
 Eval.dataset.data_dir="./mindeo/data_processed/Rec/Train/test" \
 Eval.dataset.label_file_list="[./mindeo/data_processed/Rec/Train/test/Label.txt]" > ${eval_log}

# 获取评估结果
acc=$(cat ${eval_log} | grep "INFO: acc:" | awk -F'acc:' '{print $2}')
norm_edit_dis=$(cat ${eval_log} | grep "INFO: norm_edit_dis:" | awk -F'norm_edit_dis:' '{print $2}')

# 保存结果
mkdir -p ${metrics_dir}
echo '{' > ${metric}
echo '    "evaluate": {' >> ${metric}
echo '        "acc": ' "${acc}," >> ${metric}
echo '        "norm_edit_dis": ' "${norm_edit_dis}" >> ${metric}
echo '    }' >> ${metric}
echo '}' >> ${metric}