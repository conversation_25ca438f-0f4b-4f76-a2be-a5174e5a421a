import React, { useState, useEffect, useRef } from 'react';
import {
    Form,
    Select,
    Button,
    Typography,
    Space,
    message,
    Spin,
    Alert,
    Divider,
    <PERSON>ltip,
    Switch,
    Modal,
    Slider,
    InputNumber,
    Row,
    Col,
    Radio,
} from 'antd';
import { SyncOutlined, SaveOutlined } from '@ant-design/icons';
import { useQuery } from '@tanstack/react-query';
import { useImageWorkspace, ImageInfo } from '../../contexts/ImageWorkspaceContext';
import { useAiRestoreDetectionParams, AiRestoreDisplayMode } from '../../contexts/AiRestoreDetectionContext';
import { useFunctionPanel } from '../../contexts/FunctionPanelContext';
import {
    detectImageRestore,
    getAIRestoredModels,
    AiImageRestoreParams,
    AiImageRestoreResult as ApiAiImageRestoreResult,
    VisionModel,
    CustomApiError,
} from '../../services/api';
import compositeRestoreImage from '../../assets/AI复原-合成视图.png'; // 合成视图示例
import restoredOnlyImage from '../../assets/AI复原-仅复原图.png'; // 仅复原图示例

const { Title } = Typography;
const { Option } = Select;

// Declare the global window property for AI Restore
declare global {
    interface Window {
        runAiImageRestore?: () => Promise<void>;
    }
}

// Interface for batch result items
interface BatchAiRestoreResultItem {
    index: number;
    fileName: string;
    imageDataUrl: string | null; // Captured image data URL with overlays
    processed: boolean;
    error?: string;
}

// Helper function to create the composite AI Restore image
async function createAiRestoreCompositeImage(
    originalImageFile: File,
    restoredImageBase64: string,
    originalWidth: number,
    originalHeight: number
): Promise<string> {
    const canvas = document.createElement('canvas');
    // The composite image will have original on left, restored on right.
    // Width will be originalWidth + restoredWidth, height will be max of originalHeight and restoredHeight.
    // For simplicity, if restored is often same aspect ratio, let's assume we scale restored to originalHeight for now.
    // Or, more accurately, the panel should be originalWidth + originalWidth (if restored is displayed at same visual size)
    // Let's assume the right panel shows the restored image scaled to fit originalHeight if aspect ratios differ,
    // and the canvas width for the right part matches originalWidth for visual balance.
    canvas.width = originalWidth * 2; // original on left, restored (scaled to fit) on right
    canvas.height = originalHeight;
    const ctx = canvas.getContext('2d');
    if (!ctx) {
        console.error('Failed to get canvas 2D context for AI Restore composite');
        throw new Error('Failed to get canvas context for AI Restore composite');
    }

    // 1. Draw original image on the left half
    const originalImg = new Image();
    const originalObjectURL = URL.createObjectURL(originalImageFile);
    await new Promise<void>((resolve, reject) => {
        originalImg.onload = () => {
            URL.revokeObjectURL(originalObjectURL);
            ctx.drawImage(originalImg, 0, 0, originalWidth, originalHeight);
            resolve();
        };
        originalImg.onerror = (err) => {
            URL.revokeObjectURL(originalObjectURL);
            console.error('Original image load error for AI Restore composite:', err);
            reject(new Error('Failed to load original image for AI Restore composite view'));
        };
        originalImg.src = originalObjectURL;
    });

    // 2. Draw restored image on the right half
    const restoredImg = new Image();
    const restoredDataUrl = `data:image/png;base64,${restoredImageBase64}`; // Assuming PNG, or get from API response if available
    await new Promise<void>((resolve, reject) => {
        restoredImg.onload = () => {
            // Scale restored image to fit into the right panel (originalWidth x originalHeight area)
            // while maintaining aspect ratio.
            const targetX = originalWidth;
            const targetY = 0;
            const targetWidth = originalWidth;
            const targetHeight = originalHeight;

            const hRatio = targetWidth / restoredImg.width;
            const vRatio = targetHeight / restoredImg.height;
            const ratio = Math.min(hRatio, vRatio);

            const drawWidth = restoredImg.width * ratio;
            const drawHeight = restoredImg.height * ratio;

            // Center the image in the target panel
            const offsetX = targetX + (targetWidth - drawWidth) / 2;
            const offsetY = targetY + (targetHeight - drawHeight) / 2;

            ctx.drawImage(restoredImg, offsetX, offsetY, drawWidth, drawHeight);
            resolve();
        };
        restoredImg.onerror = (err) => {
            console.error('Restored image load error for AI Restore composite:', err);
            reject(new Error('Failed to load restored image for AI Restore composite view'));
        };
        restoredImg.src = restoredDataUrl;
    });

    return canvas.toDataURL('image/png');
}

const AiRestorePanel: React.FC = () => {
    const [messageApi, contextHolder] = message.useMessage();
    const [form] = Form.useForm();
    const { selectedFunction } = useFunctionPanel();
    // 用于跟踪当前面板状态，避免重复提示
    const currentPanelRef = useRef<string | null>(null);
    // 移除本地状态，使用Context中的状态
    // const [isInferring, setIsInferring] = useState(false);
    // const [isBatchRestoring, setIsBatchRestoring] = useState<boolean>(false);
    const [batchRestoreResults, setBatchRestoreResults] = useState<{
        totalImages: number;
        processedImages: number;
        resultItems: BatchAiRestoreResultItem[];
    }>({
        totalImages: 0,
        processedImages: 0,
        resultItems: [],
    });
    const [showRestoreSaveConfirmModal, setShowRestoreSaveConfirmModal] = useState<boolean>(false);
    const [isSavingBatchRestore, setIsSavingBatchRestore] = useState<boolean>(false);
    const processingImageRef = useRef<{
        index: number;
        imageInfo: ImageInfo | null;
        file: File | null;
    }>({ index: -1, imageInfo: null, file: null });

    const {
        currentImageInfo,
        imageList,
        currentImageIndex,
        setCompositeImageDisplay,
        restoreOriginalImageDisplay,
        restoreOriginalForAiRestore, // 新增：专门用于AI复原的状态恢复
        originalImageInfo,
        setCurrentImageIndex,
        forceRefreshImage,
        getDisplayedViewDataURL,
        setIsBatchProcessingActive,
    } = useImageWorkspace();

    const {
        selectedModelValue,
        setSelectedModelValue,
        setAiRestoreProcessingResults,
        availableAiRestoreModels,
        setAvailableAiRestoreModels,
        aiRestoreModelsLoading,
        setAiRestoreModelsLoading,
        aiRestoreModelsError,
        setAiRestoreModelsError,
        autoInferenceEnabled,
        setAutoInferenceEnabled,
        batchProcessingInterval,
        setBatchProcessingInterval,
        selectedDisplayMode,
        setSelectedDisplayMode,
        isInferring,
        setIsInferring,
        isBatchProcessing,
        setIsBatchProcessing,
    } = useAiRestoreDetectionParams();

    const {
        isLoading: reactQueryModelsLoadingInitial,
        error: reactQueryModelsError,
        data: fetchedModelsData,
        refetch: refetchAiRestoreModels,
    } = useQuery<VisionModel[], CustomApiError>({
        queryKey: ['aiRestoreModels'],
        queryFn: getAIRestoredModels,
    });

    useEffect(() => {
        setAiRestoreModelsLoading(reactQueryModelsLoadingInitial);
    }, [reactQueryModelsLoadingInitial, setAiRestoreModelsLoading]);

    useEffect(() => {
        if (fetchedModelsData) {
            setAvailableAiRestoreModels(fetchedModelsData);
            setAiRestoreModelsError(null);
            if (fetchedModelsData.length > 0 && !fetchedModelsData.some(m => m.name === selectedModelValue)) {
                setSelectedModelValue(fetchedModelsData[0].name);
            }
        }
    }, [fetchedModelsData, setAvailableAiRestoreModels, setSelectedModelValue, selectedModelValue, setAiRestoreModelsError]);

    useEffect(() => {
        if (reactQueryModelsError) {
            const errMsg = reactQueryModelsError.message || '获取AI复原模型列表失败';
            setAiRestoreModelsError(errMsg);
            setAvailableAiRestoreModels([]);
        }
    }, [reactQueryModelsError, setAiRestoreModelsError, setAvailableAiRestoreModels]);

    // 监听面板切换，每次切换到AI复原面板时显示模型加载成功提示
    useEffect(() => {
        const currentKey = selectedFunction?.key as string;
        if (currentKey === 'ai-restore' && currentPanelRef.current !== currentKey && fetchedModelsData && fetchedModelsData.length > 0 && !aiRestoreModelsLoading) {
            console.log('[AiRestorePanel] 面板切换到AI复原，模型已加载');
            currentPanelRef.current = currentKey;
            messageApi.success('AI复原模型列表已成功加载！');
        } else if (currentKey !== 'ai-restore') {
            // 当切换到其他面板时，重置状态
            currentPanelRef.current = null;
        }
    }, [selectedFunction?.key, fetchedModelsData, aiRestoreModelsLoading, messageApi]);

    useEffect(() => {
        form.setFieldsValue({
            selected_model: selectedModelValue,
            display_mode: selectedDisplayMode,
            auto_inference: autoInferenceEnabled,
            batch_processing_interval: batchProcessingInterval,
        });
    }, [selectedModelValue, form, autoInferenceEnabled, batchProcessingInterval, selectedDisplayMode]);

    const handleSingleImageAiRestore = async () => {
        if (!currentImageInfo || currentImageIndex < 0 || !imageList[currentImageIndex]) {
            messageApi.error('请先加载一张图片。');
            return;
        }
        if (!selectedModelValue) {
            messageApi.error('请选择一个AI复原模型。');
            return;
        }

        setIsInferring(true);
        setAiRestoreProcessingResults(null);

        const imageFileToProcess = imageList[currentImageIndex];

        // 关键修复：每次修复前都确保恢复到原始图像状态
        // 使用专门的AI复原状态恢复函数，保持originalImageInfo不被清空
        if (originalImageInfo) {
            console.log('[AiRestorePanel] 检测到存在原始图像备份，使用AI复原专用恢复函数');
            await restoreOriginalForAiRestore();
            await new Promise(resolve => setTimeout(resolve, 300)); // 等待状态更新完成
        }

        // 额外验证：检查当前图像名称是否包含合成视图标识
        if (currentImageInfo?.name.includes('(Composite AI Restore)') || currentImageInfo?.name.includes('(Restored)')) {
            console.warn('[AiRestorePanel] 检测到临时视图状态，强制恢复原始图像');
            await restoreOriginalForAiRestore();
            await new Promise(resolve => setTimeout(resolve, 300));
        }

        try {
            const params: AiImageRestoreParams = {
                image: imageFileToProcess,
                model_name: selectedModelValue,
            };

            const response: ApiAiImageRestoreResult = await detectImageRestore(params);
            messageApi.success('AI图像修复成功！');

            console.log('[AiRestorePanel] Full API response:', response);
            if (response.image_base64 && typeof response.image_base64 === 'string' && response.image_base64.length > 0) {
                setAiRestoreProcessingResults({
                    restoredImageBase64: response.image_base64,
                });

                if (setCompositeImageDisplay && currentImageInfo) {
                    // 获取原始图像信息，确保使用正确的尺寸
                    const originalInfo = originalImageInfo || currentImageInfo;
                    const baseName = originalInfo.name.replace(/\s*\((OCR View|Restored|Barcode View|Composite AI Restore)\)\s*$/i, '');

                    console.log('[AiRestorePanel] 使用图像尺寸:', {
                        width: originalInfo.width,
                        height: originalInfo.height,
                        source: originalImageInfo ? 'originalImageInfo' : 'currentImageInfo'
                    });

                    if (selectedDisplayMode === 'composite') {
                        const compositeDataUrl = await createAiRestoreCompositeImage(
                            imageFileToProcess,
                            response.image_base64,
                            originalInfo.width,  // 使用原始图像宽度
                            originalInfo.height  // 使用原始图像高度
                        );
                        setCompositeImageDisplay(
                            compositeDataUrl,
                            originalInfo.width * 2,  // 合成图宽度 = 原图宽度 × 2
                            originalInfo.height,     // 合成图高度 = 原图高度
                            `${baseName} (Composite AI Restore)`
                        );
                    } else {
                        const restoredViewName = `${baseName} (Restored)`;
                        // 对于仅修复图模式，也使用原始图像尺寸作为基准
                        const displayWidth = response.restored_size ? response.restored_size[0] : originalInfo.width;
                        const displayHeight = response.restored_size ? response.restored_size[1] : originalInfo.height;
                        const mimeType = response.format?.toLowerCase() === 'jpeg' ? 'image/jpeg' : 'image/png';
                        const fullDataUrl = `data:${mimeType};base64,${response.image_base64}`;

                        console.log('[AiRestorePanel] 仅修复图模式尺寸:', {
                            displayWidth,
                            displayHeight,
                            hasRestoredSize: !!response.restored_size
                        });

                        setCompositeImageDisplay(
                            fullDataUrl,
                            displayWidth,
                            displayHeight,
                            restoredViewName
                        );
                    }
                }
            } else {
                console.error('AI Restore success response, but image_base64 is missing or invalid.', response);
                messageApi.error('AI图像修复成功，但未能获取修复后的图像数据。');
                setAiRestoreProcessingResults(null);
            }

        } catch (error) {
            const apiError = error as CustomApiError;
            console.error('AI Restore Inference error:', apiError);
            const errorMessage = (apiError.detail && typeof apiError.detail === 'string')
                ? apiError.detail
                : apiError.message || 'AI图像修复失败，发生未知错误。';
            messageApi.error(errorMessage);
            setAiRestoreProcessingResults(null);
        } finally {
            setIsInferring(false);
        }
    };

    useEffect(() => {
        window.runAiImageRestore = handleSingleImageAiRestore;
        return () => {
            delete window.runAiImageRestore;
        };
    }, [handleSingleImageAiRestore]);

    const handleMultiImageAiRestore = async () => {
        if (!imageList || imageList.length <= 1) {
            messageApi.error('请先通过"打开文件夹"加载多张图片后再执行多图修复！');
            return;
        }
        if (!selectedModelValue) {
            messageApi.error('请选择一个AI复原模型。');
            return;
        }

        setIsBatchProcessing(true);
        const batchRestoreMessageKey = 'batchAiRestore';
        messageApi.loading({ content: `开始批量AI复原 ${imageList.length} 张图片...`, key: batchRestoreMessageKey, duration: 0 });

        if (setIsBatchProcessingActive) setIsBatchProcessingActive(true);

        if (originalImageInfo && currentImageInfo?.name !== originalImageInfo.name) {
            await restoreOriginalImageDisplay();
            await new Promise(resolve => setTimeout(resolve, 200));
        }
        setAiRestoreProcessingResults(null); // Clear any single image results

        let imagesProcessedCount = 0;
        const totalImagesToProcess = imageList.length;

        const resultItemsInit: BatchAiRestoreResultItem[] = imageList.map((file, index) => ({
            index,
            fileName: file.name,
            imageDataUrl: null,
            processed: false,
        }));

        setBatchRestoreResults({
            totalImages: totalImagesToProcess,
            processedImages: 0,
            resultItems: resultItemsInit,
        });

        const processNextRestoreImage = async (idx: number) => {
            if (idx >= totalImagesToProcess) {
                if (setCurrentImageIndex) {
                    processingImageRef.current = { index: -1, imageInfo: null, file: null };
                    if (originalImageInfo && currentImageInfo?.name !== originalImageInfo.name ) {
                        await restoreOriginalImageDisplay();
                        await new Promise(resolve => setTimeout(resolve, 200));
                    }
                    // Attempt to reset to the first image in the list or a default state
                    if (imageList.length > 0 && forceRefreshImage) {
                        const refreshSuccess = await forceRefreshImage(0);
                        if (!refreshSuccess) setCurrentImageIndex(0);
                        await new Promise(resolve => setTimeout(resolve, 300));
                    } else if (imageList.length > 0) {
                        setCurrentImageIndex(0);
                        await new Promise(resolve => setTimeout(resolve, 300));
                    }
                }
                setIsBatchProcessing(false);
                messageApi.success({
                    content: `批量AI复原完成！共处理 ${imagesProcessedCount} 张图片。`,
                    key: batchRestoreMessageKey,
                    duration: 5,
                });
                setBatchRestoreResults(prev => ({
                    ...prev,
                    processedImages: imagesProcessedCount,
                }));
                if (imagesProcessedCount > 0) {
                    setShowRestoreSaveConfirmModal(true);
                }
                if (setIsBatchProcessingActive) setIsBatchProcessingActive(false);
                return;
            }

            const currentFile = imageList[idx];
            let currentImgInfo = await new Promise<ImageInfo | null>(resolve => {
                const img = new Image();
                const objUrl = URL.createObjectURL(currentFile);
                img.onload = () => {
                    resolve({ name: currentFile.name, url: objUrl, width: img.width, height: img.height, type: currentFile.type });
                    URL.revokeObjectURL(objUrl);
                };
                img.onerror = () => {
                    URL.revokeObjectURL(objUrl);
                    resolve(null);
                };
                img.src = objUrl;
            });

            if (!currentFile || !currentImgInfo) {
                setBatchRestoreResults(prev => {
                    const updatedItems = [...prev.resultItems];
                    updatedItems[idx] = { ...updatedItems[idx], processed: true, error: '无法加载图片信息' };
                    return { ...prev, resultItems: updatedItems };
                });
                imagesProcessedCount++;
                setTimeout(() => processNextRestoreImage(idx + 1), batchProcessingInterval);
                return;
            }

            processingImageRef.current = { index: idx, imageInfo: currentImgInfo, file: currentFile };

            // 批量处理：简化图像切换逻辑，确保稳定切换
            console.log(`[AiRestorePanel] 批量处理：切换到图像 ${idx + 1}/${totalImagesToProcess}: ${currentFile.name}`);

            // 首先清理之前的状态
            if (originalImageInfo) {
                console.log('[AiRestorePanel] 批量处理：清理之前的合成视图状态');
                await restoreOriginalImageDisplay();
                await new Promise(resolve => setTimeout(resolve, 200));
            }

            // 切换到目标图像
            if (setCurrentImageIndex) {
                setCurrentImageIndex(idx);
                await new Promise(resolve => setTimeout(resolve, 800)); // 增加等待时间确保图像切换完成
            }

            setAiRestoreProcessingResults(null); // Clear previous single restore results if any

            messageApi.loading({
                content: `批量修复中 ${idx + 1}/${totalImagesToProcess}: ${currentFile.name}`,
                key: batchRestoreMessageKey,
                duration: 0,
            });

            try {
                const params: AiImageRestoreParams = {
                    image: currentFile,
                    model_name: selectedModelValue,
                };
                const response: ApiAiImageRestoreResult = await detectImageRestore(params);
                imagesProcessedCount++;

                let capturedImageDataUrl: string | null = null;
                if (response.image_base64 && typeof response.image_base64 === 'string' && response.image_base64.length > 0) {
                    if (setCompositeImageDisplay && currentImgInfo) {
                        const baseName = currentImgInfo.name.replace(/\s*\((OCR View|Restored|Barcode View|Composite AI Restore)\)\s*$/i, '');

                        console.log('[AiRestorePanel] 批量处理使用图像尺寸:', {
                            width: currentImgInfo.width,
                            height: currentImgInfo.height,
                            fileName: currentFile.name
                        });

                        if (selectedDisplayMode === 'composite') {
                            const compositeDataUrl = await createAiRestoreCompositeImage(
                                currentFile,
                                response.image_base64,
                                currentImgInfo.width,  // 批量处理中currentImgInfo是从文件直接获取的，应该是正确的
                                currentImgInfo.height
                            );
                            setCompositeImageDisplay(
                                compositeDataUrl,
                                currentImgInfo.width * 2,
                                currentImgInfo.height,
                                `${baseName} (Composite AI Restore)`
                            );
                        } else {
                            const restoredViewName = `${baseName} (Restored)`;
                            const displayWidth = response.restored_size ? response.restored_size[0] : currentImgInfo.width;
                            const displayHeight = response.restored_size ? response.restored_size[1] : currentImgInfo.height;
                            const mimeType = response.format?.toLowerCase() === 'jpeg' ? 'image/jpeg' : 'image/png';
                            const fullDataUrl = `data:${mimeType};base64,${response.image_base64}`;

                            console.log('[AiRestorePanel] 批量处理仅修复图模式尺寸:', {
                                displayWidth,
                                displayHeight,
                                hasRestoredSize: !!response.restored_size
                            });

                            setCompositeImageDisplay(
                                fullDataUrl,
                                displayWidth,
                                displayHeight,
                                restoredViewName
                            );
                        }

                        // After setting composite/restored, the main image display is now the new view.
                        // We can try to capture this new view for batch saving.
                        await new Promise(resolve => setTimeout(resolve, 300)); // Wait for display update
                        if (getDisplayedViewDataURL) {
                            capturedImageDataUrl = await getDisplayedViewDataURL();
                        }
                    }
                } else {
                    console.error('AI Restore success, but image_base64 is missing/invalid for batch item:', currentFile.name, response);
                    throw new Error('未能获取修复后的图像数据');
                }

                setBatchRestoreResults(prev => {
                    const updatedItems = [...prev.resultItems];
                    updatedItems[idx] = {
                        index: idx,
                        fileName: currentFile.name,
                        imageDataUrl: capturedImageDataUrl,
                        processed: true,
                    };
                    return {
                        ...prev,
                        processedImages: imagesProcessedCount,
                        resultItems: updatedItems,
                    };
                });
                messageApi.success({ content: `图片 ${currentFile.name} 修复完成。`, key: `${batchRestoreMessageKey}_s${idx}`, duration: 2 });

            } catch (error) {
                imagesProcessedCount++;
                const apiError = error as CustomApiError;
                console.error(`AI图像修复失败 (图片: ${currentFile.name}):`, apiError);
                setAiRestoreProcessingResults(null);
                setBatchRestoreResults(prev => {
                    const updatedItems = [...prev.resultItems];
                    updatedItems[idx] = {
                        index: idx,
                        fileName: currentFile.name,
                        imageDataUrl: null,
                        processed: true,
                        error: apiError.message || '未知错误',
                    };
                    return { ...prev, processedImages: imagesProcessedCount, resultItems: updatedItems };
                });
                messageApi.error({ content: `图片 ${currentFile.name} 修复失败: ${apiError.message || '未知错误'}`, key: `${batchRestoreMessageKey}_e${idx}`, duration: 3 });
            } finally {
                // This ensures that if an error occurs and we break early from the specific image processing,
                // we still attempt to process the next image. The overall batch active state is handled at the loop termination.
            }

            // 使用用户设置的"多图推理间隔"等待，然后处理下一张图像
            setTimeout(() => processNextRestoreImage(idx + 1), batchProcessingInterval);
        };

        processNextRestoreImage(0);
    };

    const handleSaveAllRestoredResults = async () => {
        const validResults = batchRestoreResults.resultItems.filter(item => item.processed && item.imageDataUrl);
        if (validResults.length === 0) {
            messageApi.info('没有可供保存的有效AI复原结果图像。');
            setShowRestoreSaveConfirmModal(false);
            return;
        }

        setIsSavingBatchRestore(true);
        const saveMessageKey = 'batchSaveAiRestore';
        messageApi.loading({ content: '正在准备批量保存AI复原结果...', key: saveMessageKey, duration: 0 });

        setTimeout(async () => {
            try {
                let savedCount = 0;
                for (let i = 0; i < validResults.length; i++) {
                    const item = validResults[i];
                    if (!item.imageDataUrl) continue;

                    const nameWithoutExt = item.fileName.substring(0, item.fileName.lastIndexOf('.')) || item.fileName;
                    const saveFileName = `${nameWithoutExt}_restored_result.png`;

                    const link = document.createElement('a');
                    link.href = item.imageDataUrl;
                    link.download = saveFileName;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                    savedCount++;

                    if (i < validResults.length - 1) {
                         messageApi.loading({ content: `正在保存 ${i + 1}/${validResults.length}: ${saveFileName}`, key: saveMessageKey, duration: 0 });
                         await new Promise(resolve => setTimeout(resolve, 300));
                    }
                }
                 messageApi.success({ content: `成功保存了 ${savedCount} 个AI复原结果图像。`, key: saveMessageKey, duration: 3 });
            } catch (err) {
                console.error('批量保存AI复原结果时出错:', err);
                 messageApi.error({ content: '批量保存AI复原结果时发生错误。', key: saveMessageKey, duration: 3 });
            } finally {
                setIsSavingBatchRestore(false);
                setShowRestoreSaveConfirmModal(false);
            }
        }, 100);
    };

    const handleModelChange = (value: string) => {
        setSelectedModelValue(value);
    };

    const handleRefreshModels = () => {
        messageApi.loading({ content: '正在刷新模型列表...', key: 'refreshModels' });
        refetchAiRestoreModels().then(() => {
            messageApi.success({ content: '模型列表已刷新！', key: 'refreshModels' });
        }).catch(() => {
            messageApi.error({ content: '刷新模型列表失败。', key: 'refreshModels' });
        });
    };

    const overallLoading = (aiRestoreModelsLoading && !fetchedModelsData && !aiRestoreModelsError) || isInferring || isBatchProcessing;
    const loadingTip = isInferring ? "AI复原处理中..."
                       : isBatchProcessing ? "批量AI复原中..."
                       : (aiRestoreModelsLoading ? "加载模型列表中..."
                       : "请稍候...");

    const canInfer = !!currentImageInfo && !!selectedModelValue && !isInferring && !isBatchProcessing && !aiRestoreModelsLoading;
    const canBatchInfer = imageList && imageList.length > 1 && !!selectedModelValue && !isInferring && !isBatchProcessing && !aiRestoreModelsLoading && !autoInferenceEnabled;

    // 多图修复按钮的tooltip提示
    const multiImageTooltipTitle = !imageList || imageList.length <= 1
        ? "需要至少两张图片才能进行多图修复。请通过'打开文件夹'加载多张图片。"
        : autoInferenceEnabled
        ? "启用'切换时智能检测'时不能使用多图修复。请先关闭智能检测功能。"
        : "";

    return (
        <Spin spinning={overallLoading} tip={loadingTip}>
            {contextHolder}
            <Modal
                title="批量AI复原完成"
                open={showRestoreSaveConfirmModal}
                onCancel={() => setShowRestoreSaveConfirmModal(false)}
                footer={[
                    <Button key="close" onClick={() => setShowRestoreSaveConfirmModal(false)}>
                        关闭
                    </Button>,
                    <Button
                        key="saveAllRestored"
                        type="primary"
                        icon={<SaveOutlined />}
                        loading={isSavingBatchRestore}
                        onClick={handleSaveAllRestoredResults}
                        disabled={batchRestoreResults.resultItems.filter(item => item.imageDataUrl).length === 0}
                    >
                        保存所有修复结果图像
                    </Button>,
                ]}
            >
                <p>批量AI复原已完成！</p>
                <p>共处理 {batchRestoreResults.processedImages}/{batchRestoreResults.totalImages} 张图片。</p>
                {batchRestoreResults.resultItems.filter(item => item.imageDataUrl).length > 0 ?
                    <p>您可以点击下方按钮将所有成功处理并捕获的图像结果保存到本地。</p> :
                    <p>没有成功捕获到可供保存的图像结果。</p>
                }
                 <Alert
                    message="保存提示"
                    description="批量保存时，浏览器可能会依次提示下载多个文件，请确认您的浏览器允许多文件下载。"
                    type="info"
                    showIcon
                    style={{ marginTop: '10px' }}
                />
            </Modal>
            <div style={{ padding: '16px' }}>
                <Title level={4} style={{ textAlign: 'center', marginBottom: '16px' }}>AI图像复原</Title>

                {!currentImageInfo && !overallLoading && (
                    <Alert
                        message="请先在左侧图像工作区加载或选择一张图片后再进行检测。"
                        type="info"
                        showIcon
                        style={{ marginBottom: 16 }}
                    />
                )}

                {originalImageInfo && currentImageInfo?.name !== originalImageInfo.name && selectedDisplayMode === 'composite' && !isBatchProcessing && !isInferring &&(
                     <Alert
                        message="AI复原复合视图已激活"
                        description="当前显示的是处理后的合成图像。如需重新进行单图推理，面板将自动使用原图。"
                        type="info"
                        showIcon
                        style={{ marginBottom: '12px' }}
                    />
                )}

                <Form
                    form={form}
                    layout="horizontal"
                    labelCol={{ span: 8 }}
                    wrapperCol={{ span: 14 }}
                    labelAlign="left"
                >
                    {aiRestoreModelsError && !aiRestoreModelsLoading && (
                        <Form.Item style={{ marginBottom: '16px' }}>
                            <Alert
                                message="模型加载错误"
                                description={
                                    <div style={{ 
                                        display: 'flex', 
                                        alignItems: 'flex-start', 
                                        gap: '12px', 
                                        flexWrap: 'wrap',
                                        width: '100%'
                                    }}>
                                        <span style={{ 
                                            wordBreak: 'break-word', 
                                            flex: '1', 
                                            minWidth: '0',
                                            lineHeight: '1.5'
                                        }}>
                                            {aiRestoreModelsError}
                                        </span>
                                        <Button 
                                            size="small" 
                                            type="primary" 
                                            style={{ flexShrink: 0 }}
                                            onClick={handleRefreshModels}
                                        >
                                            重试
                                        </Button>
                                    </div>
                                }
                                type="error"
                                showIcon
                                closable
                                onClose={() => setAiRestoreModelsError(null)}
                            />
                        </Form.Item>
                    )}

                    <Form.Item
                        label="模型选择"
                        name="selected_model"
                        rules={[{ required: true, message: '请选择一个AI复原模型!' }]}
                    >
                        <Space.Compact style={{ width: '100%' }}>
                            <Select
                                placeholder={aiRestoreModelsLoading ? "加载中..." : "请选择模型"}
                                onChange={handleModelChange}
                                value={selectedModelValue}
                                style={{ width: '100%' }}
                                disabled={aiRestoreModelsLoading || availableAiRestoreModels.length === 0 || isInferring}
                            >
                                {availableAiRestoreModels.map(model => (
                                    <Option key={model.name} value={model.name}>
                                        {model.description || model.name}
                                    </Option>
                                ))}
                            </Select>
                            <Tooltip title="刷新模型列表">
                                <Button
                                    icon={<SyncOutlined />}
                                    onClick={handleRefreshModels}
                                    loading={aiRestoreModelsLoading && fetchedModelsData !== undefined}
                                    disabled={isInferring || (reactQueryModelsLoadingInitial && !fetchedModelsData)}
                                />
                            </Tooltip>
                        </Space.Compact>
                    </Form.Item>

                    <Form.Item
                        label="结果展示方式"
                        name="display_mode"
                        tooltip={
                            <div>
                                <div style={{ marginBottom: '16px' }}>
                                    <p><strong>合成视图:</strong></p>
                                    <p>左侧为原始图像，右侧为AI复原结果，便于对比。</p>
                                    <img src={compositeRestoreImage} alt="合成视图示例" style={{ maxWidth: '230px', marginTop: '8px', border: '1px solid #eee' }} />
                                </div>
                                <div>
                                    <p><strong>仅复原图:</strong></p>
                                    <p>只显示AI复原后的图像。</p>
                                    <img src={restoredOnlyImage} alt="仅复原图示例" style={{ maxWidth: '230px', marginTop: '8px', border: '1px solid #eee' }} />
                                </div>
                            </div>
                        }
                        rules={[{ required: true, message: '请选择结果展示方式!' }]}
                        initialValue="composite"
                    >
                        <Radio.Group
                            onChange={(e) => setSelectedDisplayMode(e.target.value as AiRestoreDisplayMode)}
                            value={selectedDisplayMode}
                            disabled={isInferring || isBatchProcessing || aiRestoreModelsLoading}
                            optionType="button"
                            buttonStyle="solid"
                        >
                            <Radio.Button value="composite">合成视图</Radio.Button>
                            <Radio.Button value="restored_only">仅复原图</Radio.Button>
                        </Radio.Group>
                    </Form.Item>

                    <Form.Item
                        label="多图推理间隔"
                        name="batch_processing_interval"
                        tooltip="多图修复时每张图片处理之间的等待时间（毫秒）"
                        rules={[{ required: true, message: '请设置多图处理间隔!' }]}
                    >
                        <Row gutter={8} align="middle">
                            <Col span={12}>
                                <Slider
                                    min={0}
                                    max={5000}
                                    step={100}
                                    onChange={(value: number) => setBatchProcessingInterval(value)}
                                    value={batchProcessingInterval}
                                    disabled={isInferring || isBatchProcessing || aiRestoreModelsLoading}
                                    marks={{ 1000: '1s', 3000: '3s', 5000: '5s' }}
                                    style={{ marginRight: '8px' }}
                                />
                            </Col>
                            <Col span={12}>
                                <InputNumber
                                    min={0}
                                    max={10000}
                                    step={100}
                                    style={{ width: '100%' }}
                                    onChange={(value: number | null) => { if (value !== null) setBatchProcessingInterval(value); }}
                                    value={batchProcessingInterval}
                                    disabled={isInferring || isBatchProcessing || aiRestoreModelsLoading}
                                    addonAfter="ms"
                                    size="small"
                                    controls={false}
                                />
                            </Col>
                        </Row>
                    </Form.Item>

                    <Divider style={{ margin: '12px 0' }} />

                    <Form.Item
                        label="切换时智能检测"
                        name="auto_inference"
                        valuePropName="checked"
                        tooltip="启用后，切换图片时自动执行AI复原，复原完成前不可继续切换"
                    >
                        <Switch
                            checked={autoInferenceEnabled}
                            onChange={(checked) => setAutoInferenceEnabled(checked)}
                            disabled={isInferring || isBatchProcessing || aiRestoreModelsLoading || availableAiRestoreModels.length === 0 || !currentImageInfo || !imageList || imageList.length <= 1}
                        />
                    </Form.Item>

                    <Form.Item wrapperCol={{ offset: 0, span: 24 }} style={{ marginTop: '24px' }}>
                        <Space style={{ width: '100%', justifyContent: 'center' }}>
                            <Button
                                type="primary"
                                onClick={handleSingleImageAiRestore}
                                disabled={!canInfer}
                            >
                                {originalImageInfo && currentImageInfo?.name !== originalImageInfo.name && !isBatchProcessing ? '重新修复原图' : '单图修复'}
                            </Button>
                            <Tooltip title={multiImageTooltipTitle}>
                                <span>
                                    <Button disabled={!canBatchInfer} onClick={handleMultiImageAiRestore}>
                                        多图修复
                                    </Button>
                                </span>
                            </Tooltip>
                        </Space>
                    </Form.Item>
                </Form>
            </div>
        </Spin>
    );
};

export default AiRestorePanel;