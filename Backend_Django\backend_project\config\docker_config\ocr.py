"""
Docker环境OCR配置

专门为Docker容器环境提供的OCR任务配置，使用Docker容器内的路径。
"""

from pathlib import Path
from typing import Dict, Any


def get_docker_ocr_configs(base_dir: Path) -> Dict[str, Any]:
    """
    获取Docker环境的OCR配置 - 更新路径为Docker环境
    
    Args:
        base_dir: Django项目的BASE_DIR路径（在Docker中通常是/app）
        
    Returns:
        Docker环境的OCR配置字典
    """
    return {
        'license_plate_cn': {
            'use_angle_cls': False,
            'det_algorithm': 'DB',
            'rec_algorithm': 'CRNN',
            'lang': 'ch',
            'use_gpu': False,
            'rec_image_shape': "3,64,320",
            'det_limit_type': "max",
            'det_limit_side_len': 320,
            'det_db_thresh': 0.01,
            'det_db_box_thresh': 0.01,
            'use_onnx': False,
            'use_npu': False,
            'use_mlu': False,
            'use_xpu': False,
            'return_word_box': False,
            'rec_char_dict_path': '/app/models/system_models/ocr/car_liencese_ch/AI_OCR_Rec_CHNLP_NCHW_1x3x64x320/ppocr_keys_v1.txt',
        },
        
        'id_card_en': {
            'use_angle_cls': False,
            'det_algorithm': 'DB',
            'rec_algorithm': 'CRNN',
            'lang': 'ch',
            'use_gpu': False,
            'rec_image_shape': "3,48,320",
            'det_limit_side_len': 640,
            'det_db_thresh': 0.3,
            'det_db_box_thresh': 0.6,
            'det_db_unclip_ratio': 1.5,
            'use_space_char': True,
            'rec_char_dict_path': '/app/models/system_models/ocr/Identity_card_number_en/Identity_card_rec_model/en_dict.txt',
            'use_onnx': False,
            'use_npu': False,
            'use_mlu': False,
            'use_xpu': False,
            'return_word_box': False,
        },
        
        'general_text_mobile_ch_en': {  # PP-OCRv4 mobile模型配置（中英文）- Docker环境
            'use_angle_cls': False,
            'det_algorithm': 'DB',
            'rec_algorithm': 'SVTR_LCNet',
            'lang': 'ch',
            'use_gpu': False,
            'rec_image_shape': "3,48,320",
            'det_limit_side_len': 640,
            'det_db_thresh': 0.3,
            'det_db_box_thresh': 0.6,
            'det_db_unclip_ratio': 1.5,
            'use_space_char': True,
            # PP-OCRv4 mobile使用标准中英文字典 - Docker路径
            'rec_char_dict_path': '/app/models/system_models/ocr/general_ocr_mobile_ch_en/PP-OCRv4_mobile_rec_inference_ch_en/ppocr_keys_v1.txt',
            'use_onnx': False,
            'use_npu': False,
            'use_mlu': False,
            'use_xpu': False,
            'return_word_box': False,
            # PP-OCRv4 mobile特有参数
            'det_db_score_mode': 'fast',
            'rec_batch_num': 6,
            'max_text_length': 25,
        },
        
        'general_text_mobile_en': {  # PP-OCRv4 mobile模型配置（纯英文）- Docker环境
            'use_angle_cls': False,
            'det_algorithm': 'DB',
            'rec_algorithm': 'SVTR_LCNet',
            'lang': 'en',
            'use_gpu': False,
            'rec_image_shape': "3,48,320",
            'det_limit_side_len': 640,
            'det_db_thresh': 0.3,
            'det_db_box_thresh': 0.6,
            'det_db_unclip_ratio': 1.5,
            'use_space_char': True,
            # PP-OCRv4 mobile使用英文字典 - Docker路径
            'rec_char_dict_path': '/app/models/system_models/ocr/general_ocr_mobile_en/PP-OCRv4_mobile_rec_inference_en/en_dict.txt',
            'use_onnx': False,
            'use_npu': False,
            'use_mlu': False,
            'use_xpu': False,
            'return_word_box': False,
            # PP-OCRv4 mobile特有参数
            'det_db_score_mode': 'fast',
            'rec_batch_num': 6,
            'max_text_length': 25,
        },
        
        'default': {
            'use_angle_cls': False,
            'det_algorithm': 'DB',
            'rec_algorithm': 'SVTR_LCNet',
            'lang': 'ch',
            'use_gpu': False,
            'rec_image_shape': "3,48,320",
            'det_limit_side_len': 960,
            'det_db_thresh': 0.3,
            'det_db_box_thresh': 0.6,
            'use_onnx': False,
            'use_npu': False,
            'use_mlu': False,
            'use_xpu': False,
            'return_word_box': False,
        }
    }
