#ifndef _AI_ENGINE_MNN_H
#define _AI_ENGINE_MNN_H

//-----------------------------------------------------------------------------
//  Includes

#include <dirent.h>

#include <new>
#include <memory>
#include <string>
#include <vector>
#include <unordered_map>
#include <unordered_set>

#include <unistd.h>

#include "MNN/Interpreter.hpp"
#include "AIEngineCommon.h"
#include "ModelLoader.hpp"
#include "Postprocess.hpp"
#include "Preprocess.hpp"
#include "NIU.hpp"
#include "autoconf.h"
//-----------------------------------------------------------------------------
//  Definitions

// 确保NO_ERROR不会被定义
#ifdef NO_ERROR
#undef NO_ERROR
#endif

// 自适应子位深度提取范围功能参考区域尺寸
#ifndef MACR_ADAP_SBER_REF_SIZE
#define MACR_ADAP_SBER_REF_SIZE                 (100)
#endif

// 离线模型存储路径
#ifndef MACR_OFFLINE_MODEL_PATH
#define MACR_OFFLINE_MODEL_PATH                 "./"
#endif
//-----------------------------------------------------------------------------
//  Declarations

namespace AIEngineMNN{
/**
 * @brief    
 *           ModelLoader(release版本)
 *           
 * @date     2024-02-29 Created by HuangJP
 */
class ModelLoader : public BaseModelLoader
{
public:
    /**
     * @brief    
     *           获取ModelLoader实例，该实例具备唯一性
     *           
     * @retval   指向ModelLoader实例的指针
     *           
     * @date     2024-02-29 Created by HuangJP
     */
    static ModelLoader *Instance(void)
    {
        static ModelLoader model_loader;
        return &model_loader;
    }

    /**
     * @brief    
     *           获取解释器
     *           
     * @param    model_tag:     模型标签
     *           
     * @retval   指向解释器的指针，获取失败时返回空指针
     *           
     * @date     2024-02-29 Created by HuangJP
     */
    std::shared_ptr<MNN::Interpreter> Get_Interpreter(std::string model_tag)
    {
        // 尝试获取解释器
        if (interpreter_map.find(model_tag) == interpreter_map.end())
        {
            // 获取失败时，尝试创建解释器
            const ModelData *model = Get_Model_Data(model_tag);  // 尝试获取模型数据
            if (model == nullptr)
            {
                // 获取模型数据失败，返回空指针
                return nullptr;
            }

            std::shared_ptr<MNN::Interpreter> net;

            // 判断模型文件是否嵌入到了ModelData中
            if (model->data_size == 0)
            {
                // 尝试获取模型文件
                std::string model_path(MACR_OFFLINE_MODEL_PATH); // 模型存放路径
                std::vector<std::string> model_list = this->_get_offline_model_list(model_path); // 获取离线模型列表
                std::string model_file;
                for (auto filename: model_list)
                {
                    // 遍历离线模型列表，找到目标模型文件
                    if (filename.find(model_tag) != std::string::npos)
                    {
                        // 找到模型文件，停止遍历离线模型列表，并记录模型文件路径
                        model_file.assign(model_path+filename);
                        break;
                    }
                }

                // 判断模型文件是否获取成功
                if (model_file.empty())
                {
                    LOGE("The corresponding model was not found in the specified path.");
                    LOGD("An attempt was made to locate model `%s` from `%s`, but it failed.", model_tag.c_str(), MACR_OFFLINE_MODEL_PATH);
                    return nullptr;
                }

                // 从文件创建解释器
                net = std::shared_ptr<MNN::Interpreter>(MNN::Interpreter::createFromFile(model_file.c_str()), MNN::Interpreter::destroy);
                if (net == nullptr)
                {
                    // 创建解释器失败，返回空指针
                    LOGE("Failed to create interpreter");
                    return nullptr;
                }
            }
            else
            {
                // 从内存创建解释器
                net = std::shared_ptr<MNN::Interpreter>(MNN::Interpreter::createFromBuffer(&model->data[0], model->data_size), MNN::Interpreter::destroy);
                if (net == nullptr)
                {
                    // 创建解释器失败，返回空指针
                    LOGE("Failed to create interpreter");
                    return nullptr;
                }
            }

            // 配置解释器
            net->setSessionMode(MNN::Interpreter::Session_Backend_Auto); // 自动选择后端
            net->setSessionHint(MNN::Interpreter::MAX_TUNING_NUMBER, 4); // GPU优化相关

            // 保存解释器
            interpreter_map[model_tag] = net;
        }

        return interpreter_map[model_tag];
    }

protected:
    // 模型标签(string)映射到解释器的数据类型
    typedef std::unordered_map<std::string, std::shared_ptr<MNN::Interpreter>> interpreter_map_t;
    interpreter_map_t interpreter_map;

    // 析构函数
    ~ModelLoader() {}
    // 构造函数
    ModelLoader(): BaseModelLoader() {}

private:
    /**
     * @brief    
     *           获取离线模型列表
     *           
     * @param    path:      模型存放路径
     *           
     * @retval   离线模型存放路径列表
     *           
     * @date     2024-07-29 Created by HuangJP
     */
    std::vector<std::string> _get_offline_model_list(std::string path)
    {
        std::vector<std::string> offline_models;
        DIR *dir; // 打开路径
        struct dirent *dr;

        // 打开模型存放路径
        dir = opendir(path.c_str());
        if (dir == nullptr)
        {
            return offline_models; // 打开目标路径失败，返回空的模型列表
        }

        // 逐个读目录下的文件
        while ((dr = readdir(dir)) != nullptr)
        {
            // 获取文件后缀
            std::string filename(dr->d_name);
            size_t pos = filename.find_last_of('.');
            if (pos == std::string::npos)
            {
                continue; // 获取后缀分隔符失败，跳过当前文件
            }

            // 判断当前文件是否为模型文件
            std::string suffix = filename.substr(pos+1);
            if (suffix != "mnn")
            {
                continue; // 当前文件不是模型文件，跳过当前文件
            }

            offline_models.emplace_back(filename); // 返回模型文件名
        }

        closedir(dir); // 关闭打开的路径

        return offline_models; // 返回离线模型列表
    }
};

/**
 * @brief    
 *           前处理模块
 *           
 * @date     2024-02-29 Created by HuangJP
 */
class Preprocess : public BasePreprocess
{
public:
    /**
     * @brief    
     *           前处理模块构造函数（生产函数）
     *           
     * @param    interpreter:   指向解释器的共享指针
     * @param    session:       从解释器创建的会话
     * @param    model_data:    模型数据
     *           
     * @retval   指向前处理模块对象的指针，构造失败时返回空指针
     *           
     * @date     2024-02-29 Created by HuangJP
     */
    static Preprocess *Construct(std::shared_ptr<MNN::Interpreter> interpreter, MNN::Session *session, const BaseModelLoader::ModelData *model_data)
    {
        // 检查传入参数是否合法
        if ((interpreter == nullptr)
            || (session == nullptr)
            || (model_data == nullptr))
        {
            LOGE("A null pointer have been detected");
            return nullptr;
        }

        // 构造前处理模块
        Preprocess *pre = new (std::nothrow)Preprocess(interpreter, session, model_data);

        // 判断构造是否成功
        if (pre == nullptr)
        {
            LOGE("Failed to allocate memory");
            return nullptr;
        }

        return pre;
    }

    /**
     * @brief    
     *           前处理模块销毁函数
     *           
     * @param    pre:       指向前处理模块的指针
     *           
     * @date     2024-02-29 Created by HuangJP
     */
    static void Destruct(Preprocess *pre)
    {
        if (pre != nullptr)
        {
            delete pre;
        }
    }

protected:
    /**
     * @brief    
     *           获取输入图片工具实例
     *           
     * @param    color_format:      输入图片颜色格式
     * @param    rsn:               右移位数
     * @param    memory_format:     内存存储格式
     *           
     * @retval   指向输入图片工具实例的指针
     *           
     * @date     2024-10-31 Created by HuangJP
     */
    InputImage *Get_Input_Image_Instance(bit_depth_t bit_depth, color_format_t color_format) override
    {
        return _Get_Input_Image_Instance<tensor_t, true>(bit_depth, color_format, this->_model_data->memory_format);
    }

    /**
     * @brief    
     *           前处理数据构造函数
     *           
     * @param    index:     输入张量索引
     * @param    data:      指向前处理数据的指针
     *           
     * @retval   错误码
     *           
     * @date     2024-02-29 Created by HuangJP
     */
    int Construct_Preprocessed_Data(void *index, PreprocessedData *data) override
    {
        // 判断传入参数是否合法
        if ((index == nullptr)
            || (data == nullptr))
        {
            LOGE("A null pointer have been detected");
            return AIENGINE_INVALID_PARAM;
        }

        tensor_index_t idx = (tensor_index_t)reinterpret_cast<intptr_t>(index);

        // 尝试根据索引获取输入张量
        if (input_tensor_map.find(idx) == input_tensor_map.end())
        {
            MNN::Tensor *input_tensor = _interpreter->getSessionInput(_session, idx);
            if (input_tensor == nullptr)
            {
                LOGE("Failed to get input tensor");
                return AIENGINE_GOT_NULLPTR;
            }

            // 转化输入张量格式
            std::shared_ptr<MNN::Tensor> nchw_input_tensor = std::shared_ptr<MNN::Tensor>(new (std::nothrow)MNN::Tensor(input_tensor, MNN::Tensor::CAFFE), MNN::Tensor::destroy);
            if (nchw_input_tensor == nullptr)
            {
                LOGE("Failed to allocate memory");
                return AIENGINE_OUT_OF_MEMORY;
            }

            input_tensor_map[idx] = mnn_tensor_pair(input_tensor, nchw_input_tensor);
        }

        // 获取输入张量信息
        mnn_tensor_pair mnn_tensor = input_tensor_map[idx];
        std::shared_ptr<MNN::Tensor> nchw_input_tensor = mnn_tensor.second;
        std::vector<int> input_shape = nchw_input_tensor->shape();
        if (this->_model_data->memory_format == BaseModelLoader::AIENGINE_MEMORY_FORMAT_NCHW)
        {
            data->resize_n = input_shape[0];
            data->resize_c = input_shape[1];
            data->resize_h = input_shape[2];
            data->resize_w = input_shape[3];
        }
        else if (this->_model_data->memory_format == BaseModelLoader::AIENGINE_MEMORY_FORMAT_NHWC)
        {
            data->resize_n = input_shape[0];
            data->resize_h = input_shape[1];
            data->resize_w = input_shape[2];
            data->resize_c = input_shape[3];
        }
        else
        {
            LOGE("Unsupported input tensor memory format.");
            return AIENGINE_INVALID_PARAM;
        }

        data->input_tensor_ptr = nchw_input_tensor->host<tensor_t>();

        return AIENGINE_NO_ERROR;
    }

    /**
     * @brief    
     *           同步输入张量数据到内存
     *           
     * @param    index:     输入张量索引
     *           
     * @retval   错误码
     *           
     * @date     2024-02-29 Created by HuangJP
     */
    int Input_Tensor_Flush_Memory(void *index) override
    {
        tensor_index_t idx = (tensor_index_t)reinterpret_cast<intptr_t>(index);

        // 尝试获取输入张量
        if (input_tensor_map.find(idx) == input_tensor_map.end())
        {
            LOGE("Unable to find input tensor through index");
            return AIENGINE_INVALID_PARAM;
        }

        // 将数据拷贝至输入张量中
        mnn_tensor_pair mnn_tensor = input_tensor_map[idx];
        MNN::Tensor *input_tensor = mnn_tensor.first;
        std::shared_ptr<MNN::Tensor> nchw_input_tensor = mnn_tensor.second;
        input_tensor->copyFromHostTensor(nchw_input_tensor.get());

        return AIENGINE_NO_ERROR;
    }

private:
    typedef const char * tensor_index_t;
    typedef float tensor_t;

    // first: NC4HW4格式的输入张量，second: NCHW格式的输入张量
    typedef std::pair<MNN::Tensor *, std::shared_ptr<MNN::Tensor>> mnn_tensor_pair;
    // 键：张量索引，值：(first: NC4HW4格式的输入张量，second: NCHW格式的输入张量)
    std::unordered_map<tensor_index_t, mnn_tensor_pair> input_tensor_map;

    const std::shared_ptr<MNN::Interpreter> _interpreter;
    const MNN::Session *_session;

    /**
     * @brief    
     *           前处理模块析构函数
     *           
     * @date     2024-07-30 Created by HuangJP
     */
    ~Preprocess()
    {

    }

    /**
     * @brief    
     *           前处理模块构造函数
     *           
     * @param    interpreter:   指向解释器的共享指针
     * @param    session:       从解释器创建的会话
     * @param    model_data:    模型数据
     *           
     * @date     2024-02-29 Created by HuangJP
     */
    Preprocess(std::shared_ptr<MNN::Interpreter> interpreter, MNN::Session *session, const BaseModelLoader::ModelData *model_data): 
        BasePreprocess(MACR_ADAP_SBER_REF_SIZE, model_data),
        _interpreter(interpreter), 
        _session(session)
    {
        
    }
};

/**
 * @brief    
 *           后处理模块
 *           
 * @date     2024-02-29 Created by HuangJP
 */
class Postprocess : public BasePostprocess
{
public:
    /**
     * @brief    
     *           后处理模块构造函数（生产函数）
     *           
     * @param    interpreter:   指向解释器的共享指针
     * @param    session:       从解释器创建的会话
     * @param    model_data:    模型数据
     *           
     * @retval   指向前处理模块对象的指针，构造失败时返回空指针
     *           
     * @date     2024-02-29 Created by HuangJP
     */
    static Postprocess *Construct(std::shared_ptr<MNN::Interpreter> interpreter, MNN::Session *session, const BaseModelLoader::ModelData *model_data)
    {
        // 检查传入参数是否合法
        if ((interpreter == nullptr)
            || (session == nullptr)
            || (model_data == nullptr))
        {
            LOGE("A null pointer has been detected");
            return nullptr;
        }

        // 构造前处理模块
        Postprocess *post = new (std::nothrow)Postprocess(interpreter, session, model_data);

        // 判断构造是否成功
        if (post == nullptr)
        {
            LOGE("Failed to allocate memory");
            return nullptr;
        }

        return post;
    }

    /**
     * @brief    
     *           后处理模块销毁函数
     *           
     * @param    post:  指向后处理模块的指针
     *           
     * @date     2024-02-29 Created by HuangJP
     */
    static void Destruct(Postprocess *post)
    {
        if (post != nullptr)
        {
            delete post;
        }
    }

private:
    typedef const char * tensor_index_t;

    // 输出张量数组数据格式
    typedef float _tensor_t;
    struct mnn_output_tensor: output_tensor_t {
        MNN::Tensor *output_tensor;                         // NC4HW4格式的输出张量
        std::shared_ptr<MNN::Tensor> nchw_output_tensor;    // NCHW格式的输出张量
    }; // MNN输出张量

    std::unordered_map<const char *, mnn_output_tensor> mnn_output_tensor_map;

    const std::shared_ptr<MNN::Interpreter> _interpreter;
    const MNN::Session *_session;

    /**
     * @brief    
     *           获取输出张量
     *           
     * @param    index:     输出张量索引
     *           
     * @retval   指向输出张量组的指针，获取失败时返回空指针
     *           
     * @date     2024-10-30 Created by HuangJP
     */
    output_tensor_t *Get_Output_Tensor(void *index) override
    {
        tensor_index_t idx = (tensor_index_t)reinterpret_cast<intptr_t>(index);

        // 尝试根据索引获取输入张量组
        if (mnn_output_tensor_map.find(idx) == mnn_output_tensor_map.end())
        {
            // 获取失败，尝试创建输出张量组
            MNN::Tensor *output_tensor = _interpreter->getSessionOutput(_session, idx);
            if (output_tensor == nullptr)
            {
                LOGE("Failed to get output tensor");
                return nullptr;
            }

            // 转化输入张量格式
            std::shared_ptr<MNN::Tensor> nchw_output_tensor = std::shared_ptr<MNN::Tensor>(new (std::nothrow)MNN::Tensor(output_tensor, MNN::Tensor::CAFFE), MNN::Tensor::destroy);
            if (nchw_output_tensor == nullptr)
            {
                LOGE("Failed to allocate memory");
                return nullptr;
            }

            mnn_output_tensor_map[idx] = mnn_output_tensor{};
            mnn_output_tensor_map[idx].data = nchw_output_tensor->host<_tensor_t>();
            for (auto num: nchw_output_tensor->shape()) mnn_output_tensor_map[idx].shape.emplace_back(num);
            mnn_output_tensor_map[idx].output_tensor = output_tensor; 
            mnn_output_tensor_map[idx].nchw_output_tensor = nchw_output_tensor;
        }

        // 拷贝输出张量
        mnn_output_tensor_map[idx].output_tensor->copyToHostTensor(mnn_output_tensor_map[idx].nchw_output_tensor.get());
        return &mnn_output_tensor_map[idx];
    }

    /**
     * @brief    
     *           后处理模块析构函数
     *           
     * @date     2024-07-31 Created by HuangJP
     */
    ~Postprocess()
    {

    }

    /**
     * @brief    
     *           后处理模块构造函数
     *           
     * @param    interpreter:   指向解释器的共享指针
     * @param    session:       从解释器创建的会话
     * @param    model_data:    模型数据
     *           
     * @date     2024-02-29 Created by HuangJP
     */
    Postprocess(std::shared_ptr<MNN::Interpreter> interpreter, MNN::Session *session, const BaseModelLoader::ModelData *model_data): 
        BasePostprocess(model_data),
        _interpreter(interpreter),
        _session(session)
    {

    }
};

/**
 * @brief    
 *           神经网络推理单元
 *           
 * @date     2024-02-29 Created by HuangJP
 */
class NIU : public BaseNIU
{
public:
    /**
     * @brief    
     *           NIU构造函数（生产函数）
     *           
     * @param    model_tag:     模型标签
     *           
     * @retval   指向NIU的指针，构造失败时，返回空指针
     *           
     * @date     2024-02-29 Created by HuangJP
     */
    static NIU *Construct(std::string model_tag)
    {
        // 构造NIU
        NIU *niu = new (std::nothrow)NIU(model_tag);
        if (niu == nullptr)
        {
            LOGE("Failed to allocate memory");
            return nullptr;
        }

        // 判断NIU是否未准备好
        if (niu->is_ready == false)
        {
            // NIU未准备好，释放申请的内存
            delete niu;
            niu = nullptr;
            return nullptr;
        }

        return niu;
    }

    /**
     * @brief    
     *           NIU销毁函数
     *           
     * @param    指向NIU的指针
     *           
     * @retval   错误码
     *           
     * @date     2024-02-29 Created by HuangJP
     */
    static void Destruct(NIU *niu)
    {
        if (niu != nullptr)
        {
            delete niu;
        }
    }

    /**
     * @brief    
     *           获取前处理模块接口
     *           
     * @retval   指向前处理模块的指针
     *           
     * @date     2024-03-01 Created by HuangJP
     */
    class Preprocess *Preprocess(void) override
    {
        return this->pre.get();
    }

    /**
     * @brief    
     *           获取后处理模块接口
     *           
     * @retval   指向后处理模块的指针
     *           
     * @date     2024-03-01 Created by HuangJP
     */
    class Postprocess *Postprocess(void) override
    {
        return this->post.get();
    }

    /**
     * @brief    
     *           推理接口
     *           
     * @retval   错误码
     *           
     * @date     2024-03-01 Created by HuangJP
     */
    int Inference(void) override
    {
        // 调用推理
        MNN::ErrorCode ret = interpreter->runSession(session);
        if (ret != MNN::NO_ERROR)
        {
            LOGE("Failed to run session");
            LOGD("Run session error code: %d", ret);
            return AIENGINE_INFERENCE_FAILED;
        }

        return AIENGINE_NO_ERROR;
    }

    /**
     * @brief    
     *           获取输入张量索引列表
     *           
     * @retval   指向输入张量索引列表的指针
     *           
     * @date     2024-03-04 Created by HuangJP
     */
    const std::vector<void *> *Get_Input_Tensor_Index_List(void) override
    {
        return &model->input_tensor_index_list;
    }

    /**
     * @brief    
     *           获取输出张量索引列表
     *           
     * @retval   指向输出张量索引列表的指针
     *           
     * @date     2024-03-04 Created by HuangJP
     */
    const std::vector<void *> *Get_Output_Tensor_Index_List(void) override
    {
        return &model->output_tensor_index_list;
    }

private:
    bool is_ready; // 是否准备好
    std::shared_ptr<MNN::Interpreter> interpreter; // 解释器
    const BaseModelLoader::ModelData *model; // 模型数据
    MNN::Session *session; // 会话
    std::shared_ptr<class Preprocess> pre; // 前处理模块
    std::shared_ptr<class Postprocess> post; // 后处理模块

    /**
     * @brief    
     *           神经网络推理单元析构函数
     *           
     * @date     2024-07-31 Created by HuangJP
     */
    ~NIU()
    {
        // 销毁会话
        if (interpreter != nullptr)
        {
            if (session != nullptr)
            {
                interpreter->releaseSession(session);
                session = nullptr;
            }
        }
    }

    /**
     * @brief    
     *           神经网络推理单元构造函数
     *           
     * @param    model_tag:     模型标签
     *           
     * @date     2024-07-31 Created by HuangJP
     */
    NIU(std::string model_tag)
    {
        // 变量初始化
        is_ready = false;
        interpreter = nullptr;
        model = nullptr;
        session = nullptr;
        pre = nullptr;
        post = nullptr;
        
        // 尝试获取解释器
        interpreter = ModelLoader::Instance()->Get_Interpreter(model_tag);
        if (interpreter == nullptr)
        {
            return; // 获取解释器失败，错误返回
        }

        // 尝试获取模型数据
        model = ModelLoader::Get_Model_Data(model_tag);
        if (model == nullptr)
        {
            return;
        }

        // 配置会话
        MNN::ScheduleConfig config;
        // 指定推理后端
        switch (model->backend)
        {
            // CPU推理
            case ModelLoader::AIENGINE_BACKEND_CPU:
                config.type = MNN_FORWARD_CPU;
                break;

            // GPU推理
            case ModelLoader::AIENGINE_BACKEND_GPU:
                config.type = MNN_FORWARD_OPENCL; // 基于OpenCL
                break;

            // NN推理
            case ModelLoader::AIENGINE_BACKEND_NN:
                config.type = MNN_FORWARD_NN;
                break;

            // 不支持的后端
            default:
                LOGD("Unsupported backend: %d, switch to CPU backend", model->backend);
                config.type = MNN_FORWARD_CPU; // 不支持的后端，默认使用CPU推理
                break;
        }
        // 指定推理线程数，仅CPU推理时有效
        config.numThread = model->num_threads;

        // 配置Backend
        MNN::BackendConfig backend_config;
        backend_config.memory = MNN::BackendConfig::Memory_High; // 使用较多内存
        // 指定推理精度
        switch (model->precision)
        {
            // 低精度
            case ModelLoader::AIENGINE_PRECISION_LOW:
                backend_config.precision = MNN::BackendConfig::Precision_Low; // 使用低精度推理
                break;

            // 低精度（BF16）
            case ModelLoader::AIENGINE_PRECISION_LOW_BF16:
                backend_config.precision = MNN::BackendConfig::Precision_Low_BF16; // 使用低精度推理，BF16优化
                break;

            // 普通精度
            case ModelLoader::AIENGINE_PRECISION_NORMAL:
                backend_config.precision = MNN::BackendConfig::Precision_Normal; // 使用普通精度推理
                break;

            // 高精度
            case ModelLoader::AIENGINE_PRECISION_HIGH:
                backend_config.precision = MNN::BackendConfig::Precision_High; // 使用高精度推理
                break;

            // 不支持的精度
            default:
                LOGD("Unsupported precision: %d, switch to normal precision.", model->precision);
                backend_config.precision = MNN::BackendConfig::Precision_Normal; // 使用普通精度推理
                break;
        }
        backend_config.power = MNN::BackendConfig::Power_High; // 使用高功率推理
        config.backendConfig = &backend_config;

        // 创建会话
        session = interpreter->createSession(config);
        if (session == nullptr)
        {
            LOGE("Failed to create session");
            return;
        }

        // 构造前处理模块
        pre = std::shared_ptr<class Preprocess>(
            Preprocess::Construct(interpreter, session, this->model), 
            Preprocess::Destruct);
        if (pre == nullptr)
        {
            LOGE("Failed to construct preprocess module");
            return;
        }

        // 构造后处理模块
        post = std::shared_ptr<class Postprocess>(
            Postprocess::Construct(interpreter, session, this->model), 
            Postprocess::Destruct);
        if (pre == nullptr)
        {
            LOGE("Failed to construct postprocess module");
            return;
        }

        // 通知NIU创建成功
        is_ready = true;
    }
};

} /* namespace AIEngineMNN */
#endif
//-----------------------------------------------------------------------------
//  End of file